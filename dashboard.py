#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
📊 Dashboard - لوحة المعلومات الرئيسية
Main Dashboard after login
"""

import pygame
import sys
import sqlite3
from datetime import datetime
from database import UserDatabase

# إعدادات الشاشة
SCREEN_WIDTH = 1400
SCREEN_HEIGHT = 900
BACKGROUND_COLOR = (245, 250, 255)  # لون أزرق فاتح جداً
PRIMARY_COLOR = (70, 130, 180)      # أزرق فولاذي
SECONDARY_COLOR = (255, 255, 255)   # أبيض
TEXT_COLOR = (25, 25, 112)          # أزرق داكن
ACCENT_COLOR = (100, 149, 237)      # أزرق كورن فلاور
SUCCESS_COLOR = (34, 139, 34)       # أخضر
WARNING_COLOR = (255, 165, 0)       # برتقالي
ERROR_COLOR = (220, 20, 60)         # أحمر
CARD_COLOR = (248, 250, 252)        # رمادي فاتح

class Dashboard:
    def __init__(self, user_data):
        """تهيئة لوحة المعلومات"""
        pygame.init()
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption(f"Dashboard - {user_data['full_name']}")
        self.clock = pygame.time.Clock()
        
        # بيانات المستخدم
        self.user_data = user_data
        
        # تهيئة قاعدة البيانات
        self.db = UserDatabase()
        
        # تهيئة الخطوط
        self.title_font = pygame.font.Font(None, 48)
        self.header_font = pygame.font.Font(None, 36)
        self.subheader_font = pygame.font.Font(None, 28)
        self.text_font = pygame.font.Font(None, 24)
        self.small_font = pygame.font.Font(None, 20)
        self.button_font = pygame.font.Font(None, 26)
        
        # متغيرات الواجهة
        self.stats = {}
        self.recent_projects = []
        self.system_info = {}
        
        # الأزرار
        self.buttons = {}
        self.create_buttons()
        
        # تحميل البيانات
        self.load_dashboard_data()
        
        # حالة التطبيق
        self.running = True
        self.next_action = None
    
    def create_buttons(self):
        """إنشاء الأزرار"""
        button_width = 200
        button_height = 60
        
        # الأزرار الرئيسية
        main_buttons = [
            ("new_project", "New Project", 100, 750, PRIMARY_COLOR),
            ("customer_info", "Customer Info", 320, 750, ACCENT_COLOR),
            ("view_projects", "View Projects", 540, 750, SUCCESS_COLOR),
        ]
        
        # أزرار إضافية للمدير الرئيسي
        if self.user_data.get('user_role') == 'super_admin':
            main_buttons.extend([
                ("admin_panel", "Admin Panel", 760, 750, ERROR_COLOR),
                ("system_settings", "System Settings", 980, 750, WARNING_COLOR),
            ])
        
        # زر تسجيل الخروج
        main_buttons.append(("logout", "Logout", SCREEN_WIDTH - 150, 50, TEXT_COLOR))
        
        for button_id, text, x, y, color in main_buttons:
            if button_id == "logout":
                width, height = 120, 40
            else:
                width, height = button_width, button_height
                
            self.buttons[button_id] = {
                'rect': pygame.Rect(x, y, width, height),
                'text': text,
                'color': color,
                'hover': False
            }
    
    def load_dashboard_data(self):
        """تحميل بيانات لوحة المعلومات"""
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()
            
            # إحصائيات عامة
            cursor.execute('SELECT COUNT(*) FROM users')
            total_users = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 1')
            active_users = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM user_projects')
            total_projects = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM user_projects WHERE user_id = ?', (self.user_data['id'],))
            user_projects = cursor.fetchone()[0]
            
            self.stats = {
                'total_users': total_users,
                'active_users': active_users,
                'total_projects': total_projects,
                'user_projects': user_projects
            }
            
            # المشاريع الحديثة للمستخدم
            cursor.execute('''
                SELECT project_name, created_at, updated_at
                FROM user_projects 
                WHERE user_id = ?
                ORDER BY updated_at DESC
                LIMIT 5
            ''', (self.user_data['id'],))
            
            self.recent_projects = cursor.fetchall()
            
            # معلومات النظام
            cursor.execute('SELECT username, last_login FROM users WHERE last_login IS NOT NULL ORDER BY last_login DESC LIMIT 1')
            last_login_user = cursor.fetchone()
            
            self.system_info = {
                'last_login_user': last_login_user[0] if last_login_user else 'None',
                'last_login_time': last_login_user[1] if last_login_user else 'Never',
                'current_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات لوحة المعلومات: {e}")
    
    def draw_header(self):
        """رسم رأس الصفحة"""
        # الخلفية العلوية
        header_rect = pygame.Rect(0, 0, SCREEN_WIDTH, 120)
        pygame.draw.rect(self.screen, PRIMARY_COLOR, header_rect)
        
        # العنوان الرئيسي
        title_text = f"Welcome, {self.user_data['full_name']}"
        title_surface = self.title_font.render(title_text, True, SECONDARY_COLOR)
        self.screen.blit(title_surface, (50, 30))
        
        # معلومات المستخدم
        user_info = f"Role: {self.user_data.get('user_role', 'user').title()} | Last Login: {self.user_data.get('last_login', 'First time')}"
        info_surface = self.text_font.render(user_info, True, SECONDARY_COLOR)
        self.screen.blit(info_surface, (50, 80))
        
        # الوقت الحالي
        current_time = datetime.now().strftime("%A, %B %d, %Y - %H:%M")
        time_surface = self.text_font.render(current_time, True, SECONDARY_COLOR)
        time_rect = time_surface.get_rect(right=SCREEN_WIDTH - 200, y=80)
        self.screen.blit(time_surface, time_rect)
    
    def draw_stats_cards(self):
        """رسم بطاقات الإحصائيات"""
        card_width = 280
        card_height = 120
        start_x = 50
        start_y = 150
        spacing = 20
        
        # بطاقات الإحصائيات
        stats_cards = [
            ("My Projects", self.stats['user_projects'], "📁", SUCCESS_COLOR),
            ("Total Projects", self.stats['total_projects'], "🏗️", ACCENT_COLOR),
            ("Active Users", self.stats['active_users'], "👥", PRIMARY_COLOR),
            ("Total Users", self.stats['total_users'], "👤", WARNING_COLOR)
        ]
        
        for i, (title, value, icon, color) in enumerate(stats_cards):
            x = start_x + i * (card_width + spacing)
            y = start_y
            
            # رسم البطاقة
            card_rect = pygame.Rect(x, y, card_width, card_height)
            pygame.draw.rect(self.screen, SECONDARY_COLOR, card_rect, border_radius=15)
            pygame.draw.rect(self.screen, color, card_rect, 3, border_radius=15)
            
            # الأيقونة
            icon_surface = self.header_font.render(icon, True, color)
            self.screen.blit(icon_surface, (x + 20, y + 20))
            
            # القيمة
            value_surface = self.header_font.render(str(value), True, TEXT_COLOR)
            value_rect = value_surface.get_rect(right=x + card_width - 20, y=y + 20)
            self.screen.blit(value_surface, value_rect)
            
            # العنوان
            title_surface = self.text_font.render(title, True, TEXT_COLOR)
            self.screen.blit(title_surface, (x + 20, y + 70))
    
    def draw_recent_projects(self):
        """رسم المشاريع الحديثة"""
        # إطار المشاريع الحديثة
        projects_x = 50
        projects_y = 300
        projects_width = 650
        projects_height = 400
        
        projects_rect = pygame.Rect(projects_x, projects_y, projects_width, projects_height)
        pygame.draw.rect(self.screen, SECONDARY_COLOR, projects_rect, border_radius=15)
        pygame.draw.rect(self.screen, PRIMARY_COLOR, projects_rect, 2, border_radius=15)
        
        # عنوان القسم
        header_surface = self.header_font.render("Recent Projects", True, PRIMARY_COLOR)
        self.screen.blit(header_surface, (projects_x + 20, projects_y + 20))
        
        # قائمة المشاريع
        if self.recent_projects:
            start_y = projects_y + 70
            for i, project in enumerate(self.recent_projects[:5]):
                project_name = project[0]
                created_date = project[1][:10] if project[1] else "Unknown"
                updated_date = project[2][:10] if project[2] else "Unknown"
                
                y_pos = start_y + i * 60
                
                # خلفية المشروع
                if i % 2 == 0:
                    project_bg = pygame.Rect(projects_x + 10, y_pos - 5, projects_width - 20, 50)
                    pygame.draw.rect(self.screen, CARD_COLOR, project_bg, border_radius=8)
                
                # اسم المشروع
                name_surface = self.text_font.render(f"📁 {project_name}", True, TEXT_COLOR)
                self.screen.blit(name_surface, (projects_x + 30, y_pos))
                
                # التواريخ
                dates_text = f"Created: {created_date} | Updated: {updated_date}"
                dates_surface = self.small_font.render(dates_text, True, TEXT_COLOR)
                self.screen.blit(dates_surface, (projects_x + 30, y_pos + 25))
        else:
            # لا توجد مشاريع
            no_projects_surface = self.text_font.render("No projects yet. Start your first project!", True, TEXT_COLOR)
            no_projects_rect = no_projects_surface.get_rect(center=(projects_x + projects_width // 2, projects_y + 200))
            self.screen.blit(no_projects_surface, no_projects_rect)
    
    def draw_system_info(self):
        """رسم معلومات النظام"""
        # إطار معلومات النظام
        info_x = 720
        info_y = 300
        info_width = 630
        info_height = 400
        
        info_rect = pygame.Rect(info_x, info_y, info_width, info_height)
        pygame.draw.rect(self.screen, SECONDARY_COLOR, info_rect, border_radius=15)
        pygame.draw.rect(self.screen, ACCENT_COLOR, info_rect, 2, border_radius=15)
        
        # عنوان القسم
        header_surface = self.header_font.render("System Information", True, ACCENT_COLOR)
        self.screen.blit(header_surface, (info_x + 20, info_y + 20))
        
        # معلومات النظام
        info_items = [
            ("🖥️ System Status", "Online and Running"),
            ("👤 Current User", self.user_data['username']),
            ("🔐 User Role", self.user_data.get('user_role', 'user').title()),
            ("📧 Email", self.user_data.get('email', 'Not provided')),
            ("📱 Phone", self.user_data.get('phone', 'Not provided')),
            ("🕐 Login Time", datetime.now().strftime("%H:%M:%S")),
            ("📊 Total Users", str(self.stats['total_users'])),
            ("🏗️ Total Projects", str(self.stats['total_projects'])),
        ]
        
        start_y = info_y + 70
        for i, (label, value) in enumerate(info_items):
            y_pos = start_y + i * 35
            
            # التسمية
            label_surface = self.text_font.render(label, True, TEXT_COLOR)
            self.screen.blit(label_surface, (info_x + 30, y_pos))
            
            # القيمة
            value_surface = self.text_font.render(str(value), True, ACCENT_COLOR)
            value_rect = value_surface.get_rect(right=info_x + info_width - 30, y=y_pos)
            self.screen.blit(value_surface, value_rect)
    
    def draw_buttons(self):
        """رسم الأزرار"""
        mouse_pos = pygame.mouse.get_pos()
        
        for button_id, button in self.buttons.items():
            # تحديث حالة التمرير
            button['hover'] = button['rect'].collidepoint(mouse_pos)
            
            # تحديد لون الزر
            if button['hover']:
                color = tuple(min(255, c + 30) for c in button['color'])
            else:
                color = button['color']
            
            # رسم الزر
            pygame.draw.rect(self.screen, color, button['rect'], border_radius=12)
            
            # إضافة ظل للأزرار الكبيرة
            if button_id != "logout":
                shadow_rect = button['rect'].copy()
                shadow_rect.x += 3
                shadow_rect.y += 3
                pygame.draw.rect(self.screen, (0, 0, 0, 50), shadow_rect, border_radius=12)
                pygame.draw.rect(self.screen, color, button['rect'], border_radius=12)
            
            # رسم النص
            text_surface = self.button_font.render(button['text'], True, SECONDARY_COLOR)
            text_rect = text_surface.get_rect(center=button['rect'].center)
            self.screen.blit(text_surface, text_rect)
    
    def handle_click(self, pos):
        """معالجة النقر"""
        for button_id, button in self.buttons.items():
            if button['rect'].collidepoint(pos):
                self.handle_button_click(button_id)
                break
    
    def handle_button_click(self, button_id):
        """معالجة النقر على الأزرار"""
        if button_id == "new_project":
            self.next_action = "new_project"
            self.running = False
        elif button_id == "customer_info":
            self.next_action = "customer_info"
            self.running = False
        elif button_id == "view_projects":
            self.next_action = "view_projects"
            self.running = False
        elif button_id == "admin_panel":
            if self.user_data.get('user_role') == 'super_admin':
                self.next_action = "admin_panel"
                self.running = False
        elif button_id == "system_settings":
            if self.user_data.get('user_role') == 'super_admin':
                self.next_action = "system_settings"
                self.running = False
        elif button_id == "logout":
            self.next_action = "logout"
            self.running = False
    
    def run(self):
        """تشغيل لوحة المعلومات"""
        while self.running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.next_action = "quit"
                    self.running = False
                
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # النقر بالزر الأيسر
                        self.handle_click(event.pos)
                
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        self.next_action = "logout"
                        self.running = False
                    elif event.key == pygame.K_F5:
                        self.load_dashboard_data()
            
            # رسم الشاشة
            self.screen.fill(BACKGROUND_COLOR)
            self.draw_header()
            self.draw_stats_cards()
            self.draw_recent_projects()
            self.draw_system_info()
            self.draw_buttons()
            
            pygame.display.flip()
            self.clock.tick(60)
        
        pygame.quit()
        return self.next_action

def show_dashboard(user_data):
    """عرض لوحة المعلومات"""
    dashboard = Dashboard(user_data)
    return dashboard.run()

if __name__ == "__main__":
    # اختبار لوحة المعلومات
    from database import UserDatabase
    
    db = UserDatabase()
    user_data = db.verify_user("admin", "admin123")
    
    if user_data:
        action = show_dashboard(user_data)
        print(f"الإجراء المحدد: {action}")
    else:
        print("فشل في تسجيل الدخول")
