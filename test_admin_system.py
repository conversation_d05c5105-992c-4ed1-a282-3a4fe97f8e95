#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 اختبار نظام إدارة المستخدمين
Test Admin System
"""

from database import UserDatabase

def test_super_admin():
    """اختبار المدير الرئيسي"""
    print("🔐 اختبار المدير الرئيسي...")
    
    db = UserDatabase()
    
    # اختبار تسجيل الدخول
    admin_user = db.verify_user("MohammedBushih<PERSON>", "Mfb112002*")
    
    if admin_user:
        print("✅ تسجيل دخول المدير الرئيسي ناجح")
        print(f"   الاسم: {admin_user['full_name']}")
        print(f"   اسم المستخدم: {admin_user['username']}")
        print(f"   الصلاحية: {admin_user['user_role']}")
        
        if admin_user['user_role'] == 'super_admin':
            print("🔐 صلاحيات المدير الرئيسي مفعلة")
            return True
        else:
            print("❌ صلاحيات المدير الرئيسي غير صحيحة")
            return False
    else:
        print("❌ فشل في تسجيل دخول المدير الرئيسي")
        return False

def test_regular_users():
    """اختبار المستخدمين العاديين"""
    print("\n👤 اختبار المستخدمين العاديين...")
    
    db = UserDatabase()
    
    # اختبار المستخدم الافتراضي
    admin_user = db.verify_user("admin", "admin123")
    
    if admin_user:
        print("✅ تسجيل دخول المستخدم العادي ناجح")
        print(f"   الاسم: {admin_user['full_name']}")
        print(f"   اسم المستخدم: {admin_user['username']}")
        print(f"   الصلاحية: {admin_user['user_role']}")
        
        if admin_user['user_role'] != 'super_admin':
            print("✅ المستخدم العادي لا يملك صلاحيات المدير الرئيسي")
            return True
        else:
            print("❌ المستخدم العادي يملك صلاحيات خاطئة")
            return False
    else:
        print("❌ فشل في تسجيل دخول المستخدم العادي")
        return False

def test_database_structure():
    """اختبار هيكل قاعدة البيانات"""
    print("\n🗄️ اختبار هيكل قاعدة البيانات...")
    
    import sqlite3
    
    try:
        conn = sqlite3.connect("users.db")
        cursor = conn.cursor()
        
        # فحص جدول المستخدمين
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        required_columns = ['id', 'username', 'password_hash', 'email', 'full_name', 'phone', 'user_role', 'created_at', 'last_login', 'is_active']
        
        missing_columns = [col for col in required_columns if col not in columns]
        
        if not missing_columns:
            print("✅ جدول المستخدمين يحتوي على جميع الأعمدة المطلوبة")
        else:
            print(f"❌ أعمدة مفقودة: {missing_columns}")
            return False
        
        # فحص أنواع المستخدمين
        cursor.execute('SELECT user_role, COUNT(*) FROM users GROUP BY user_role')
        roles = cursor.fetchall()
        
        print("📊 أنواع المستخدمين:")
        for role, count in roles:
            print(f"   {role or 'غير محدد'}: {count} مستخدم")
        
        # التحقق من وجود المدير الرئيسي
        cursor.execute('SELECT COUNT(*) FROM users WHERE username = "MohammedBushiha" AND user_role = "super_admin"')
        super_admin_count = cursor.fetchone()[0]
        
        if super_admin_count == 1:
            print("✅ المدير الرئيسي موجود ومعرف بشكل صحيح")
        else:
            print(f"❌ عدد المديرين الرئيسيين غير صحيح: {super_admin_count}")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def test_security_features():
    """اختبار المميزات الأمنية"""
    print("\n🛡️ اختبار المميزات الأمنية...")
    
    db = UserDatabase()
    
    # اختبار كلمة مرور خاطئة
    wrong_login = db.verify_user("MohammedBushiha", "wrong_password")
    if not wrong_login:
        print("✅ رفض كلمة المرور الخاطئة")
    else:
        print("❌ قبول كلمة مرور خاطئة!")
        return False
    
    # اختبار اسم مستخدم غير موجود
    nonexistent_user = db.verify_user("nonexistent_user", "any_password")
    if not nonexistent_user:
        print("✅ رفض المستخدم غير الموجود")
    else:
        print("❌ قبول مستخدم غير موجود!")
        return False
    
    # اختبار تشفير كلمة المرور
    password = "test123"
    hash1 = db.hash_password(password)
    hash2 = db.hash_password(password)
    
    if hash1 == hash2:
        print("✅ تشفير كلمة المرور يعمل بشكل صحيح")
    else:
        print("❌ تشفير كلمة المرور غير متسق")
        return False
    
    return True

def test_admin_panel_access():
    """اختبار الوصول للوحة الإدارة"""
    print("\n🖥️ اختبار الوصول للوحة الإدارة...")
    
    try:
        from admin_panel import AdminPanel
        from database import UserDatabase
        
        db = UserDatabase()
        
        # اختبار المدير الرئيسي
        super_admin = db.verify_user("MohammedBushiha", "Mfb112002*")
        if super_admin and super_admin['user_role'] == 'super_admin':
            print("✅ المدير الرئيسي يمكنه الوصول للوحة الإدارة")
        else:
            print("❌ المدير الرئيسي لا يمكنه الوصول للوحة الإدارة")
            return False
        
        # اختبار مستخدم عادي
        regular_user = db.verify_user("admin", "admin123")
        if regular_user and regular_user['user_role'] != 'super_admin':
            print("✅ المستخدم العادي لا يملك صلاحيات لوحة الإدارة")
        else:
            print("❌ المستخدم العادي يملك صلاحيات خاطئة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار لوحة الإدارة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار نظام إدارة المستخدمين المتقدم")
    print("=" * 60)
    
    tests = [
        ("اختبار المدير الرئيسي", test_super_admin),
        ("اختبار المستخدمين العاديين", test_regular_users),
        ("اختبار هيكل قاعدة البيانات", test_database_structure),
        ("اختبار المميزات الأمنية", test_security_features),
        ("اختبار الوصول للوحة الإدارة", test_admin_panel_access)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        print("\n🔐 معلومات تسجيل الدخول للمدير الرئيسي:")
        print("   اسم المستخدم: MohammedBushiha")
        print("   كلمة المرور: Mfb112002*")
        print("\n🚀 لتشغيل النظام: python main.py")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
