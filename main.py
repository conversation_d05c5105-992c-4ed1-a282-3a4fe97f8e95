import pygame
import math
import copy
import os
import json
import sqlite3
import tkinter as tk
from tkinter import filedialog
from datetime import datetime
from customer_info import start_customer_info
from view_3d import launch_3d_viewer
from login_page import show_login_page
from database import UserDatabase
import sys
import subprocess

# مكتبات لدعم النص العربي
ARABIC_SUPPORT = False
reshape = None
get_display = None

try:
    import arabic_reshaper
    from bidi.algorithm import get_display as bidi_get_display
    reshape = arabic_reshaper.reshape
    get_display = bidi_get_display
    ARABIC_SUPPORT = True
    print("✅ تم تحميل مكتبات دعم النص العربي بنجاح")
except ImportError as e:
    print(f"⚠️ مكتبات دعم النص العربي غير متوفرة: {e}")
    print("سيتم تثبيتها الآن...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "arabic-reshaper==3.0.0", "python-bidi==0.6.6"])
        import arabic_reshaper
        from bidi.algorithm import get_display as bidi_get_display
        reshape = arabic_reshaper.reshape
        get_display = bidi_get_display
        ARABIC_SUPPORT = True
        print("✅ تم تثبيت وتحميل مكتبات دعم النص العربي بنجاح")
    except Exception as install_error:
        print(f"❌ فشل في تثبيت مكتبات دعم النص العربي: {install_error}")
        ARABIC_SUPPORT = False

def fix_arabic_text(text):
    """إصلاح النص العربي للعرض الصحيح في pygame"""
    if not ARABIC_SUPPORT or not text:
        return text

    try:
        # تنظيف النص من المسافات الزائدة
        text = text.strip()

        # إعادة تشكيل النص العربي لربط الحروف
        reshaped_text = reshape(text)

        # تطبيق خوارزمية الاتجاه الثنائي (من اليمين إلى اليسار)
        display_text = get_display(reshaped_text)

        return display_text
    except Exception as e:
        print(f"خطأ في معالجة النص العربي '{text}': {e}")
        return text

# اختبار دعم النص العربي
if ARABIC_SUPPORT:
    test_text = "تعديل الجدار"
    fixed_text = fix_arabic_text(test_text)
    print(f"اختبار النص العربي: '{test_text}' -> '{fixed_text}'")
else:
    print("⚠️ تحذير: النص العربي قد لا يظهر بشكل صحيح")

# --- ثوابت وإعدادات Pygame ---
SCREEN_WIDTH = 1000
SCREEN_HEIGHT = 700
SIDEBAR_WIDTH = 150
DRAWING_AREA_WIDTH = SCREEN_WIDTH - SIDEBAR_WIDTH
INITIAL_PIXELS_PER_METER = 50
MIN_PIXELS_PER_METER = 10
MAX_PIXELS_PER_METER = 300
ZOOM_FACTOR_STEP = 1.1

# --- الألوان ---
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GRAY = (200, 200, 200)
BLUE = (0, 0, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
YELLOW = (255, 255, 0)
DARK_GRAY = (50, 50, 50)
DOOR_COLOR = (150, 75, 0)
WINDOW_COLOR = (100, 150, 255)
HIGHLIGHT_COLOR = (255, 165, 0)
INPUT_BOX_COLOR = (230, 230, 230)
INPUT_BOX_ACTIVE_COLOR = (255, 255, 200)
DELETE_COLOR = (200, 0, 0)
GRID_COLOR = (230, 230, 230)
RULER_COLOR = (0, 150, 150)
LIGHT_BLUE = (173, 216, 230)
DARK_BLUE = (0, 0, 139)

# --- متغيرات التراجع ---
history = []
MAX_HISTORY_SIZE = 20

# --- الأدوات وعناصر واجهة المستخدم ---
TOOLS = ["WALL", "DOOR", "WINDOW", "CHAIR", "BUGATTI", "DELETE", "MEASURE", "EDIT", "SAVE"]
TOOL_RECTS = []
INPUT_RECTS = {}

# --- قيم افتراضية لحقول الإدخال ---
DEFAULT_DOOR_WIDTH_M_STR = "0.9"
DEFAULT_WINDOW_WIDTH_M_STR = "1.2"
DEFAULT_CHAIR_SIZE_M_STR = "0.5"
DEFAULT_BUGATTI_SIZE_M_STR = "4.5"  # حجم السيارة بالمتر


# --- ألوان الكائنات الجديدة ---
CHAIR_COLOR = (139, 69, 19)  # بني
BUGATTI_COLOR = (0, 100, 200)  # أزرق سيارة

FURNITURE_COLOR = (101, 67, 33)  # بني داكن

# --- تعريف أنواع الأبواب واتجاهات الفتح ---
DOOR_TYPES = ["SINGLE", "DOUBLE", "SLIDING", "FOLDING"]
DOOR_SWING_DIRECTIONS = ["LEFT", "RIGHT"]
DEFAULT_DOOR_TYPE = "SINGLE"
DEFAULT_DOOR_SWING = "LEFT"

# --- دوال حفظ واستعادة المشاريع ---
def save_project_data(project_name=None, auto_save=False):
    """حفظ بيانات المشروع الحالي"""
    global current_user, user_db, elements, measurements

    if not current_user or not user_db:
        print("⚠️ لم يتم تسجيل الدخول - لا يمكن حفظ المشروع")
        return False

    try:
        # تجميع بيانات الجدران
        walls_data = []
        for elem in elements:
            if elem['type'] == 'wall':
                wall_data = {
                    'start_m': list(elem['start_m']),
                    'end_m': list(elem['end_m']),
                    'length_m': elem['length_m'],
                    'height_m': elem.get('height_m', 3.0)
                }
                walls_data.append(wall_data)

        # تجميع بيانات الكراسي
        chairs_data = []
        for elem in elements:
            if elem['type'] == 'chair':
                chair_data = {
                    'pos_m': list(elem['pos_m']),
                    'size_m': elem.get('size_m', 0.5)
                }
                chairs_data.append(chair_data)

        # تجميع بيانات الأبواب
        doors_data = []
        for elem in elements:
            if elem['type'] == 'door':
                door_data = {
                    'pos_m': list(elem['pos_m']),
                    'width_m': elem.get('width_m', 0.9),
                    'height_m': elem.get('height_m', 2.2),
                    'bottom_height_m': elem.get('bottom_height_m', 0.0),
                    'left_distance_m': elem.get('left_distance_m', 0.0),
                    'right_distance_m': elem.get('right_distance_m', 0.0)
                }
                doors_data.append(door_data)

        # تجميع بيانات النوافذ
        windows_data = []
        for elem in elements:
            if elem['type'] == 'window':
                window_data = {
                    'pos_m': list(elem['pos_m']),
                    'width_m': elem.get('width_m', 1.2),
                    'height_m': elem.get('height_m', 1.5),
                    'bottom_height_m': elem.get('bottom_height_m', 1.0),
                    'left_distance_m': elem.get('left_distance_m', 0.0),
                    'right_distance_m': elem.get('right_distance_m', 0.0)
                }
                windows_data.append(window_data)

        # تجميع بيانات القياسات
        measurements_data = []
        for measurement in measurements:
            measurement_data = {
                'point1': list(measurement['point1']),
                'point2': list(measurement['point2']),
                'distance': measurement['distance']
            }
            measurements_data.append(measurement_data)

        # إنشاء البيانات المجمعة
        project_data = {
            'walls': walls_data,
            'chairs': chairs_data,
            'doors': doors_data,
            'windows': windows_data,
            'measurements': measurements_data,
            'saved_at': datetime.now().isoformat(),
            'auto_save': auto_save
        }

        # تحديد اسم المشروع
        if not project_name:
            if auto_save:
                project_name = f"AutoSave_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            else:
                project_name = f"Project_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # حفظ في قاعدة البيانات
        success = user_db.save_user_project(
            user_id=current_user['id'],
            project_name=project_name,
            project_data=json.dumps(project_data, ensure_ascii=False)
        )

        if success:
            if auto_save:
                print(f"💾 تم الحفظ التلقائي: {project_name}")
            else:
                print(f"✅ تم حفظ المشروع: {project_name}")
            return True
        else:
            print("❌ فشل في حفظ المشروع")
            return False

    except Exception as e:
        print(f"❌ خطأ في حفظ المشروع: {e}")
        return False

def load_project_data(project_id):
    """تحميل بيانات المشروع"""
    global current_user, user_db, elements, measurements

    if not current_user or not user_db:
        print("⚠️ لم يتم تسجيل الدخول - لا يمكن تحميل المشروع")
        return False

    try:
        conn = sqlite3.connect(user_db.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT project_data FROM user_projects
            WHERE id = ? AND user_id = ?
        ''', (project_id, current_user['id']))

        result = cursor.fetchone()
        conn.close()

        if not result or not result[0]:
            print("❌ لم يتم العثور على بيانات المشروع")
            return False

        # تحليل البيانات
        project_data = json.loads(result[0])

        # مسح البيانات الحالية
        elements.clear()
        measurements.clear()

        # تحميل الجدران
        for wall_data in project_data.get('walls', []):
            wall = {
                
                'type': 'wall',
                'start_m': wall_data['start_m'],
                'end_m': wall_data['end_m'],
                'length_m': wall_data['length_m'],
                'height_m': wall_data.get('height_m', 3.0)
            }
            elements.append(wall)

        # تحميل الكراسي
        for chair_data in project_data.get('chairs', []):
            chair = {
                'type': 'chair',
                'pos_m': chair_data['pos_m'],
                'size_m': chair_data.get('size_m', 0.5)
            }
            elements.append(chair)

        # تحميل الأبواب
        for door_data in project_data.get('doors', []):
            door = {
                'type': 'door',
                'pos_m': door_data['pos_m'],
                'width_m': door_data.get('width_m', 0.9),
                'height_m': door_data.get('height_m', 2.2),
                'bottom_height_m': door_data.get('bottom_height_m', 0.0),
                'left_distance_m': door_data.get('left_distance_m', 0.0),
                'right_distance_m': door_data.get('right_distance_m', 0.0)
            }
            elements.append(door)

        # تحميل النوافذ
        for window_data in project_data.get('windows', []):
            window = {
                'type': 'window',
                'pos_m': window_data['pos_m'],
                'width_m': window_data.get('width_m', 1.2),
                'height_m': window_data.get('height_m', 1.5),
                'bottom_height_m': window_data.get('bottom_height_m', 1.0),
                'left_distance_m': window_data.get('left_distance_m', 0.0),
                'right_distance_m': window_data.get('right_distance_m', 0.0)
            }
            elements.append(window)

        # تحميل القياسات
        for measurement_data in project_data.get('measurements', []):
            measurement = {
                'point1': measurement_data['point1'],
                'point2': measurement_data['point2'],
                'distance': measurement_data['distance']
            }
            measurements.append(measurement)

        print(f"✅ تم تحميل المشروع بنجاح - العناصر: {len(elements)}, القياسات: {len(measurements)}")
        return True

    except Exception as e:
        print(f"❌ خطأ في تحميل المشروع: {e}")
        return False

def show_save_dialog():
    """عرض نافذة حوار للحفظ"""
    import tkinter as tk
    from tkinter import messagebox

    # إنشاء نافذة مخفية
    root = tk.Tk()
    root.withdraw()

    # عرض رسالة التأكيد
    result = messagebox.askyesnocancel(
        "حفظ المشروع",
        "هل تريد حفظ التغييرات في المشروع الحالي؟\n\nنعم: حفظ والخروج\nلا: خروج بدون حفظ\nإلغاء: العودة للمشروع",
        icon='question'
    )

    root.destroy()
    return result  # True = نعم، False = لا، None = إلغاء

# --- دالة بدء تطبيق الرسم ---
def start_drawing_app(user_data=None, project_id=None, project_name=None):
    # --- تهيئة المتغيرات العامة ---
    global current_user, user_db
    if user_data:
        current_user = user_data
        user_db = UserDatabase()

    # --- تهيئة Pygame ---
    pygame.init()
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))

    # تحديد عنوان النافذة حسب المشروع
    if project_name:
        pygame.display.set_caption(f"MEASURE - {project_name}")
    else:
        pygame.display.set_caption("MEASURE - مشروع جديد")

    clock = pygame.time.Clock()

    # --- تحميل المشروع إذا تم تحديده ---
    if project_id and user_data:
        print(f"📂 تحميل المشروع: {project_name} (ID: {project_id})")
        success = load_project_data(project_id)
        if success:
            print(f"✅ تم تحميل المشروع بنجاح: {project_name}")
        else:
            print(f"❌ فشل في تحميل المشروع: {project_name}")
            # يمكن عرض رسالة خطأ للمستخدم هنا

    # --- إعداد الخطوط العربية ---
    pygame.font.init()

    # محاولة تحميل خط عربي
    arabic_font = None
    arabic_small_font = None
    arabic_input_font = None

    # قائمة بالخطوط العربية المتاحة في Windows
    arabic_fonts = [
        "Arial Unicode MS",
        "Tahoma",
        "Microsoft Sans Serif",
        "Segoe UI",
        "Calibri",
        "Times New Roman"
    ]

    for font_name in arabic_fonts:
        try:
            arabic_font = pygame.font.SysFont(font_name, 24)
            arabic_small_font = pygame.font.SysFont(font_name, 18)
            arabic_input_font = pygame.font.SysFont(font_name, 20)
            print(f"✅ تم تحميل الخط العربي: {font_name}")
            break
        except:
            continue

    # إذا فشل تحميل خط عربي، استخدم الخط الافتراضي
    if not arabic_font:
        print("⚠️ لم يتم العثور على خط عربي، سيتم استخدام الخط الافتراضي")
        arabic_font = pygame.font.Font(None, 24)
        arabic_small_font = pygame.font.Font(None, 18)
        arabic_input_font = pygame.font.Font(None, 20)

    # الخطوط للنص الإنجليزي
    font = pygame.font.Font(None, 24)
    small_font = pygame.font.Font(None, 18)
    input_font = pygame.font.Font(None, 22)

    # --- تحميل الأيقونات ---
    ICON_SIZE = (40, 40)
    icons = {}
    def load_icon(filename):
        try:
            img = pygame.image.load(os.path.join(filename)).convert_alpha()
            return pygame.transform.scale(img, ICON_SIZE)
        except pygame.error as e:
            print(f"خطأ في تحميل الأيقونة {filename}: {e}")
            fallback_surface = pygame.Surface(ICON_SIZE, pygame.SRCALPHA)
            fallback_surface.fill((0,0,0,0))
            pygame.draw.rect(fallback_surface, RED, (0, 0, ICON_SIZE[0], ICON_SIZE[1]), 1)
            pygame.draw.line(fallback_surface, RED, (0,0), ICON_SIZE, 1)
            pygame.draw.line(fallback_surface, RED, (ICON_SIZE[0],0), (0, ICON_SIZE[1]), 1)
            return fallback_surface

    try:
        icons["WALL"] = load_icon("wall_icon.png")
        icons["DOOR"] = load_icon("door_icon.png")
        icons["WINDOW"] = load_icon("window_icon.png")
        icons["CHAIR"] = load_icon("chair_icon.png")

        icons["DELETE"] = load_icon("delete_icon.png")
        icons["MEASURE"] = load_icon("measure_icon.png")
        icons["SAVE"] = load_icon("save_icon.png")
    except Exception as e:
        print(f"حدث خطأ عام أثناء تحميل الأيقونات: {e}")

    # --- متغيرات الحالة ---
    global selected_element, show_edit_panel, edit_values, edit_active_input
    running = True
    current_tool = None
    drawing = False
    start_pos_m = None
    current_wall_direction = None  # لحفظ الاتجاه المطلوب للجدار الجديد
    elements = []
    measurements = []

    # --- متغيرات نافذة التعديل العامة ---
    selected_element = None
    show_edit_panel = False
    edit_values = {}
    edit_active_input = None

    # --- متغيرات التكبير/التصغير ---
    pixels_per_meter = INITIAL_PIXELS_PER_METER
    view_offset_x = 0
    view_offset_y = 0

    # --- متغيرات حقول الإدخال ---
    active_input = None
    input_values = {
        'door': DEFAULT_DOOR_WIDTH_M_STR,
        'window': DEFAULT_WINDOW_WIDTH_M_STR,
        'door_type': DEFAULT_DOOR_TYPE,
        'door_swing': DEFAULT_DOOR_SWING,
        'wall_height': "6.0",  # إضافة قيمة افتراضية أعلى لارتفاع الجدار
        'chair_size': DEFAULT_CHAIR_SIZE_M_STR,
        'bugatti_size': DEFAULT_BUGATTI_SIZE_M_STR
    }

    # --- متغيرات المسطرة ---
    ruler_point1_m = None
    ruler_point2_m = None
    ruler_snapped_point_m = None

    # --- متغيرات معاينة الأبواب والشبابيك ---
    preview_door_window = None  # معلومات معاينة الباب/النافذة
    preview_wall = None  # الجدار المعاين عليه

    # --- دوال التحويل ---
    def world_m_to_screen(world_m_pos):
        screen_x = round(world_m_pos[0] * pixels_per_meter + view_offset_x) + SIDEBAR_WIDTH
        screen_y = round(world_m_pos[1] * pixels_per_meter + view_offset_y)
        return (screen_x, screen_y)

    def screen_to_world_m(screen_pos):
        screen_x_adj = screen_pos[0]
        if screen_x_adj >= SIDEBAR_WIDTH:
            screen_x_adj -= SIDEBAR_WIDTH
        else:
            screen_x_adj = 0
        if pixels_per_meter == 0:
            return (0, 0)
        world_mx = (screen_x_adj - view_offset_x) / pixels_per_meter
        world_my = (screen_pos[1] - view_offset_y) / pixels_per_meter
        return (world_mx, world_my)

    # --- دوال مساعدة ---

    def draw_sidebar(selected_tool, current_input_values, active_input_field):
        sidebar_area = pygame.Rect(0, 0, SIDEBAR_WIDTH, SCREEN_HEIGHT)
        pygame.draw.rect(screen, GRAY, sidebar_area)

        TOOL_RECTS.clear()
        INPUT_RECTS.clear()


        y_offset = 20
        button_height = 55
        input_height = 25
        button_padding = 15

        scale_text = small_font.render(f"Scale: {pixels_per_meter:.1f} px/m", True, BLACK)
        screen.blit(scale_text, (10, SCREEN_HEIGHT - 60))
        zoom_instr_text = small_font.render("Wheel to zoom.", True, BLACK)
        screen.blit(zoom_instr_text, (10, SCREEN_HEIGHT - 40))
        ruler_instr_text = small_font.render("Select tool above.", True, BLACK)
        screen.blit(ruler_instr_text, (10, SCREEN_HEIGHT - 20))

        for tool in TOOLS:
            button_rect = pygame.Rect(10, y_offset, SIDEBAR_WIDTH - 20, button_height)
            TOOL_RECTS.append(button_rect)

            bg_color = BLUE
            if tool == selected_tool:
                bg_color = HIGHLIGHT_COLOR
            elif tool == "DELETE":
                bg_color = DELETE_COLOR
            elif tool == "MEASURE":
                 bg_color = RULER_COLOR
            elif tool == "SAVE":
                 bg_color = GREEN
            elif tool == "CHAIR":
                 bg_color = CHAIR_COLOR
            elif tool == "BUGATTI":
                 bg_color = BUGATTI_COLOR
            elif tool == "EDIT":
                 bg_color = YELLOW

            pygame.draw.rect(screen, bg_color, button_rect, border_radius=5)
            icon_surface = icons.get(tool)
            if icon_surface:
                icon_rect = icon_surface.get_rect(center=button_rect.center)
                screen.blit(icon_surface, icon_rect)
            else:
                tool_text = font.render(tool, True, WHITE)
                text_rect = tool_text.get_rect(center=button_rect.center)
                screen.blit(tool_text, text_rect)

            y_offset += button_height + button_padding

            if tool == "DOOR":
                # مربع إدخال عرض الباب
                input_rect = pygame.Rect(10, y_offset, SIDEBAR_WIDTH - 20, input_height)
                INPUT_RECTS['door'] = input_rect
                input_bg_color = INPUT_BOX_ACTIVE_COLOR if active_input_field == 'door' else INPUT_BOX_COLOR
                pygame.draw.rect(screen, input_bg_color, input_rect)
                pygame.draw.rect(screen, BLACK, input_rect, 1)
                input_text_surf = input_font.render("W: " + current_input_values['door'] + " m", True, BLACK)
                screen.blit(input_text_surf, (input_rect.x + 5, input_rect.y + 5))
                y_offset += input_height + 5

                # إزالة خيارات نوع الباب واتجاه الفتح

            elif tool == "WINDOW":
                input_rect = pygame.Rect(10, y_offset, SIDEBAR_WIDTH - 20, input_height)
                INPUT_RECTS['window'] = input_rect
                input_bg_color = INPUT_BOX_ACTIVE_COLOR if active_input_field == 'window' else INPUT_BOX_COLOR
                pygame.draw.rect(screen, input_bg_color, input_rect)
                pygame.draw.rect(screen, BLACK, input_rect, 1)
                input_text_surf = input_font.render("W: " + current_input_values['window'] + " m", True, BLACK)
                screen.blit(input_text_surf, (input_rect.x + 5, input_rect.y + 5))
                y_offset += input_height + 5

            elif tool == "CHAIR":
                input_rect = pygame.Rect(10, y_offset, SIDEBAR_WIDTH - 20, input_height)
                INPUT_RECTS['chair_size'] = input_rect
                input_bg_color = INPUT_BOX_ACTIVE_COLOR if active_input_field == 'chair_size' else INPUT_BOX_COLOR
                pygame.draw.rect(screen, input_bg_color, input_rect)
                pygame.draw.rect(screen, BLACK, input_rect, 1)
                input_text_surf = input_font.render("Size: " + current_input_values['chair_size'] + " m", True, BLACK)
                screen.blit(input_text_surf, (input_rect.x + 5, input_rect.y + 5))
                y_offset += input_height + 5

            elif tool == "BUGATTI":
                input_rect = pygame.Rect(10, y_offset, SIDEBAR_WIDTH - 20, input_height)
                INPUT_RECTS['bugatti_size'] = input_rect
                input_bg_color = INPUT_BOX_ACTIVE_COLOR if active_input_field == 'bugatti_size' else INPUT_BOX_COLOR
                pygame.draw.rect(screen, input_bg_color, input_rect)
                pygame.draw.rect(screen, BLACK, input_rect, 1)
                input_text_surf = input_font.render("Size: " + current_input_values['bugatti_size'] + " m", True, BLACK)
                screen.blit(input_text_surf, (input_rect.x + 5, input_rect.y + 5))
                y_offset += input_height + 5



        # إضافة زر العرض ثلاثي الأبعاد في نهاية الشريط الجانبي
        view_3d_btn_rect = pygame.Rect(10, SCREEN_HEIGHT - 60, SIDEBAR_WIDTH - 20, 40)
        pygame.draw.rect(screen, LIGHT_BLUE, view_3d_btn_rect, 0, 5)
        pygame.draw.rect(screen, DARK_BLUE, view_3d_btn_rect, 2, 5)
        view_3d_text = font.render("3D", True, DARK_BLUE)
        text_rect = view_3d_text.get_rect(center=view_3d_btn_rect.center)
        screen.blit(view_3d_text, text_rect)

        # إضافة حقل إدخال لارتفاع الجدار إذا كانت أداة الجدار محددة
        if selected_tool == "WALL":
            wall_height_label = small_font.render("Wall Height (m):", True, BLACK)
            screen.blit(wall_height_label, (10, y_offset + button_height + 10))

            wall_height_rect = pygame.Rect(10, y_offset + button_height + 30, SIDEBAR_WIDTH - 20, input_height)
            INPUT_RECTS['wall_height'] = wall_height_rect

            if active_input_field == 'wall_height':
                pygame.draw.rect(screen, INPUT_BOX_ACTIVE_COLOR, wall_height_rect)
            else:
                pygame.draw.rect(screen, INPUT_BOX_COLOR, wall_height_rect)

            pygame.draw.rect(screen, BLACK, wall_height_rect, 1)
            wall_height_text = input_font.render(current_input_values.get('wall_height', "6.0"), True, BLACK)
            screen.blit(wall_height_text, (wall_height_rect.x + 5, wall_height_rect.y + 5))

        return view_3d_btn_rect  # إرجاع مستطيل الزر للتحقق من النقر عليه

    def draw_edit_panel():
        """رسم نافذة تعديل خصائص العنصر المحدد"""
        if not show_edit_panel or not selected_element:
            return {}

        # إعدادات النافذة
        panel_width = 300
        panel_height = 500  # زيادة الارتفاع لاستيعاب الحقول الجديدة
        panel_x = SCREEN_WIDTH - panel_width - 20
        panel_y = 50

        # رسم خلفية النافذة
        panel_rect = pygame.Rect(panel_x, panel_y, panel_width, panel_height)
        pygame.draw.rect(screen, WHITE, panel_rect)
        pygame.draw.rect(screen, BLACK, panel_rect, 2)

        # عنوان النافذة حسب نوع العنصر
        element_type = selected_element.get('type', 'unknown')
        type_names = {
            'wall': 'Wall',
            'door': 'Door',
            'window': 'Window',
            'chair': 'Chair'
        }
        title_text = font.render(f"Edit {type_names.get(element_type, 'Element')}", True, BLACK)
        screen.blit(title_text, (panel_x + 10, panel_y + 10))

        input_rects = {}
        current_y = panel_y + 50

        # رسم حقول الإدخال حسب نوع العنصر
        if element_type == 'wall':
            current_y = draw_wall_edit_fields(panel_x, current_y, panel_width, input_rects)
        elif element_type == 'door':
            current_y = draw_door_edit_fields(panel_x, current_y, panel_width, input_rects)
        elif element_type == 'window':
            current_y = draw_window_edit_fields(panel_x, current_y, panel_width, input_rects)
        elif element_type == 'chair':
            current_y = draw_chair_edit_fields(panel_x, current_y, panel_width, input_rects)

        # أزرار الحفظ والإلغاء
        button_y = current_y + 20
        save_rect = pygame.Rect(panel_x + 10, button_y, 80, 30)
        cancel_rect = pygame.Rect(panel_x + 100, button_y, 80, 30)

        input_rects['save'] = save_rect
        input_rects['cancel'] = cancel_rect

        # زر الحفظ
        pygame.draw.rect(screen, GREEN, save_rect)
        pygame.draw.rect(screen, BLACK, save_rect, 1)
        save_text = small_font.render("Save", True, BLACK)
        save_text_rect = save_text.get_rect(center=save_rect.center)
        screen.blit(save_text, save_text_rect)

        # زر الإلغاء
        pygame.draw.rect(screen, RED, cancel_rect)
        pygame.draw.rect(screen, BLACK, cancel_rect, 1)
        cancel_text = small_font.render("Cancel", True, BLACK)
        cancel_text_rect = cancel_text.get_rect(center=cancel_rect.center)
        screen.blit(cancel_text, cancel_text_rect)

        return input_rects

    def draw_wall_edit_fields(panel_x, current_y, panel_width, input_rects):
        """رسم حقول تعديل الجدار"""
        # معلومات الجدار الحالي
        current_length = selected_element.get('length_m', 0.0)
        current_height = selected_element.get('height_m', 6.0)

        length_info = small_font.render(f"Current Length: {current_length:.2f} m", True, BLACK)
        screen.blit(length_info, (panel_x + 10, current_y))

        height_info = small_font.render(f"Current Height: {current_height:.2f} m", True, BLACK)
        screen.blit(height_info, (panel_x + 10, current_y + 20))

        current_y += 50

        # حقل الطول الجديد
        length_label = small_font.render("New Length (m):", True, BLACK)
        screen.blit(length_label, (panel_x + 10, current_y))

        length_rect = pygame.Rect(panel_x + 10, current_y + 20, panel_width - 20, 25)
        input_rects['length'] = length_rect

        bg_color = INPUT_BOX_ACTIVE_COLOR if edit_active_input == 'length' else INPUT_BOX_COLOR
        pygame.draw.rect(screen, bg_color, length_rect)
        pygame.draw.rect(screen, BLACK, length_rect, 1)

        length_text = input_font.render(edit_values.get('length', ''), True, BLACK)
        screen.blit(length_text, (length_rect.x + 5, length_rect.y + 5))

        current_y += 55

        # حقل الارتفاع الجديد
        height_label = small_font.render("New Height (m):", True, BLACK)
        screen.blit(height_label, (panel_x + 10, current_y))

        height_rect = pygame.Rect(panel_x + 10, current_y + 20, panel_width - 20, 25)
        input_rects['height'] = height_rect

        bg_color = INPUT_BOX_ACTIVE_COLOR if edit_active_input == 'height' else INPUT_BOX_COLOR
        pygame.draw.rect(screen, bg_color, height_rect)
        pygame.draw.rect(screen, BLACK, height_rect, 1)

        height_text = input_font.render(edit_values.get('height', ''), True, BLACK)
        screen.blit(height_text, (height_rect.x + 5, height_rect.y + 5))

        return current_y + 55

    def draw_door_edit_fields(panel_x, current_y, panel_width, input_rects):
        """رسم حقول تعديل الباب"""
        current_width = selected_element.get('width_m', 0.9)
        current_height = selected_element.get('height_m', 2.2)
        current_bottom = selected_element.get('bottom_height_m', 0.0)
        current_left = selected_element.get('left_distance_m', 0.0)
        current_right = selected_element.get('right_distance_m', 0.0)

        # عرض القيم الحالية
        info_text = [
            f"Width: {current_width:.2f}m",
            f"Height: {current_height:.2f}m",
            f"From Floor: {current_bottom:.2f}m",
            f"Left: {current_left:.2f}m, Right: {current_right:.2f}m"
        ]

        for i, text in enumerate(info_text):
            info_surface = small_font.render(text, True, BLACK)
            screen.blit(info_surface, (panel_x + 10, current_y + i * 18))

        current_y += len(info_text) * 18 + 10

        # حقول التعديل
        fields = [
            ('width', 'Width (m):'),
            ('height', 'Height (m):'),
            ('bottom_height', 'From Floor (m):'),
            ('left_distance', 'Left Distance (m):'),
            ('right_distance', 'Right Distance (m):')
        ]

        for field_key, field_label in fields:
            # تسمية الحقل
            label_surface = small_font.render(field_label, True, BLACK)
            screen.blit(label_surface, (panel_x + 10, current_y))

            # مستطيل الإدخال
            field_rect = pygame.Rect(panel_x + 10, current_y + 18, panel_width - 20, 22)
            input_rects[field_key] = field_rect

            # لون الخلفية حسب التحديد
            bg_color = INPUT_BOX_ACTIVE_COLOR if edit_active_input == field_key else INPUT_BOX_COLOR
            pygame.draw.rect(screen, bg_color, field_rect)
            pygame.draw.rect(screen, BLACK, field_rect, 1)

            # النص المدخل
            field_text = input_font.render(edit_values.get(field_key, ''), True, BLACK)
            screen.blit(field_text, (field_rect.x + 5, field_rect.y + 3))

            current_y += 45

        return current_y

    def draw_window_edit_fields(panel_x, current_y, panel_width, input_rects):
        """رسم حقول تعديل النافذة"""
        current_width = selected_element.get('width_m', 1.2)
        current_height = selected_element.get('height_m', 1.5)
        current_bottom = selected_element.get('bottom_height_m', 1.0)
        current_left = selected_element.get('left_distance_m', 0.0)
        current_right = selected_element.get('right_distance_m', 0.0)

        # عرض القيم الحالية
        info_text = [
            f"Width: {current_width:.2f}m",
            f"Height: {current_height:.2f}m",
            f"From Floor: {current_bottom:.2f}m",
            f"Left: {current_left:.2f}m, Right: {current_right:.2f}m"
        ]

        for i, text in enumerate(info_text):
            info_surface = small_font.render(text, True, BLACK)
            screen.blit(info_surface, (panel_x + 10, current_y + i * 18))

        current_y += len(info_text) * 18 + 10

        # حقول التعديل
        fields = [
            ('width', 'Width (m):'),
            ('height', 'Height (m):'),
            ('bottom_height', 'From Floor (m):'),
            ('left_distance', 'Left Distance (m):'),
            ('right_distance', 'Right Distance (m):')
        ]

        for field_key, field_label in fields:
            # تسمية الحقل
            label_surface = small_font.render(field_label, True, BLACK)
            screen.blit(label_surface, (panel_x + 10, current_y))

            # مستطيل الإدخال
            field_rect = pygame.Rect(panel_x + 10, current_y + 18, panel_width - 20, 22)
            input_rects[field_key] = field_rect

            # لون الخلفية حسب التحديد
            bg_color = INPUT_BOX_ACTIVE_COLOR if edit_active_input == field_key else INPUT_BOX_COLOR
            pygame.draw.rect(screen, bg_color, field_rect)
            pygame.draw.rect(screen, BLACK, field_rect, 1)

            # النص المدخل
            field_text = input_font.render(edit_values.get(field_key, ''), True, BLACK)
            screen.blit(field_text, (field_rect.x + 5, field_rect.y + 3))

            current_y += 45

        return current_y

    def draw_chair_edit_fields(panel_x, current_y, panel_width, input_rects):
        """رسم حقول تعديل الكرسي"""
        current_size = selected_element.get('size_m', 0.5)
        current_pos = selected_element.get('pos_m', [0, 0])

        size_info = small_font.render(f"Current Size: {current_size:.2f} m", True, BLACK)
        screen.blit(size_info, (panel_x + 10, current_y))

        pos_info = small_font.render(f"Position: ({current_pos[0]:.2f}, {current_pos[1]:.2f})", True, BLACK)
        screen.blit(pos_info, (panel_x + 10, current_y + 20))

        current_y += 50

        # حقل الحجم الجديد
        size_label = small_font.render("New Size (m):", True, BLACK)
        screen.blit(size_label, (panel_x + 10, current_y))

        size_rect = pygame.Rect(panel_x + 10, current_y + 20, panel_width - 20, 25)
        input_rects['size'] = size_rect

        bg_color = INPUT_BOX_ACTIVE_COLOR if edit_active_input == 'size' else INPUT_BOX_COLOR
        pygame.draw.rect(screen, bg_color, size_rect)
        pygame.draw.rect(screen, BLACK, size_rect, 1)

        size_text = input_font.render(edit_values.get('size', ''), True, BLACK)
        screen.blit(size_text, (size_rect.x + 5, size_rect.y + 5))

        return current_y + 55

    def apply_element_changes():
        """تطبيق التغييرات على العنصر المحدد"""
        if not selected_element:
            return False

        try:
            # حفظ الحالة الحالية في التاريخ
            current_state_copy = copy.deepcopy(elements)
            history.append(current_state_copy)
            if len(history) > MAX_HISTORY_SIZE:
                history.pop(0)

            element_type = selected_element.get('type', '')

            if element_type == 'wall':
                return apply_wall_changes()
            elif element_type == 'door':
                return apply_door_changes()
            elif element_type == 'window':
                return apply_window_changes()
            elif element_type == 'chair':
                return apply_chair_changes()

            return False

        except Exception as e:
            print(f"خطأ في تطبيق التغييرات: {e}")
            return False

    def apply_wall_changes():
        """تطبيق التغييرات على الجدار"""
        try:
            new_length = float(edit_values.get('length', '0'))
            new_height = float(edit_values.get('height', '6'))

            if new_length <= 0 or new_height <= 0:
                print("خطأ: الطول والارتفاع يجب أن يكونا موجبين")
                return False

            # تحديث ارتفاع الجدار
            selected_element['height_m'] = new_height

            # تحديث طول الجدار إذا تغير
            current_length = selected_element.get('length_m', 0.0)
            if abs(new_length - current_length) > 0.01:
                start_m = selected_element['start_m']
                end_m = selected_element['end_m']

                dx = end_m[0] - start_m[0]
                dy = end_m[1] - start_m[1]
                current_length = math.sqrt(dx*dx + dy*dy)

                if current_length > 0:
                    unit_x = dx / current_length
                    unit_y = dy / current_length

                    new_end_x = start_m[0] + unit_x * new_length
                    new_end_y = start_m[1] + unit_y * new_length

                    selected_element['end_m'] = [new_end_x, new_end_y]
                    selected_element['length_m'] = new_length

            print(f"✅ تم تحديث الجدار - الطول: {new_length:.2f}م، الارتفاع: {new_height:.2f}م")
            return True
        except ValueError:
            print("خطأ: قيم غير صالحة")
            return False

    def apply_door_changes():
        """تطبيق التغييرات على الباب"""
        try:
            # التحقق من جميع القيم
            new_width = float(edit_values.get('width', '0.9'))
            new_height = float(edit_values.get('height', '2.2'))
            new_bottom_height = float(edit_values.get('bottom_height', '0.0'))
            new_left_distance = float(edit_values.get('left_distance', '0.0'))
            new_right_distance = float(edit_values.get('right_distance', '0.0'))

            # التحقق من صحة القيم
            if new_width <= 0 or new_height <= 0:
                print("خطأ: العرض والارتفاع يجب أن يكونا موجبين")
                return False

            if new_bottom_height < 0:
                print("خطأ: المسافة من الأرض لا يمكن أن تكون سالبة")
                return False

            if new_left_distance < 0 or new_right_distance < 0:
                print("خطأ: المسافات لا يمكن أن تكون سالبة")
                return False

            # تطبيق التغييرات
            selected_element['width_m'] = new_width
            selected_element['height_m'] = new_height
            selected_element['bottom_height_m'] = new_bottom_height
            selected_element['left_distance_m'] = new_left_distance
            selected_element['right_distance_m'] = new_right_distance

            # إعادة حساب موضع الباب إذا تغيرت المسافات
            if selected_element.get('wall_ref'):
                update_door_window_position(selected_element, new_left_distance, new_width)

            print(f"✅ تم تحديث الباب - العرض: {new_width:.2f}م، الارتفاع: {new_height:.2f}م")
            return True
        except ValueError:
            print("خطأ: قيم غير صالحة")
            return False

    def apply_window_changes():
        """تطبيق التغييرات على النافذة"""
        try:
            # التحقق من جميع القيم
            new_width = float(edit_values.get('width', '1.2'))
            new_height = float(edit_values.get('height', '1.5'))
            new_bottom_height = float(edit_values.get('bottom_height', '1.0'))
            new_left_distance = float(edit_values.get('left_distance', '0.0'))
            new_right_distance = float(edit_values.get('right_distance', '0.0'))

            # التحقق من صحة القيم
            if new_width <= 0 or new_height <= 0:
                print("خطأ: العرض والارتفاع يجب أن يكونا موجبين")
                return False

            if new_bottom_height < 0:
                print("خطأ: المسافة من الأرض لا يمكن أن تكون سالبة")
                return False

            if new_left_distance < 0 or new_right_distance < 0:
                print("خطأ: المسافات لا يمكن أن تكون سالبة")
                return False

            # تطبيق التغييرات
            selected_element['width_m'] = new_width
            selected_element['height_m'] = new_height
            selected_element['bottom_height_m'] = new_bottom_height
            selected_element['left_distance_m'] = new_left_distance
            selected_element['right_distance_m'] = new_right_distance

            # إعادة حساب موضع النافذة إذا تغيرت المسافات
            if selected_element.get('wall_ref'):
                update_door_window_position(selected_element, new_left_distance, new_width)

            print(f"✅ تم تحديث النافذة - العرض: {new_width:.2f}م، الارتفاع: {new_height:.2f}م")
            return True
        except ValueError:
            print("خطأ: قيم غير صالحة")
            return False

    def update_door_window_position(element, left_distance, width):
        """تحديث موضع الباب/النافذة بناءً على المسافة اليسرى الجديدة"""
        try:
            wall_ref = element.get('wall_ref')
            if not wall_ref:
                return

            wall_start = wall_ref['start_m']
            wall_end = wall_ref['end_m']

            # حساب اتجاه الجدار
            wall_vec_x = wall_end[0] - wall_start[0]
            wall_vec_y = wall_end[1] - wall_start[1]
            wall_length = (wall_vec_x**2 + wall_vec_y**2)**0.5

            if wall_length == 0:
                return

            # تطبيع متجه الجدار
            unit_x = wall_vec_x / wall_length
            unit_y = wall_vec_y / wall_length

            # حساب الموضع الجديد
            distance_to_center = left_distance + width / 2
            new_x = wall_start[0] + unit_x * distance_to_center
            new_y = wall_start[1] + unit_y * distance_to_center

            # تحديث موضع العنصر
            element['pos_m'] = [new_x, new_y]

        except Exception as e:
            print(f"خطأ في تحديث موضع العنصر: {e}")

    def apply_chair_changes():
        """تطبيق التغييرات على الكرسي"""
        try:
            new_size = float(edit_values.get('size', '0.5'))
            if new_size <= 0:
                print("خطأ: الحجم يجب أن يكون موجباً")
                return False

            selected_element['size_m'] = new_size
            print(f"✅ تم تحديث الكرسي - الحجم: {new_size:.2f}م")
            return True
        except ValueError:
            print("خطأ: قيمة حجم غير صالحة")
            return False

    def close_edit_panel():
        """إغلاق نافذة التعديل"""
        global selected_element, show_edit_panel, edit_active_input
        selected_element = None
        show_edit_panel = False
        edit_active_input = None
        edit_values.clear()

    def open_edit_panel(element):
        """فتح نافذة تعديل العنصر"""
        global selected_element, show_edit_panel, edit_active_input
        selected_element = element
        show_edit_panel = True
        edit_active_input = None

        # تحديث القيم الافتراضية حسب نوع العنصر
        element_type = element.get('type', '')
        edit_values.clear()

        if element_type == 'wall':
            edit_values['length'] = f"{element.get('length_m', 0.0):.2f}"
            edit_values['height'] = f"{element.get('height_m', 6.0):.2f}"
        elif element_type == 'door':
            edit_values['width'] = f"{element.get('width_m', 0.9):.2f}"
            edit_values['height'] = f"{element.get('height_m', 2.2):.2f}"
            edit_values['bottom_height'] = f"{element.get('bottom_height_m', 0.0):.2f}"
            edit_values['left_distance'] = f"{element.get('left_distance_m', 0.0):.2f}"
            edit_values['right_distance'] = f"{element.get('right_distance_m', 0.0):.2f}"
        elif element_type == 'window':
            edit_values['width'] = f"{element.get('width_m', 1.2):.2f}"
            edit_values['height'] = f"{element.get('height_m', 1.5):.2f}"
            edit_values['bottom_height'] = f"{element.get('bottom_height_m', 1.0):.2f}"
            edit_values['left_distance'] = f"{element.get('left_distance_m', 0.0):.2f}"
            edit_values['right_distance'] = f"{element.get('right_distance_m', 0.0):.2f}"
        elif element_type == 'chair':
            edit_values['size'] = f"{element.get('size_m', 0.5):.2f}"

        print(f"📝 فتح نافذة تعديل {element_type}")

    def calculate_distance_m(p1_m, p2_m):
        if p1_m is None or p2_m is None: return 0.0
        return math.sqrt((p2_m[0] - p1_m[0])**2 + (p2_m[1] - p1_m[1])**2)

    def point_segment_distance_m(p_m, a_m, b_m):
        ax, ay = a_m
        bx, by = b_m
        px, py = p_m
        segment_len_sq = (bx - ax)**2 + (by - ay)**2
        if segment_len_sq < 1e-12:
            dist_m = calculate_distance_m(p_m, a_m)
            return dist_m, a_m
        u = (((px - ax) * (bx - ax)) + ((py - ay) * (by - ay))) / segment_len_sq
        if u < 0.0:
            closest_point_m = a_m
        elif u > 1.0:
            closest_point_m = b_m
        else:
            ix = ax + u * (bx - ax)
            iy = ay + u * (by - ay)
            closest_point_m = (ix, iy)
        dist_m = calculate_distance_m(p_m, closest_point_m)
        return dist_m, closest_point_m

    def find_closest_wall_m(point_m, walls_list):
        closest_wall = None
        min_dist_m = float('inf')
        tolerance_pixels = 10
        tolerance_m = tolerance_pixels / pixels_per_meter if pixels_per_meter > 0 else float('inf')
        for wall in walls_list:
            dist_m, _ = point_segment_distance_m(point_m, wall['start_m'], wall['end_m'])
            if dist_m < min_dist_m and dist_m < tolerance_m:
                min_dist_m = dist_m
                closest_wall = wall
        return closest_wall

    def find_closest_endpoint_m(point_m, elements_list, tolerance_m):
        closest_endpoint = None
        min_dist_sq = tolerance_m**2
        for elem in elements_list:
            if elem['type'] == 'wall':
                dist_sq_start = (point_m[0] - elem['start_m'][0])**2 + (point_m[1] - elem['start_m'][1])**2
                if dist_sq_start < min_dist_sq:
                    min_dist_sq = dist_sq_start
                    closest_endpoint = elem['start_m']
                dist_sq_end = (point_m[0] - elem['end_m'][0])**2 + (point_m[1] - elem['end_m'][1])**2
                if dist_sq_end < min_dist_sq:
                    min_dist_sq = dist_sq_end
                    closest_endpoint = elem['end_m']
        return closest_endpoint

    def find_element_at_pos_m(pos_m, elements_list):
        clicked_element = None
        min_dist_m = float('inf')
        tolerance_pixels = 10
        tolerance_m = tolerance_pixels / pixels_per_meter if pixels_per_meter > 0 else float('inf')

        for elem in reversed(elements_list):
            if elem['type'] == 'door' or elem['type'] == 'window':
                width_m = elem['width_m']
                click_thickness_m = tolerance_m * 2
                center_m = elem['pos_m']
                wall = elem.get('wall_ref')
                if wall:
                    dx_wall_m = wall['end_m'][0] - wall['start_m'][0]
                    dy_wall_m = wall['end_m'][1] - wall['start_m'][1]
                    is_horizontal = abs(dx_wall_m) > abs(dy_wall_m)
                    if is_horizontal:
                        half_w_m = width_m / 2
                        half_h_m = click_thickness_m / 2
                    else:
                        half_w_m = click_thickness_m / 2
                        half_h_m = width_m / 2
                    min_x_m = center_m[0] - half_w_m
                    max_x_m = center_m[0] + half_w_m
                    min_y_m = center_m[1] - half_h_m
                    max_y_m = center_m[1] + half_h_m
                    if min_x_m <= pos_m[0] <= max_x_m and min_y_m <= pos_m[1] <= max_y_m:
                        dist_m = calculate_distance_m(pos_m, center_m)
                        if dist_m < min_dist_m:
                            min_dist_m = dist_m
                            clicked_element = elem

            elif elem['type'] == 'chair':
                center_m = elem['pos_m']
                size_m = elem['size_m']
                half_size_m = size_m / 2
                min_x_m = center_m[0] - half_size_m
                max_x_m = center_m[0] + half_size_m
                min_y_m = center_m[1] - half_size_m
                max_y_m = center_m[1] + half_size_m
                if min_x_m <= pos_m[0] <= max_x_m and min_y_m <= pos_m[1] <= max_y_m:
                    dist_m = calculate_distance_m(pos_m, center_m)
                    if dist_m < min_dist_m:
                        min_dist_m = dist_m
                        clicked_element = elem

            elif elem['type'] == 'bugatti':
                center_m = elem['pos_m']
                size_m = elem['size_m']
                # السيارة لها شكل مستطيل (طول × عرض)
                half_length_m = size_m / 2
                half_width_m = (size_m * 0.45) / 2  # نفس النسبة المستخدمة في الرسم
                min_x_m = center_m[0] - half_length_m
                max_x_m = center_m[0] + half_length_m
                min_y_m = center_m[1] - half_width_m
                max_y_m = center_m[1] + half_width_m
                if min_x_m <= pos_m[0] <= max_x_m and min_y_m <= pos_m[1] <= max_y_m:
                    dist_m = calculate_distance_m(pos_m, center_m)
                    if dist_m < min_dist_m:
                        min_dist_m = dist_m
                        clicked_element = elem

        if clicked_element is None:
            for elem in elements_list:
                if elem['type'] == 'wall':
                    dist_m, _ = point_segment_distance_m(pos_m, elem['start_m'], elem['end_m'])
                    if dist_m < tolerance_m and dist_m < min_dist_m:
                         min_dist_m = dist_m
                         clicked_element = elem
        return clicked_element

    def draw_elements():
        for elem in elements:
            if elem['type'] == 'wall':
                start_screen = world_m_to_screen(elem['start_m'])
                end_screen = world_m_to_screen(elem['end_m'])
                if not (max(start_screen[0], end_screen[0]) < SIDEBAR_WIDTH or \
                        min(start_screen[0], end_screen[0]) > SCREEN_WIDTH or \
                        max(start_screen[1], end_screen[1]) < 0 or \
                        min(start_screen[1], end_screen[1]) > SCREEN_HEIGHT):
                    pygame.draw.line(screen, DARK_GRAY, start_screen, end_screen, 3)
                    mid_x_screen = (start_screen[0] + end_screen[0]) // 2
                    mid_y_screen = (start_screen[1] + end_screen[1]) // 2
                    length_m = elem['length_m']
                    dim_text = small_font.render(f"{length_m:.2f}m", True, BLACK)
                    dx_screen = end_screen[0] - start_screen[0]
                    dy_screen = end_screen[1] - start_screen[1]
                    if abs(dx_screen) < 0.01:
                         angle = math.pi / 2 if dy_screen > 0 else -math.pi / 2
                    else:
                         angle = math.atan2(dy_screen, dx_screen)
                    text_offset = 10
                    text_x = mid_x_screen + math.sin(angle) * text_offset
                    text_y = mid_y_screen - math.cos(angle) * text_offset
                    text_rect = dim_text.get_rect(center=(int(text_x), int(text_y)))
                    if text_rect.right > SIDEBAR_WIDTH and text_rect.bottom > 0 and \
                       text_rect.left < SCREEN_WIDTH and text_rect.top < SCREEN_HEIGHT:
                        bg_rect = text_rect.inflate(4, 2)
                        pygame.draw.rect(screen, WHITE, bg_rect)
                        screen.blit(dim_text, text_rect)

            elif elem['type'] == 'door' or elem['type'] == 'window':
                wall = elem.get('wall_ref')
                if not wall or wall not in elements:
                    continue

                width_m = elem['width_m']
                center_m = elem['pos_m']
                center_screen = world_m_to_screen(center_m)

                if not (center_screen[0] < SIDEBAR_WIDTH or center_screen[0] > SCREEN_WIDTH or \
                        center_screen[1] < 0 or center_screen[1] > SCREEN_HEIGHT):
                    width_px = width_m * pixels_per_meter
                    height_px_base = 0.1 * pixels_per_meter
                    visual_height_factor = 5
                    height_px = max(3, height_px_base * visual_height_factor)

                    dx_wall_m = wall['end_m'][0] - wall['start_m'][0]
                    dy_wall_m = wall['end_m'][1] - wall['start_m'][1]
                    is_horizontal = abs(dx_wall_m) > abs(dy_wall_m)

                    if is_horizontal:
                        rect_w = width_px
                        rect_h = height_px
                    else:
                        rect_w = height_px
                        rect_h = width_px

                    item_rect_screen = pygame.Rect(
                        center_screen[0] - rect_w / 2,
                        center_screen[1] - rect_h / 2,
                        rect_w, rect_h)

                    if elem['type'] == 'door':
                        # رسم الباب كتجويف مع إطار أحمر
                        draw_door_opening(screen, item_rect_screen, is_horizontal)
                        # رسم المسافات الثابتة
                        draw_permanent_distance_labels(screen, elem, item_rect_screen, is_horizontal)
                    else:  # window
                        # رسم النافذة كتجويف مع إطار أزرق
                        draw_window_opening(screen, item_rect_screen, is_horizontal)
                        # رسم المسافات الثابتة
                        draw_permanent_distance_labels(screen, elem, item_rect_screen, is_horizontal)

                    # رسم النص كما هو
                    dim_text = small_font.render(f"{elem['width_m']:.2f}m", True, BLACK)
                    text_rect = dim_text.get_rect(center=item_rect_screen.center)
                    text_rect.y += item_rect_screen.height // 2 + 5
                    if text_rect.right > SIDEBAR_WIDTH and text_rect.bottom > 0 and \
                       text_rect.left < SCREEN_WIDTH and text_rect.top < SCREEN_HEIGHT:
                        bg_rect = text_rect.inflate(4, 2)
                        pygame.draw.rect(screen, WHITE, bg_rect)
                        screen.blit(dim_text, text_rect)

            elif elem['type'] == 'chair':
                center_m = elem['pos_m']
                center_screen = world_m_to_screen(center_m)
                size_m = elem['size_m']

                if not (center_screen[0] < SIDEBAR_WIDTH or center_screen[0] > SCREEN_WIDTH or \
                        center_screen[1] < 0 or center_screen[1] > SCREEN_HEIGHT):
                    size_px = size_m * pixels_per_meter

                    # رسم الكرسي كمربع بني
                    chair_rect = pygame.Rect(
                        center_screen[0] - size_px / 2,
                        center_screen[1] - size_px / 2,
                        size_px, size_px)

                    pygame.draw.rect(screen, CHAIR_COLOR, chair_rect)
                    pygame.draw.rect(screen, BLACK, chair_rect, 2)

                    # رسم رمز الكرسي (خطوط للمقعد والظهر)
                    seat_rect = pygame.Rect(
                        chair_rect.x + size_px * 0.1,
                        chair_rect.y + size_px * 0.4,
                        size_px * 0.8, size_px * 0.4)
                    pygame.draw.rect(screen, BLACK, seat_rect, 1)

                    # ظهر الكرسي
                    back_rect = pygame.Rect(
                        chair_rect.x + size_px * 0.1,
                        chair_rect.y + size_px * 0.1,
                        size_px * 0.8, size_px * 0.2)
                    pygame.draw.rect(screen, BLACK, back_rect, 1)

                    # رسم النص
                    dim_text = small_font.render(f"Chair {size_m:.1f}m", True, BLACK)
                    text_rect = dim_text.get_rect(center=chair_rect.center)
                    text_rect.y += chair_rect.height // 2 + 5
                    if text_rect.right > SIDEBAR_WIDTH and text_rect.bottom > 0 and \
                       text_rect.left < SCREEN_WIDTH and text_rect.top < SCREEN_HEIGHT:
                        bg_rect = text_rect.inflate(4, 2)
                        pygame.draw.rect(screen, WHITE, bg_rect)
                        screen.blit(dim_text, text_rect)

            elif elem['type'] == 'bugatti':
                center_m = elem['pos_m']
                center_screen = world_m_to_screen(center_m)
                size_m = elem['size_m']

                if not (center_screen[0] < SIDEBAR_WIDTH or center_screen[0] > SCREEN_WIDTH or \
                        center_screen[1] < 0 or center_screen[1] > SCREEN_HEIGHT):
                    # حساب أبعاد السيارة (نسبة 2:1 تقريباً)
                    length_px = size_m * pixels_per_meter
                    width_px = length_px * 0.45  # عرض أقل من الطول

                    # رسم السيارة كمستطيل أزرق
                    car_rect = pygame.Rect(
                        center_screen[0] - length_px / 2,
                        center_screen[1] - width_px / 2,
                        length_px, width_px)

                    pygame.draw.rect(screen, BUGATTI_COLOR, car_rect)
                    pygame.draw.rect(screen, BLACK, car_rect, 2)

                    # رسم تفاصيل السيارة
                    # النوافذ (مستطيلات صغيرة)
                    window_margin = length_px * 0.15
                    window_rect = pygame.Rect(
                        car_rect.x + window_margin,
                        car_rect.y + width_px * 0.2,
                        length_px * 0.7, width_px * 0.6)
                    pygame.draw.rect(screen, (200, 220, 255), window_rect)
                    pygame.draw.rect(screen, BLACK, window_rect, 1)

                    # العجلات (دوائر صغيرة)
                    wheel_radius = min(width_px * 0.15, 8)
                    wheel_y_offset = width_px * 0.35
                    wheel_x_positions = [
                        car_rect.x + length_px * 0.2,  # العجلة الأمامية
                        car_rect.x + length_px * 0.8   # العجلة الخلفية
                    ]

                    for wheel_x in wheel_x_positions:
                        # العجلة العلوية
                        pygame.draw.circle(screen, BLACK,
                                         (int(wheel_x), int(center_screen[1] - wheel_y_offset)),
                                         int(wheel_radius))
                        # العجلة السفلية
                        pygame.draw.circle(screen, BLACK,
                                         (int(wheel_x), int(center_screen[1] + wheel_y_offset)),
                                         int(wheel_radius))

                    # رسم النص
                    dim_text = small_font.render(f"Bugatti {size_m:.1f}m", True, BLACK)
                    text_rect = dim_text.get_rect(center=car_rect.center)
                    text_rect.y += car_rect.height // 2 + 5
                    if text_rect.right > SIDEBAR_WIDTH and text_rect.bottom > 0 and \
                       text_rect.left < SCREEN_WIDTH and text_rect.top < SCREEN_HEIGHT:
                        bg_rect = text_rect.inflate(4, 2)
                        pygame.draw.rect(screen, WHITE, bg_rect)
                        screen.blit(dim_text, text_rect)

    def draw_grid():
        spacing_m = 1.0
        line_spacing_pixels = spacing_m * pixels_per_meter
        if line_spacing_pixels < 5: return

        world_top_left_m = screen_to_world_m((SIDEBAR_WIDTH, 0))
        world_bottom_right_m = screen_to_world_m((SCREEN_WIDTH, SCREEN_HEIGHT))

        start_mx = math.floor(world_top_left_m[0] / spacing_m) * spacing_m
        end_mx = math.ceil(world_bottom_right_m[0] / spacing_m) * spacing_m
        m_x = start_mx
        while m_x <= end_mx:
             screen_x, _ = world_m_to_screen((m_x, world_top_left_m[1]))
             if screen_x >= SIDEBAR_WIDTH:
                 pygame.draw.line(screen, GRID_COLOR, (screen_x, 0), (screen_x, SCREEN_HEIGHT))
             m_x += spacing_m

        start_my = math.floor(world_top_left_m[1] / spacing_m) * spacing_m
        end_my = math.ceil(world_bottom_right_m[1] / spacing_m) * spacing_m
        m_y = start_my
        while m_y <= end_my:
            _, screen_y = world_m_to_screen((world_top_left_m[0], m_y))
            pygame.draw.line(screen, GRID_COLOR, (SIDEBAR_WIDTH, screen_y), (SCREEN_WIDTH, screen_y))
            m_y += spacing_m

    def snap_to_orthogonal_m(start_point_m, current_point_m):
        dx_m = current_point_m[0] - start_point_m[0]
        dy_m = current_point_m[1] - start_point_m[1]
        if abs(dx_m) > abs(dy_m):
            snapped_point_m = (current_point_m[0], start_point_m[1])
        else:
            snapped_point_m = (start_point_m[0], current_point_m[1])
        return snapped_point_m

    def find_wall_endpoint_near(pos_m, elements, tolerance_m=0.3):
        """البحث عن نهاية جدار قريبة من النقطة المحددة"""
        for elem in elements:
            if elem['type'] == 'wall':
                start_pos = elem['start_m']
                end_pos = elem['end_m']

                # فحص المسافة من نقطة البداية
                dist_to_start = calculate_distance_m(pos_m, start_pos)
                if dist_to_start <= tolerance_m:
                    return start_pos, elem

                # فحص المسافة من نقطة النهاية
                dist_to_end = calculate_distance_m(pos_m, end_pos)
                if dist_to_end <= tolerance_m:
                    return end_pos, elem

        return None, None

    def get_perpendicular_direction(wall_elem):
        """الحصول على الاتجاه العمودي للجدار"""
        start_pos = wall_elem['start_m']
        end_pos = wall_elem['end_m']

        dx = end_pos[0] - start_pos[0]
        dy = end_pos[1] - start_pos[1]

        # إذا كان الجدار أفقي، الاتجاه العمودي هو عمودي
        if abs(dx) > abs(dy):
            return 'vertical'  # الجدار أفقي، الاتجاه الجديد عمودي
        else:
            return 'horizontal'  # الجدار عمودي، الاتجاه الجديد أفقي

    def snap_to_perpendicular_direction(start_pos, current_pos, direction):
        """محاذاة النقطة حسب الاتجاه المحدد"""
        if direction == 'horizontal':
            # أفقي - تثبيت Y
            return (current_pos[0], start_pos[1])
        else:  # vertical
            # عمودي - تثبيت X
            return (start_pos[0], current_pos[1])

    def draw_ruler(screen, p1_m, p2_m, current_mouse_m, snapped_indicator_m):
        if p1_m is None:
            if snapped_indicator_m:
                snap_screen = world_m_to_screen(snapped_indicator_m)
                pygame.draw.circle(screen, RULER_COLOR, snap_screen, 5, 1)
            return

        end_point_m = p2_m if p2_m is not None else snapped_indicator_m if snapped_indicator_m is not None else current_mouse_m
        if end_point_m is None: return

        p1_screen = world_m_to_screen(p1_m)
        p2_screen = world_m_to_screen(end_point_m)

        pygame.draw.line(screen, RULER_COLOR, p1_screen, p2_screen, 2)
        pygame.draw.circle(screen, RULER_COLOR, p1_screen, 5, 2)
        pygame.draw.circle(screen, RULER_COLOR, p2_screen, 5, 2)

        distance_m = calculate_distance_m(p1_m, end_point_m)
        dist_text = small_font.render(f"{distance_m:.3f}m", True, BLACK)
        mid_x = (p1_screen[0] + p2_screen[0]) // 2
        mid_y = (p1_screen[1] + p2_screen[1]) // 2
        text_rect = dist_text.get_rect(center=(mid_x, mid_y - 15))
        bg_rect = text_rect.inflate(4, 2)
        pygame.draw.rect(screen, WHITE, bg_rect)
        screen.blit(dist_text, text_rect)

        if p2_m is None and snapped_indicator_m:
             snap_screen = world_m_to_screen(snapped_indicator_m)
             pygame.draw.circle(screen, RULER_COLOR, snap_screen, 5, 1)

    def save_all_data():
        """حفظ البيانات - يستخدم النظام الجديد للحفظ في قاعدة البيانات"""
        if len(elements) == 0 and len(measurements) == 0:
            print("لا توجد بيانات للحفظ!")
            return

        # محاولة الحفظ في قاعدة البيانات أولاً
        if current_user and user_db:
            # طلب اسم المشروع من المستخدم
            root = tk.Tk()
            root.withdraw()

            from tkinter import simpledialog
            project_name = simpledialog.askstring(
                "حفظ المشروع",
                "أدخل اسم المشروع:",
                initialvalue=f"Project_{datetime.now().strftime('%Y%m%d_%H%M')}"
            )

            if project_name:
                success = save_project_data(project_name, auto_save=False)
                if success:
                    print(f"✅ تم حفظ المشروع في قاعدة البيانات: {project_name}")
                    return
                else:
                    print("❌ فشل في حفظ المشروع في قاعدة البيانات")
            else:
                print("تم إلغاء الحفظ")
                return

        # إذا فشل الحفظ في قاعدة البيانات، استخدم الطريقة القديمة
        print("⚠️ الحفظ في ملف محلي...")

        root = tk.Tk()
        root.withdraw()

        file_path = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")],
            title="اختر مكان حفظ البيانات"
        )

        if not file_path:
            print("تم إلغاء الحفظ")
            return

        try:
            # استخدام دالة الحفظ الجديدة لتجميع البيانات
            project_data = {
                'walls': [],
                'chairs': [],
                'doors': [],
                'windows': [],
                'measurements': []
            }

            for elem in elements:
                if elem['type'] == 'wall':
                    wall_data = {
                        'start_m': list(elem['start_m']),
                        'end_m': list(elem['end_m']),
                        'length_m': elem['length_m'],
                        'height_m': elem.get('height_m', 3.0)
                    }
                    project_data['walls'].append(wall_data)
                elif elem['type'] == 'chair':
                    chair_data = {
                        'pos_m': list(elem['pos_m']),
                        'size_m': elem.get('size_m', 0.5)
                    }
                    project_data['chairs'].append(chair_data)
                elif elem['type'] == 'door':
                    door_data = {
                        'pos_m': list(elem['pos_m']),
                        'width_m': elem.get('width_m', 0.9),
                        'height_m': elem.get('height_m', 2.2),
                        'bottom_height_m': elem.get('bottom_height_m', 0.0),
                        'left_distance_m': elem.get('left_distance_m', 0.0),
                        'right_distance_m': elem.get('right_distance_m', 0.0)
                    }
                    project_data['doors'].append(door_data)
                elif elem['type'] == 'window':
                    window_data = {
                        'pos_m': list(elem['pos_m']),
                        'width_m': elem.get('width_m', 1.2),
                        'height_m': elem.get('height_m', 1.5),
                        'bottom_height_m': elem.get('bottom_height_m', 1.0),
                        'left_distance_m': elem.get('left_distance_m', 0.0),
                        'right_distance_m': elem.get('right_distance_m', 0.0)
                    }
                    project_data['windows'].append(window_data)

            # تجميع بيانات القياسات
            for m in measurements:
                measurement_data = {
                    'point1_m': list(m["point1"]),
                    'point2_m': list(m["point2"]),
                    'distance_m': m["distance"]
                }
                project_data['measurements'].append(measurement_data)

            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(project_data, f, indent=4, ensure_ascii=False)
                print(f"تم الحفظ في: {file_path}")

        except Exception as e:
            print(f"خطأ أثناء الحفظ: {e}")

    def draw_door_opening(screen, door_rect, is_horizontal):
        """رسم الباب كتجويف مع إطار أحمر"""
        # رسم التجويف (خلفية بيضاء لإظهار الفراغ)
        pygame.draw.rect(screen, WHITE, door_rect)

        # رسم الإطار الأحمر السميك
        frame_thickness = 3
        pygame.draw.rect(screen, (200, 0, 0), door_rect, frame_thickness)  # أحمر داكن

        # إضافة خط متقطع في المنتصف لإظهار أنه تجويف
        if is_horizontal:
            # خط عمودي في المنتصف
            start_y = door_rect.top + 2
            end_y = door_rect.bottom - 2
            center_x = door_rect.centerx

            # رسم خط متقطع
            dash_length = 4
            current_y = start_y
            while current_y < end_y:
                pygame.draw.line(screen, (150, 0, 0),
                               (center_x, current_y),
                               (center_x, min(current_y + dash_length, end_y)), 2)
                current_y += dash_length * 2
        else:
            # خط أفقي في المنتصف
            start_x = door_rect.left + 2
            end_x = door_rect.right - 2
            center_y = door_rect.centery

            # رسم خط متقطع
            dash_length = 4
            current_x = start_x
            while current_x < end_x:
                pygame.draw.line(screen, (150, 0, 0),
                               (current_x, center_y),
                               (min(current_x + dash_length, end_x), center_y), 2)
                current_x += dash_length * 2

    def draw_window_opening(screen, window_rect, is_horizontal):
        """رسم النافذة كتجويف مع إطار أزرق"""
        # رسم التجويف (خلفية بيضاء لإظهار الفراغ)
        pygame.draw.rect(screen, WHITE, window_rect)

        # رسم الإطار الأزرق السميك
        frame_thickness = 3
        pygame.draw.rect(screen, (0, 0, 200), window_rect, frame_thickness)  # أزرق داكن

        # إضافة شبكة صغيرة لإظهار أنها نافذة
        grid_color = (0, 0, 150)  # أزرق أغمق

        if is_horizontal:
            # خطوط عمودية
            third_width = window_rect.width // 3
            for i in range(1, 3):
                x = window_rect.left + i * third_width
                pygame.draw.line(screen, grid_color,
                               (x, window_rect.top + 2),
                               (x, window_rect.bottom - 2), 1)

            # خط أفقي في المنتصف
            pygame.draw.line(screen, grid_color,
                           (window_rect.left + 2, window_rect.centery),
                           (window_rect.right - 2, window_rect.centery), 1)
        else:
            # خطوط أفقية
            third_height = window_rect.height // 3
            for i in range(1, 3):
                y = window_rect.top + i * third_height
                pygame.draw.line(screen, grid_color,
                               (window_rect.left + 2, y),
                               (window_rect.right - 2, y), 1)

            # خط عمودي في المنتصف
            pygame.draw.line(screen, grid_color,
                           (window_rect.centerx, window_rect.top + 2),
                           (window_rect.centerx, window_rect.bottom - 2), 1)

    def update_door_window_preview(mouse_pos_m, current_tool_name, input_values_dict):
        """تحديث معاينة الباب/النافذة عند تحريك الماوس"""
        nonlocal preview_door_window, preview_wall

        if current_tool_name not in ["DOOR", "WINDOW"]:
            preview_door_window = None
            preview_wall = None
            return

        # البحث عن أقرب جدار
        walls_only = [elem for elem in elements if elem['type'] == 'wall']
        closest_wall = find_closest_wall_m(mouse_pos_m, walls_only)

        if closest_wall:
            # حساب أقرب نقطة على الجدار
            _, closest_point = point_segment_distance_m(mouse_pos_m, closest_wall['start_m'], closest_wall['end_m'])

            # تحديد عرض الباب/النافذة
            if current_tool_name == "DOOR":
                width_m = float(input_values_dict.get('door', '0.9'))
                item_type = 'door'
            else:
                width_m = float(input_values_dict.get('window', '1.2'))
                item_type = 'window'

            # إنشاء معلومات المعاينة
            preview_door_window = {
                'type': item_type,
                'pos_m': closest_point,
                'width_m': width_m,
                'wall_ref': closest_wall
            }
            preview_wall = closest_wall
        else:
            preview_door_window = None
            preview_wall = None

    def calculate_door_window_distances(center_m, width_m, wall):
        """حساب المسافات يمين ويسار الباب/النافذة"""
        try:
            # حساب اتجاه الجدار
            wall_start = wall['start_m']
            wall_end = wall['end_m']
            wall_length = wall['length_m']

            # حساب المسافة من بداية الجدار إلى مركز الباب/النافذة
            distance_from_start = calculate_distance_m(wall_start, center_m)

            # حساب المسافات يمين ويسار
            half_width = width_m / 2
            left_distance = distance_from_start - half_width
            right_distance = wall_length - (distance_from_start + half_width)

            # التأكد من أن المسافات لا تكون سالبة
            left_distance = max(0, left_distance)
            right_distance = max(0, right_distance)

            return left_distance, right_distance

        except Exception as e:
            print(f"خطأ في حساب المسافات: {e}")
            return 0, 0

    def draw_door_window_preview(screen):
        """رسم معاينة الباب/النافذة مع المسافات"""
        if not preview_door_window or not preview_wall:
            return

        try:
            # استخراج بيانات المعاينة
            center_m = preview_door_window['pos_m']
            width_m = preview_door_window['width_m']
            wall = preview_wall
            item_type = preview_door_window['type']

            # حساب اتجاه الجدار
            dx_wall_m = wall['end_m'][0] - wall['start_m'][0]
            dy_wall_m = wall['end_m'][1] - wall['start_m'][1]
            is_horizontal = abs(dx_wall_m) > abs(dy_wall_m)

            # حساب المسافات يمين ويسار
            left_distance, right_distance = calculate_door_window_distances(center_m, width_m, wall)

            # حساب أبعاد المستطيل
            center_screen = world_m_to_screen(center_m)
            width_px = width_m * pixels_per_meter
            height_px_base = 8  # سمك أساسي

            if is_horizontal:
                rect_w = width_px
                rect_h = height_px_base
            else:
                rect_w = height_px_base
                rect_h = width_px

            # إنشاء مستطيل المعاينة
            preview_rect = pygame.Rect(
                center_screen[0] - rect_w / 2,
                center_screen[1] - rect_h / 2,
                rect_w, rect_h
            )

            # رسم المعاينة بشفافية
            preview_surface = pygame.Surface((rect_w, rect_h), pygame.SRCALPHA)

            if item_type == 'door':
                # معاينة الباب مع شفافية
                preview_surface.fill((255, 255, 255, 150))  # أبيض شفاف
                pygame.draw.rect(preview_surface, (200, 0, 0, 200), (0, 0, rect_w, rect_h), 3)  # إطار أحمر شفاف

                # خط متقطع للمعاينة
                if is_horizontal:
                    center_x = rect_w // 2
                    for y in range(2, int(rect_h) - 2, 8):
                        pygame.draw.line(preview_surface, (150, 0, 0, 150),
                                       (center_x, y), (center_x, min(y + 4, int(rect_h) - 2)), 2)
                else:
                    center_y = rect_h // 2
                    for x in range(2, int(rect_w) - 2, 8):
                        pygame.draw.line(preview_surface, (150, 0, 0, 150),
                                       (x, center_y), (min(x + 4, int(rect_w) - 2), center_y), 2)
            else:
                # معاينة النافذة مع شفافية
                preview_surface.fill((255, 255, 255, 150))  # أبيض شفاف
                pygame.draw.rect(preview_surface, (0, 0, 200, 200), (0, 0, rect_w, rect_h), 3)  # إطار أزرق شفاف

                # شبكة للمعاينة
                grid_color = (0, 0, 150, 150)
                if is_horizontal:
                    # خطوط عمودية
                    third_width = rect_w // 3
                    for i in range(1, 3):
                        x = i * third_width
                        pygame.draw.line(preview_surface, grid_color, (x, 2), (x, rect_h - 2), 1)
                    # خط أفقي
                    pygame.draw.line(preview_surface, grid_color, (2, rect_h//2), (rect_w - 2, rect_h//2), 1)
                else:
                    # خطوط أفقية
                    third_height = rect_h // 3
                    for i in range(1, 3):
                        y = i * third_height
                        pygame.draw.line(preview_surface, grid_color, (2, y), (rect_w - 2, y), 1)
                    # خط عمودي
                    pygame.draw.line(preview_surface, grid_color, (rect_w//2, 2), (rect_w//2, rect_h - 2), 1)

            # رسم المعاينة على الشاشة
            screen.blit(preview_surface, preview_rect.topleft)

            # رسم نص المعاينة الرئيسي
            preview_text = f"معاينة {item_type} - {width_m:.2f}م"
            text_surface = small_font.render(preview_text, True, (100, 100, 100))
            text_rect = text_surface.get_rect()
            text_rect.center = (center_screen[0], center_screen[1] - rect_h//2 - 35)

            # خلفية للنص الرئيسي
            bg_rect = text_rect.inflate(4, 2)
            pygame.draw.rect(screen, (255, 255, 255, 200), bg_rect)
            screen.blit(text_surface, text_rect)

            # رسم المسافات يمين ويسار
            draw_distance_labels(screen, center_screen, rect_w, rect_h, left_distance, right_distance, is_horizontal)

        except Exception as e:
            print(f"خطأ في رسم معاينة الباب/النافذة: {e}")

    def draw_distance_labels(screen, center_screen, rect_w, rect_h, left_distance, right_distance, is_horizontal):
        """رسم تسميات المسافات يمين ويسار الباب/النافذة"""
        try:
            # ألوان النصوص
            text_color = (50, 50, 150)  # أزرق داكن
            bg_color = (255, 255, 255, 220)  # أبيض شبه شفاف

            if is_horizontal:
                # للجدران الأفقية - المسافات يمين ويسار

                # النص الأيسر
                left_text = f"Left: {left_distance:.2f}m"
                left_surface = small_font.render(left_text, True, text_color)
                left_rect = left_surface.get_rect()
                left_rect.center = (center_screen[0] - rect_w//2 - 40, center_screen[1])

                # خلفية النص الأيسر
                left_bg_rect = left_rect.inflate(6, 4)
                pygame.draw.rect(screen, bg_color, left_bg_rect)
                pygame.draw.rect(screen, text_color, left_bg_rect, 1)
                screen.blit(left_surface, left_rect)

                # خط يشير للجانب الأيسر
                pygame.draw.line(screen, text_color,
                               (center_screen[0] - rect_w//2, center_screen[1]),
                               (left_rect.right + 5, center_screen[1]), 2)

                # النص الأيمن
                right_text = f"Right: {right_distance:.2f}m"
                right_surface = small_font.render(right_text, True, text_color)
                right_rect = right_surface.get_rect()
                right_rect.center = (center_screen[0] + rect_w//2 + 40, center_screen[1])

                # خلفية النص الأيمن
                right_bg_rect = right_rect.inflate(6, 4)
                pygame.draw.rect(screen, bg_color, right_bg_rect)
                pygame.draw.rect(screen, text_color, right_bg_rect, 1)
                screen.blit(right_surface, right_rect)

                # خط يشير للجانب الأيمن
                pygame.draw.line(screen, text_color,
                               (center_screen[0] + rect_w//2, center_screen[1]),
                               (right_rect.left - 5, center_screen[1]), 2)

            else:
                # للجدران الرأسية - المسافات أعلى وأسفل

                # النص العلوي (يسار)
                top_text = f"Top: {left_distance:.2f}m"
                top_surface = small_font.render(top_text, True, text_color)
                top_rect = top_surface.get_rect()
                top_rect.center = (center_screen[0], center_screen[1] - rect_h//2 - 20)

                # خلفية النص العلوي
                top_bg_rect = top_rect.inflate(6, 4)
                pygame.draw.rect(screen, bg_color, top_bg_rect)
                pygame.draw.rect(screen, text_color, top_bg_rect, 1)
                screen.blit(top_surface, top_rect)

                # خط يشير للأعلى
                pygame.draw.line(screen, text_color,
                               (center_screen[0], center_screen[1] - rect_h//2),
                               (center_screen[0], top_rect.bottom + 5), 2)

                # النص السفلي (يمين)
                bottom_text = f"Bottom: {right_distance:.2f}m"
                bottom_surface = small_font.render(bottom_text, True, text_color)
                bottom_rect = bottom_surface.get_rect()
                bottom_rect.center = (center_screen[0], center_screen[1] + rect_h//2 + 20)

                # خلفية النص السفلي
                bottom_bg_rect = bottom_rect.inflate(6, 4)
                pygame.draw.rect(screen, bg_color, bottom_bg_rect)
                pygame.draw.rect(screen, text_color, bottom_bg_rect, 1)
                screen.blit(bottom_surface, bottom_rect)

                # خط يشير للأسفل
                pygame.draw.line(screen, text_color,
                               (center_screen[0], center_screen[1] + rect_h//2),
                               (center_screen[0], bottom_rect.top - 5), 2)

        except Exception as e:
            print(f"خطأ في رسم تسميات المسافات: {e}")

    def draw_permanent_distance_labels(screen, element, item_rect_screen, is_horizontal):
        """رسم المسافات الثابتة للأبواب والشبابيك الموضوعة"""
        try:
            # التحقق من وجود المسافات المحفوظة
            if 'left_distance_m' not in element or 'right_distance_m' not in element:
                return

            left_distance = element['left_distance_m']
            right_distance = element['right_distance_m']

            # ألوان النصوص للعناصر الثابتة
            text_color = (80, 80, 80)  # رمادي داكن
            bg_color = (240, 240, 240, 200)  # رمادي فاتح شبه شفاف

            # حجم خط أصغر للعناصر الثابتة
            font_size = 10

            if is_horizontal:
                # للجدران الأفقية - المسافات يمين ويسار

                # النص الأيسر
                left_text = f"{left_distance:.2f}m"
                left_surface = small_font.render(left_text, True, text_color)
                left_rect = left_surface.get_rect()
                left_rect.center = (item_rect_screen.left - 25, item_rect_screen.centery)

                # خلفية النص الأيسر
                left_bg_rect = left_rect.inflate(4, 2)
                pygame.draw.rect(screen, bg_color, left_bg_rect)
                screen.blit(left_surface, left_rect)

                # خط صغير يشير للجانب الأيسر
                pygame.draw.line(screen, text_color,
                               (item_rect_screen.left, item_rect_screen.centery),
                               (left_rect.right + 2, item_rect_screen.centery), 1)

                # النص الأيمن
                right_text = f"{right_distance:.2f}m"
                right_surface = small_font.render(right_text, True, text_color)
                right_rect = right_surface.get_rect()
                right_rect.center = (item_rect_screen.right + 25, item_rect_screen.centery)

                # خلفية النص الأيمن
                right_bg_rect = right_rect.inflate(4, 2)
                pygame.draw.rect(screen, bg_color, right_bg_rect)
                screen.blit(right_surface, right_rect)

                # خط صغير يشير للجانب الأيمن
                pygame.draw.line(screen, text_color,
                               (item_rect_screen.right, item_rect_screen.centery),
                               (right_rect.left - 2, item_rect_screen.centery), 1)

            else:
                # للجدران الرأسية - المسافات أعلى وأسفل

                # النص العلوي
                top_text = f"{left_distance:.2f}m"
                top_surface = small_font.render(top_text, True, text_color)
                top_rect = top_surface.get_rect()
                top_rect.center = (item_rect_screen.centerx, item_rect_screen.top - 15)

                # خلفية النص العلوي
                top_bg_rect = top_rect.inflate(4, 2)
                pygame.draw.rect(screen, bg_color, top_bg_rect)
                screen.blit(top_surface, top_rect)

                # خط صغير يشير للأعلى
                pygame.draw.line(screen, text_color,
                               (item_rect_screen.centerx, item_rect_screen.top),
                               (item_rect_screen.centerx, top_rect.bottom + 2), 1)

                # النص السفلي
                bottom_text = f"{right_distance:.2f}m"
                bottom_surface = small_font.render(bottom_text, True, text_color)
                bottom_rect = bottom_surface.get_rect()
                bottom_rect.center = (item_rect_screen.centerx, item_rect_screen.bottom + 15)

                # خلفية النص السفلي
                bottom_bg_rect = bottom_rect.inflate(4, 2)
                pygame.draw.rect(screen, bg_color, bottom_bg_rect)
                screen.blit(bottom_surface, bottom_rect)

                # خط صغير يشير للأسفل
                pygame.draw.line(screen, text_color,
                               (item_rect_screen.centerx, item_rect_screen.bottom),
                               (item_rect_screen.centerx, bottom_rect.top - 2), 1)

        except Exception as e:
            print(f"خطأ في رسم المسافات الثابتة: {e}")

    def draw_status_bar():
        status_bar_height = 25
        status_bar_rect = pygame.Rect(SIDEBAR_WIDTH, SCREEN_HEIGHT - status_bar_height,
                                     DRAWING_AREA_WIDTH, status_bar_height)
        pygame.draw.rect(screen, GRAY, status_bar_rect)

        # عرض إحداثيات الماوس
        mouse_pos_text = small_font.render(f"X: {mouse_pos_m[0]:.2f}m, Y: {mouse_pos_m[1]:.2f}m", True, BLACK)
        screen.blit(mouse_pos_text, (SIDEBAR_WIDTH + 10, SCREEN_HEIGHT - status_bar_height + 5))

        # عرض المقياس الحالي
        scale_text = small_font.render(f"Scale: {pixels_per_meter:.1f} px/m", True, BLACK)
        screen.blit(scale_text, (SIDEBAR_WIDTH + 200, SCREEN_HEIGHT - status_bar_height + 5))

        # عرض الأداة الحالية
        tool_text = small_font.render(f"Tool: {current_tool if current_tool else 'None'}", True, BLACK)
        screen.blit(tool_text, (SIDEBAR_WIDTH + 350, SCREEN_HEIGHT - status_bar_height + 5))

    # --- حلقة اللعبة الرئيسية ---
    view_3d_btn_rect = None
    while running:
        mouse_pos = pygame.mouse.get_pos()
        mouse_pos_m = screen_to_world_m(mouse_pos)

        ruler_snapped_point_m = None
        if current_tool == "MEASURE":
            snap_tolerance_pixels = 10
            snap_tolerance_m = snap_tolerance_pixels / pixels_per_meter if pixels_per_meter > 0 else 0.1
            ruler_snapped_point_m = find_closest_endpoint_m(mouse_pos_m, elements, snap_tolerance_m)

        # تحديث معاينة الأبواب والشبابيك
        update_door_window_preview(mouse_pos_m, current_tool, input_values)

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                # التحقق من وجود تغييرات غير محفوظة
                if len(elements) > 0 or len(measurements) > 0:
                    save_choice = show_save_dialog()
                    if save_choice is True:  # نعم - حفظ والخروج
                        save_project_data(auto_save=True)
                        running = False
                    elif save_choice is False:  # لا - خروج بدون حفظ
                        running = False
                    # إذا كان None (إلغاء) - لا نفعل شيء، نبقى في المشروع
                else:
                    running = False

            elif event.type == pygame.MOUSEWHEEL:
                 if mouse_pos[0] >= SIDEBAR_WIDTH:
                    world_mx_before, world_my_before = mouse_pos_m
                    old_ppm = pixels_per_meter
                    if event.y > 0: pixels_per_meter *= ZOOM_FACTOR_STEP
                    elif event.y < 0: pixels_per_meter /= ZOOM_FACTOR_STEP
                    pixels_per_meter = max(MIN_PIXELS_PER_METER, min(MAX_PIXELS_PER_METER, pixels_per_meter))
                    new_ppm = pixels_per_meter
                    if old_ppm == 0: continue
                    screen_mouse_x_drawing = mouse_pos[0] - SIDEBAR_WIDTH
                    screen_mouse_y = mouse_pos[1]
                    view_offset_x = screen_mouse_x_drawing - world_mx_before * new_ppm
                    view_offset_y = screen_mouse_y - world_my_before * new_ppm

            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # زر الماوس الأيسر
                    mouse_pos = pygame.mouse.get_pos()

                    # التحقق من النقر على زر العرض ثلاثي الأبعاد
                    if view_3d_btn_rect and view_3d_btn_rect.collidepoint(mouse_pos):
                        # حفظ الملف الحالي قبل الانتقال إلى العرض ثلاثي الأبعاد
                        temp_file_path = "temp_design.json"

                        try:
                            # تجميع بيانات الجدران والكائنات
                            walls_data = []
                            chairs_data = []
                            doors_data = []
                            windows_data = []

                            for elem in elements:
                                if elem['type'] == 'wall':
                                    wall_data = {
                                        'start_m': list(elem['start_m']),
                                        'end_m': list(elem['end_m']),
                                        'length_m': elem['length_m']
                                    }

                                    # إضافة ارتفاع الجدار إذا كان موجودًا
                                    if 'height_m' in elem:
                                        wall_data['height_m'] = elem['height_m']
                                        print(f"حفظ جدار بارتفاع: {elem['height_m']} متر")
                                    else:
                                        print("تحذير: جدار بدون ارتفاع محدد")

                                    walls_data.append(wall_data)

                                elif elem['type'] == 'chair':
                                    chair_data = {
                                        'pos_m': list(elem['pos_m']),
                                        'size_m': elem['size_m']
                                    }
                                    chairs_data.append(chair_data)

                                elif elem['type'] == 'door':
                                    door_data = {
                                        'pos_m': list(elem['pos_m']),
                                        'width_m': elem['width_m'],
                                        'height_m': elem.get('height_m', 2.2),
                                        'bottom_height_m': elem.get('bottom_height_m', 0.0),
                                        'left_distance_m': elem.get('left_distance_m', 0.0),
                                        'right_distance_m': elem.get('right_distance_m', 0.0)
                                    }
                                    doors_data.append(door_data)
                                    print(f"حفظ باب بعرض: {elem['width_m']:.2f}م")

                                elif elem['type'] == 'window':
                                    window_data = {
                                        'pos_m': list(elem['pos_m']),
                                        'width_m': elem['width_m'],
                                        'height_m': elem.get('height_m', 1.5),
                                        'bottom_height_m': elem.get('bottom_height_m', 1.0),
                                        'left_distance_m': elem.get('left_distance_m', 0.0),
                                        'right_distance_m': elem.get('right_distance_m', 0.0)
                                    }
                                    windows_data.append(window_data)
                                    print(f"حفظ نافذة بعرض: {elem['width_m']:.2f}م")

                            # دمج البيانات في ملف واحد
                            combined_data = {
                                'walls': walls_data,
                                'chairs': chairs_data,
                                'doors': doors_data,
                                'windows': windows_data,
                                'measurements': measurements
                            }

                            with open(temp_file_path, "w", encoding="utf-8") as f:
                                json.dump(combined_data, f, indent=4, ensure_ascii=False)
                                print(f"تم حفظ التصميم مؤقتًا في: {temp_file_path}")

                            # إيقاف حلقة pygame مؤقتًا
                            running = False
                            pygame.quit()

                            # تشغيل العرض ثلاثي الأبعاد
                            print("جاري تشغيل العرض ثلاثي الأبعاد...")
                            launch_3d_viewer(temp_file_path)

                            # إعادة تشغيل pygame بعد العودة
                            print("العودة إلى التطبيق الرئيسي...")
                            pygame.init()
                            screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
                            pygame.display.set_caption("MEASURE")
                            running = True  # إعادة تشغيل الحلقة

                        except Exception as e:
                            print(f"خطأ أثناء الانتقال إلى العرض ثلاثي الأبعاد: {e}")
                            # في حالة حدوث خطأ، نتأكد من إعادة تشغيل pygame
                            if not pygame.get_init():
                                pygame.init()
                                screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
                                pygame.display.set_caption("MEASURE")

                        continue  # تخطي باقي الحلقة والبدء من جديد

                if event.button == 1:
                    # التحقق من النقر على نافذة التعديل أولاً
                    edit_rects = draw_edit_panel()
                    clicked_on_edit = False

                    if edit_rects:
                        for input_type, rect in edit_rects.items():
                            if rect.collidepoint(mouse_pos):
                                clicked_on_edit = True
                                if input_type == 'save':
                                    if apply_element_changes():
                                        close_edit_panel()
                                elif input_type == 'cancel':
                                    close_edit_panel()
                                elif input_type in ['length', 'height', 'width', 'size', 'bottom_height', 'left_distance', 'right_distance']:
                                    edit_active_input = input_type
                                break

                    if not clicked_on_edit:
                        clicked_sidebar = False
                        if mouse_pos[0] < SIDEBAR_WIDTH:
                            clicked_sidebar = True
                            clicked_on_input = False
                            for input_type, rect in INPUT_RECTS.items():
                                if rect.collidepoint(mouse_pos):
                                    active_input = input_type
                                    clicked_on_input = True
                                    break
                            if not clicked_on_input:
                                if active_input is not None: active_input = None
                                old_tool = current_tool
                                for i, rect in enumerate(TOOL_RECTS):
                                    if rect.collidepoint(mouse_pos):
                                        current_tool = TOOLS[i]
                                        drawing = False
                                        start_pos_m = None
                                        if old_tool == "MEASURE" and current_tool != "MEASURE":
                                            ruler_point1_m = None
                                            ruler_point2_m = None
                                        elif current_tool == "SAVE":
                                            save_all_data()
                                            current_tool = None
                                        break
                        else:
                             if active_input is not None: active_input = None

                    if not clicked_sidebar:
                        if current_tool == "WALL":
                            # البحث عن نهاية جدار قريبة
                            endpoint, connected_wall = find_wall_endpoint_near(mouse_pos_m, elements)

                            if endpoint and connected_wall:
                                # بدء جدار جديد من نهاية جدار موجود
                                drawing = True
                                start_pos_m = endpoint
                                # تحديد الاتجاه العمودي للجدار المتصل
                                perpendicular_direction = get_perpendicular_direction(connected_wall)
                                print(f"🔗 بدء جدار جديد من نهاية جدار موجود")
                                print(f"📐 الاتجاه المطلوب: {perpendicular_direction}")
                                # حفظ الاتجاه المطلوب للاستخدام أثناء الرسم
                                current_wall_direction = perpendicular_direction
                            else:
                                # بدء جدار جديد عادي
                                drawing = True
                                start_pos_m = mouse_pos_m
                                current_wall_direction = None  # لا يوجد قيد على الاتجاه

                        elif current_tool == "DOOR" or current_tool == "WINDOW":
                            # استخدام المعاينة المحدثة لإضافة الباب/النافذة
                            if preview_door_window and preview_wall:
                                current_state_copy = copy.deepcopy(elements)
                                history.append(current_state_copy)
                                if len(history) > MAX_HISTORY_SIZE: history.pop(0)

                                # إضافة الباب/النافذة من المعاينة
                                elem_type = preview_door_window['type']

                                # حساب المسافات للحفظ
                                left_dist, right_dist = calculate_door_window_distances(
                                    preview_door_window['pos_m'],
                                    preview_door_window['width_m'],
                                    preview_door_window['wall_ref']
                                )

                                new_element = {
                                    'type': elem_type,
                                    'pos_m': preview_door_window['pos_m'],
                                    'width_m': preview_door_window['width_m'],
                                    'wall_ref': preview_door_window['wall_ref'],
                                    'left_distance_m': left_dist,
                                    'right_distance_m': right_dist
                                }

                                # إضافة خصائص إضافية للأبواب
                                if elem_type == 'door':
                                    new_element['door_type'] = input_values['door_type']
                                    new_element['door_swing'] = input_values['door_swing']

                                elements.append(new_element)

                                print(f"✅ تم إضافة {elem_type} بعرض {preview_door_window['width_m']:.2f}م")

                                # إعادة تعيين المعاينة
                                preview_door_window = None
                                preview_wall = None
                            else:
                                print("خطأ: يجب وضع الباب/النافذة على جدار موجود!")

                        elif current_tool == "CHAIR":
                            size_m = 0.5
                            try:
                                size_m_input = float(input_values['chair_size'])
                                if size_m_input > 0: size_m = size_m_input
                                else: print(f"تحذير: الحجم يجب أن يكون موجبًا. استخدام {size_m} متر.")
                            except ValueError: print(f"تحذير: رقم غير صالح. استخدام {size_m} متر.")

                            current_state_copy = copy.deepcopy(elements)
                            history.append(current_state_copy)
                            if len(history) > MAX_HISTORY_SIZE: history.pop(0)

                            elements.append({
                                'type': 'chair',
                                'pos_m': mouse_pos_m,
                                'size_m': size_m
                            })
                            print(f"تم إضافة كرسي بحجم {size_m} متر في الموضع ({mouse_pos_m[0]:.2f}, {mouse_pos_m[1]:.2f})")

                        elif current_tool == "BUGATTI":
                            size_m = 4.5  # حجم افتراضي للسيارة
                            try:
                                size_m_input = float(input_values['bugatti_size'])
                                if size_m_input > 0: size_m = size_m_input
                                else: print(f"تحذير: الحجم يجب أن يكون موجبًا. استخدام {size_m} متر.")
                            except ValueError: print(f"تحذير: رقم غير صالح. استخدام {size_m} متر.")

                            current_state_copy = copy.deepcopy(elements)
                            history.append(current_state_copy)
                            if len(history) > MAX_HISTORY_SIZE: history.pop(0)

                            elements.append({
                                'type': 'bugatti',
                                'pos_m': mouse_pos_m,
                                'size_m': size_m,
                                'model_path': 'models/bugatti/bugatti.obj'  # مسار النموذج
                            })
                            print(f"🚗 تم إضافة سيارة Bugatti بحجم {size_m} متر في الموضع ({mouse_pos_m[0]:.2f}, {mouse_pos_m[1]:.2f})")

                        elif current_tool == "DELETE":
                            element_to_delete = find_element_at_pos_m(mouse_pos_m, elements)
                            if element_to_delete:
                                current_state_copy = copy.deepcopy(elements)
                                history.append(current_state_copy)
                                if len(history) > MAX_HISTORY_SIZE: history.pop(0)

                                if element_to_delete['type'] == 'wall':
                                    elements_to_remove = [element_to_delete]
                                    elements_copy = list(elements)
                                    for other_elem in elements_copy:
                                        if other_elem.get('wall_ref') == element_to_delete:
                                            if other_elem not in elements_to_remove:
                                                elements_to_remove.append(other_elem)
                                    num_attached = len(elements_to_remove) - 1
                                    for item in elements_to_remove:
                                        if item in elements: elements.remove(item)
                                    print(f"تم حذف الجدار و {num_attached} عنصر مرتبط به.")
                                else:
                                    if element_to_delete in elements:
                                        elements.remove(element_to_delete)
                                        print(f"تم حذف {element_to_delete['type']}.")
                            else: print("لم يتم العثور على عنصر للحذف في هذا الموقع.")

                        elif current_tool == "MEASURE":
                            click_pos_m = ruler_snapped_point_m if ruler_snapped_point_m else mouse_pos_m
                            if ruler_point1_m is None:
                                ruler_point1_m = click_pos_m
                                ruler_point2_m = None
                            else:
                                ruler_point2_m = click_pos_m
                                distance = calculate_distance_m(ruler_point1_m, ruler_point2_m)
                                measurements.append({
                                    "point1": ruler_point1_m,
                                    "point2": ruler_point2_m,
                                    "distance": distance
                                })
                                ruler_point1_m = None
                                ruler_point2_m = None

                        elif current_tool == "EDIT":
                            # أداة التعديل - فتح نافذة تعديل للعنصر المحدد
                            clicked_element = find_element_at_pos_m(mouse_pos_m, elements)
                            if clicked_element:
                                open_edit_panel(clicked_element)

            elif event.type == pygame.MOUSEBUTTONUP:
                if event.button == 1:
                    if drawing and current_tool == "WALL" and start_pos_m:
                        # استخدام النظام الجديد للمحاذاة
                        if current_wall_direction:
                            # إذا كان هناك اتجاه محدد (من جدار متصل)
                            snapped_end_pos_m = snap_to_perpendicular_direction(start_pos_m, mouse_pos_m, current_wall_direction)
                        else:
                            # الطريقة العادية (أفقي أو عمودي حسب الأقرب)
                            snapped_end_pos_m = snap_to_orthogonal_m(start_pos_m, mouse_pos_m)

                        length_m = calculate_distance_m(start_pos_m, snapped_end_pos_m)
                        min_length_pixels = 5
                        min_length_m = min_length_pixels / pixels_per_meter if pixels_per_meter > 0 else 0.01

                        if length_m > min_length_m:
                            current_state_copy = copy.deepcopy(elements)
                            history.append(current_state_copy)
                            if len(history) > MAX_HISTORY_SIZE: history.pop(0)

                            # استخراج ارتفاع الجدار من حقل الإدخال
                            wall_height_m = 3.0  # القيمة الافتراضية
                            try:
                                height_input = input_values.get('wall_height', "3.0")
                                wall_height_m = float(height_input)
                                if wall_height_m <= 0:
                                    wall_height_m = 3.0
                                    print(f"تحذير: ارتفاع الجدار يجب أن يكون موجبًا. استخدام {wall_height_m} متر.")
                            except ValueError:
                                print(f"تحذير: قيمة ارتفاع غير صالحة. استخدام {wall_height_m} متر.")

                            # إضافة الجدار مع ارتفاعه
                            new_wall = {
                                'type': 'wall',
                                'start_m': start_pos_m,
                                'end_m': snapped_end_pos_m,
                                'length_m': length_m,
                                'height_m': wall_height_m  # إضافة ارتفاع الجدار
                            }
                            elements.append(new_wall)

                            # رسالة تأكيد مع نوع الاتجاه
                            direction_info = f" ({current_wall_direction})" if current_wall_direction else ""
                            print(f"✅ تم إضافة جدار جديد{direction_info} بارتفاع: {wall_height_m} متر")
                        else:
                            print("الجدار قصير جدًا، لم يتم إضافته.")

                        # إعادة تعيين المتغيرات
                        drawing = False
                        start_pos_m = None
                        current_wall_direction = None  # إعادة تعيين الاتجاه

            elif event.type == pygame.KEYDOWN:
                if edit_active_input:
                    # معالجة إدخال النص لنافذة التعديل
                    current_string = edit_values.get(edit_active_input, '')
                    if event.key == pygame.K_BACKSPACE:
                        edit_values[edit_active_input] = current_string[:-1]
                    elif event.key == pygame.K_RETURN or event.key == pygame.K_KP_ENTER:
                        try:
                           val = float(edit_values[edit_active_input])
                           if val <= 0: print("تحذير: القيمة يجب أن تكون موجبة.")
                        except ValueError: print("تحذير: صيغة الرقم غير صالحة.")
                        edit_active_input = None
                    elif event.unicode.isdigit() or (event.unicode == '.' and '.' not in current_string):
                        edit_values[edit_active_input] += event.unicode

                elif active_input:
                    current_string = input_values[active_input]
                    if event.key == pygame.K_BACKSPACE:
                        input_values[active_input] = current_string[:-1]
                    elif event.key == pygame.K_RETURN or event.key == pygame.K_KP_ENTER:
                        try:
                           val = float(input_values[active_input])
                           if val <= 0: print("تحذير: القيمة يجب أن تكون موجبة.")
                        except ValueError: print("تحذير: صيغة الرقم غير صالحة.")
                        active_input = None
                    elif event.unicode.isdigit() or (event.unicode == '.' and '.' not in current_string):
                        input_values[active_input] += event.unicode

                elif event.key == pygame.K_z:
                    if history:
                        elements = history.pop()
                        print("تم التراجع عن الإجراء الأخير.")
                    else: print("لا يوجد شيء للتراجع عنه.")

                elif event.key == pygame.K_ESCAPE:
                    # إغلاق نافذة التعديل عند الضغط على ESC
                    if show_edit_panel:
                        close_edit_panel()
                        print("تم إغلاق نافذة التعديل")
                    else:
                        # إذا لم تكن نافذة التعديل مفتوحة، اسأل عن الخروج
                        if len(elements) > 0 or len(measurements) > 0:
                            save_choice = show_save_dialog()
                            if save_choice is True:  # نعم - حفظ والخروج
                                save_project_data(auto_save=True)
                                running = False
                            elif save_choice is False:  # لا - خروج بدون حفظ
                                running = False
                            # إذا كان None (إلغاء) - لا نفعل شيء، نبقى في المشروع
                        else:
                            running = False

        # --- رسم الواجهة ---
        screen.fill(WHITE)
        drawing_area_rect = pygame.Rect(SIDEBAR_WIDTH, 0, DRAWING_AREA_WIDTH, SCREEN_HEIGHT)
        screen.set_clip(drawing_area_rect)

        draw_grid()
        draw_elements()

        # رسم معاينة الأبواب والشبابيك
        draw_door_window_preview(screen)

        # رسم مؤشرات نقاط الاتصال عند استخدام أداة الجدار
        if current_tool == "WALL" and not drawing:
            endpoint, connected_wall = find_wall_endpoint_near(mouse_pos_m, elements)
            if endpoint and connected_wall:
                # رسم دائرة خضراء حول نقطة النهاية القريبة
                endpoint_screen = world_m_to_screen(endpoint)
                pygame.draw.circle(screen, GREEN, endpoint_screen, 8, 3)

                # عرض نص توضيحي
                direction = get_perpendicular_direction(connected_wall)
                hint_text = small_font.render(f"🔗 انقر للاتصال ({direction})", True, GREEN)
                screen.blit(hint_text, (endpoint_screen[0] + 15, endpoint_screen[1] - 10))

        if drawing and current_tool == "WALL" and start_pos_m:
            # استخدام النظام الجديد للمحاذاة
            if current_wall_direction:
                # إذا كان هناك اتجاه محدد (من جدار متصل)
                snapped_mouse_pos_m = snap_to_perpendicular_direction(start_pos_m, mouse_pos_m, current_wall_direction)
            else:
                # الطريقة العادية (أفقي أو عمودي حسب الأقرب)
                snapped_mouse_pos_m = snap_to_orthogonal_m(start_pos_m, mouse_pos_m)

            start_screen = world_m_to_screen(start_pos_m)
            snapped_end_screen = world_m_to_screen(snapped_mouse_pos_m)

            # رسم خط أحمر للمعاينة
            pygame.draw.line(screen, RED, start_screen, snapped_end_screen, 2)

            # عرض المسافة
            current_dist_m = calculate_distance_m(start_pos_m, snapped_mouse_pos_m)
            temp_dim_text = small_font.render(f"{current_dist_m:.2f}m", True, RED)
            screen.blit(temp_dim_text, (snapped_end_screen[0] + 10, snapped_end_screen[1]))

            # عرض نوع الاتجاه
            if current_wall_direction:
                direction_text = small_font.render(f"🔗 {current_wall_direction}", True, RED)
                screen.blit(direction_text, (snapped_end_screen[0] + 10, snapped_end_screen[1] + 20))

        if current_tool == "MEASURE":
            draw_ruler(screen, ruler_point1_m, ruler_point2_m, mouse_pos_m, ruler_snapped_point_m)

        screen.set_clip(None)
        view_3d_btn_rect = draw_sidebar(current_tool, input_values, active_input)

        # رسم نافذة التعديل إذا كانت مفتوحة
        draw_edit_panel()

        draw_status_bar()

        pygame.display.flip()
        clock.tick(60)

    # --- إنهاء البرنامج ---
    pygame.quit()

# --- الدالة الرئيسية ---
def main():
    """الدالة الرئيسية للتطبيق"""
    print("🚀 بدء تشغيل نظام التصميم المعماري...")

    # 1. عرض صفحة تسجيل الدخول أولاً
    print("📋 عرض صفحة تسجيل الدخول...")
    login_result = show_login_page()

    if not login_result:
        print("❌ تم إلغاء تسجيل الدخول. إنهاء التطبيق.")
        return

    # 2. التحقق من نوع النتيجة
    if isinstance(login_result, dict) and login_result.get('admin_panel'):
        # المدير الرئيسي - توجه للوحة الإدارة مباشرة
        user_data = login_result['user']
        print(f"🔐 تسجيل دخول المدير الرئيسي: {user_data['full_name']} ({user_data['username']})")

        # استيراد وعرض لوحة الإدارة
        from admin_panel import show_admin_panel
        print("🛠️ فتح لوحة إدارة المستخدمين...")
        admin_action = show_admin_panel(user_data)

        if admin_action == "logout":
            print("👋 تم تسجيل الخروج - العودة لصفحة تسجيل الدخول")
            main()
        return
    else:
        # مستخدم عادي أو مدير عادي
        user_data = login_result
        print(f"✅ تم تسجيل الدخول بنجاح: {user_data['full_name']} ({user_data['username']})")

        # 3. تهيئة قاعدة البيانات للمستخدم الحالي
        global current_user, user_db
        current_user = user_data
        user_db = UserDatabase()

        # 4. عرض Dashboard أولاً
        print("📊 فتح لوحة المعلومات الرئيسية...")
        from dashboard import show_dashboard

        while True:
            action = show_dashboard(user_data)

            if action == "quit":
                print("👋 إنهاء التطبيق")
                break
            elif action == "logout":
                print("👋 تم تسجيل الخروج - العودة لصفحة تسجيل الدخول")
                # العودة لبداية الدالة الرئيسية لإعادة عرض صفحة تسجيل الدخول
                main()
                return
            elif action == "customer_info" or action == "new_project":
                print("🏠 بدء صفحة معلومات الزبون...")
                root, CustomerInfoApp = start_customer_info()
                app = CustomerInfoApp(root, start_drawing_app, user_data)
                root.mainloop()
                break
            elif action == "admin_panel":
                if user_data.get('user_role') == 'super_admin':
                    from admin_panel import show_admin_panel
                    print("🛠️ فتح لوحة إدارة المستخدمين...")
                    admin_action = show_admin_panel(user_data)

                    if admin_action == "logout":
                        print("👋 تم تسجيل الخروج - العودة لصفحة تسجيل الدخول")
                        main()
                        return
                    else:
                        continue  # العودة للـ Dashboard
            elif action == "view_projects":
                print("📁 عرض المشاريع...")
                from projects_viewer import show_projects_viewer
                project_action, selected_project = show_projects_viewer(user_data)

                if project_action == "quit":
                    print("👋 إنهاء التطبيق")
                    break
                elif project_action == "dashboard":
                    continue  # العودة للـ Dashboard
                elif project_action == "logout":
                    print("👋 تم تسجيل الخروج - العودة لصفحة تسجيل الدخول")
                    main()
                    return
                elif project_action == "new_project":
                    print("🏠 بدء مشروع جديد...")
                    root, CustomerInfoApp = start_customer_info()
                    app = CustomerInfoApp(root, start_drawing_app, user_data)
                    root.mainloop()
                    break
                elif project_action == "open_project" and selected_project:
                    print(f"📂 فتح المشروع: {selected_project[1]}")
                    # تحميل المشروع وبدء التطبيق
                    project_id = selected_project[0]  # ID المشروع
                    project_name = selected_project[1]  # اسم المشروع

                    # بدء تطبيق الرسم مع تحميل المشروع
                    start_drawing_app(user_data, project_id=project_id, project_name=project_name)
                    break
            elif action == "system_settings":
                if user_data.get('user_role') == 'super_admin':
                    print("⚙️ إعدادات النظام...")
                    from system_settings import show_system_settings
                    settings_action = show_system_settings(user_data)

                    if settings_action == "logout":
                        print("👋 تم تسجيل الخروج - العودة لصفحة تسجيل الدخول")
                        main()
                        return
                    else:
                        continue  # العودة للـ Dashboard
                else:
                    print("❌ غير مصرح لك بالوصول لإعدادات النظام")
                    continue
            else:
                # إجراء غير معروف، العودة للـ Dashboard
                continue

# متغيرات عامة للمستخدم الحالي
current_user = None
user_db = None

# --- تشغيل البرنامج ---
if __name__ == "__main__":
    main()

def save_to_file(file_path, elements, measurements):
    """حفظ التصميم الحالي إلى ملف JSON"""
    try:
        # تجميع بيانات الجدران
        walls_data = []
        for elem in elements:
            if elem['type'] == 'wall':
                wall_data = {
                    'start_m': list(elem['start_m']),
                    'end_m': list(elem['end_m']),
                    'length_m': elem['length_m']
                }

                # إضافة ارتفاع الجدار إذا كان موجودًا
                if 'height_m' in elem:
                    wall_data['height_m'] = elem['height_m']
                    print(f"حفظ جدار بارتفاع: {elem['height_m']} متر")
                else:
                    # إضافة ارتفاع افتراضي إذا لم يكن موجودًا
                    wall_data['height_m'] = 3.0
                    print(f"حفظ جدار بارتفاع افتراضي: 3.0 متر")

                walls_data.append(wall_data)

        # تجميع بيانات القياسات
        measures_data = []
        for m in measurements:
            measures_data.append({
                'point1_m': list(m["point1"]),
                'point2_m': list(m["point2"]),
                'distance_m': m["distance"]
            })

        # دمج البيانات في ملف واحد
        combined_data = {
            'walls': walls_data,
            'measurements': measures_data
        }

        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(combined_data, f, indent=4, ensure_ascii=False)
            print(f"تم الحفظ في: {file_path}")

    except Exception as e:
        print(f"خطأ أثناء الحفظ: {e}")

# --- تشغيل التطبيق ---
if __name__ == "__main__":
    # بدء تطبيق معلومات العميل
    start_customer_info()












