#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🗄️ Database Management Tool
أداة شاملة لإدارة قاعدة البيانات
"""

import sqlite3
import json
from datetime import datetime
from database import UserDatabase

class DatabaseManager:
    def __init__(self, db_path="users.db"):
        """تهيئة مدير قاعدة البيانات"""
        self.db_path = db_path
        self.db = UserDatabase(db_path)

    def show_all_users(self):
        """عرض جميع المستخدمين"""
        print("\n👥 جميع المستخدمين في قاعدة البيانات:")
        print("=" * 80)

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, username, full_name, email, phone, created_at, last_login, is_active
                FROM users
                ORDER BY id
            ''')

            users = cursor.fetchall()

            if not users:
                print("❌ لا توجد مستخدمين في قاعدة البيانات")
                return

            print(f"{'ID':<4} {'Username':<15} {'Full Name':<20} {'Email':<25} {'Phone':<15} {'Active':<8}")
            print("-" * 80)

            for user in users:
                active_status = "✅ نعم" if user[7] else "❌ لا"
                print(f"{user[0]:<4} {user[1]:<15} {user[2] or 'N/A':<20} {user[3] or 'N/A':<25} {user[4] or 'N/A':<15} {active_status:<8}")

            print(f"\n📊 إجمالي المستخدمين: {len(users)}")
            conn.close()

        except Exception as e:
            print(f"❌ خطأ في عرض المستخدمين: {e}")

    def show_user_details(self, user_id):
        """عرض تفاصيل مستخدم محدد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM users WHERE id = ?
            ''', (user_id,))

            user = cursor.fetchone()

            if not user:
                print(f"❌ لم يتم العثور على مستخدم بالمعرف: {user_id}")
                return

            print(f"\n👤 تفاصيل المستخدم (ID: {user_id}):")
            print("=" * 50)
            print(f"🆔 المعرف: {user[0]}")
            print(f"👤 اسم المستخدم: {user[1]}")
            print(f"📧 البريد الإلكتروني: {user[3] or 'غير محدد'}")
            print(f"👨‍💼 الاسم الكامل: {user[4] or 'غير محدد'}")
            print(f"📱 الهاتف: {user[5] or 'غير محدد'}")
            print(f"📅 تاريخ الإنشاء: {user[6]}")
            print(f"🕐 آخر تسجيل دخول: {user[7] or 'لم يسجل دخول بعد'}")
            print(f"✅ نشط: {'نعم' if user[8] else 'لا'}")

            # عرض مشاريع المستخدم
            self.show_user_projects(user_id)

            conn.close()

        except Exception as e:
            print(f"❌ خطأ في عرض تفاصيل المستخدم: {e}")

    def show_user_projects(self, user_id):
        """عرض مشاريع المستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, project_name, created_at, updated_at
                FROM user_projects
                WHERE user_id = ?
                ORDER BY updated_at DESC
            ''', (user_id,))

            projects = cursor.fetchall()

            print(f"\n📁 مشاريع المستخدم ({len(projects)} مشروع):")
            print("-" * 50)

            if not projects:
                print("❌ لا توجد مشاريع لهذا المستخدم")
                return

            for project in projects:
                print(f"🏗️ {project[1]} (ID: {project[0]})")
                print(f"   📅 تاريخ الإنشاء: {project[2]}")
                print(f"   🔄 آخر تحديث: {project[3]}")
                print()

            conn.close()

        except Exception as e:
            print(f"❌ خطأ في عرض مشاريع المستخدم: {e}")

    def create_user(self):
        """إنشاء مستخدم جديد تفاعلياً"""
        print("\n➕ إنشاء مستخدم جديد:")
        print("=" * 40)

        try:
            username = input("👤 اسم المستخدم: ").strip()
            if not username:
                print("❌ اسم المستخدم مطلوب")
                return

            password = input("🔒 كلمة المرور: ").strip()
            if not password:
                print("❌ كلمة المرور مطلوبة")
                return

            full_name = input("👨‍💼 الاسم الكامل (اختياري): ").strip() or None
            email = input("📧 البريد الإلكتروني (اختياري): ").strip() or None
            phone = input("📱 رقم الهاتف (اختياري): ").strip() or None

            user_id = self.db.create_user(username, password, email, full_name, phone)

            if user_id:
                print(f"✅ تم إنشاء المستخدم بنجاح! المعرف: {user_id}")
            else:
                print("❌ فشل في إنشاء المستخدم (اسم المستخدم موجود بالفعل)")

        except Exception as e:
            print(f"❌ خطأ في إنشاء المستخدم: {e}")

    def update_user(self, user_id):
        """تحديث بيانات مستخدم"""
        print(f"\n✏️ تحديث بيانات المستخدم (ID: {user_id}):")
        print("=" * 50)

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود المستخدم
            cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
            user = cursor.fetchone()

            if not user:
                print(f"❌ لم يتم العثور على مستخدم بالمعرف: {user_id}")
                return

            print("اتركه فارغاً للاحتفاظ بالقيمة الحالية")
            print(f"القيم الحالية: اسم المستخدم={user[1]}, الاسم={user[4]}, البريد={user[3]}, الهاتف={user[5]}")
            print()

            # جمع البيانات الجديدة
            new_username = input(f"👤 اسم المستخدم الجديد [{user[1]}]: ").strip()
            new_full_name = input(f"👨‍💼 الاسم الكامل الجديد [{user[4] or 'غير محدد'}]: ").strip()
            new_email = input(f"📧 البريد الإلكتروني الجديد [{user[3] or 'غير محدد'}]: ").strip()
            new_phone = input(f"📱 رقم الهاتف الجديد [{user[5] or 'غير محدد'}]: ").strip()

            # استخدام القيم الحالية إذا لم يتم إدخال قيم جديدة
            final_username = new_username if new_username else user[1]
            final_full_name = new_full_name if new_full_name else user[4]
            final_email = new_email if new_email else user[3]
            final_phone = new_phone if new_phone else user[5]

            # تحديث البيانات
            cursor.execute('''
                UPDATE users
                SET username = ?, full_name = ?, email = ?, phone = ?
                WHERE id = ?
            ''', (final_username, final_full_name, final_email, final_phone, user_id))

            conn.commit()

            if cursor.rowcount > 0:
                print("✅ تم تحديث بيانات المستخدم بنجاح!")
            else:
                print("❌ لم يتم تحديث أي بيانات")

            conn.close()

        except sqlite3.IntegrityError:
            print("❌ اسم المستخدم موجود بالفعل")
        except Exception as e:
            print(f"❌ خطأ في تحديث المستخدم: {e}")

    def change_password(self, user_id):
        """تغيير كلمة مرور المستخدم"""
        print(f"\n🔒 تغيير كلمة مرور المستخدم (ID: {user_id}):")
        print("=" * 50)

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود المستخدم
            cursor.execute('SELECT username FROM users WHERE id = ?', (user_id,))
            user = cursor.fetchone()

            if not user:
                print(f"❌ لم يتم العثور على مستخدم بالمعرف: {user_id}")
                return

            print(f"تغيير كلمة مرور المستخدم: {user[0]}")

            new_password = input("🔒 كلمة المرور الجديدة: ").strip()
            if not new_password:
                print("❌ كلمة المرور مطلوبة")
                return

            confirm_password = input("🔒 تأكيد كلمة المرور: ").strip()
            if new_password != confirm_password:
                print("❌ كلمة المرور وتأكيدها غير متطابقتين")
                return

            # تشفير كلمة المرور الجديدة
            password_hash = self.db.hash_password(new_password)

            # تحديث كلمة المرور
            cursor.execute('''
                UPDATE users SET password_hash = ? WHERE id = ?
            ''', (password_hash, user_id))

            conn.commit()

            if cursor.rowcount > 0:
                print("✅ تم تغيير كلمة المرور بنجاح!")
            else:
                print("❌ لم يتم تغيير كلمة المرور")

            conn.close()

        except Exception as e:
            print(f"❌ خطأ في تغيير كلمة المرور: {e}")

    def toggle_user_status(self, user_id):
        """تفعيل/إلغاء تفعيل المستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على الحالة الحالية
            cursor.execute('SELECT username, is_active FROM users WHERE id = ?', (user_id,))
            user = cursor.fetchone()

            if not user:
                print(f"❌ لم يتم العثور على مستخدم بالمعرف: {user_id}")
                return

            new_status = not user[1]  # عكس الحالة الحالية

            cursor.execute('UPDATE users SET is_active = ? WHERE id = ?', (new_status, user_id))
            conn.commit()

            status_text = "مفعل" if new_status else "معطل"
            print(f"✅ تم تغيير حالة المستخدم '{user[0]}' إلى: {status_text}")

            conn.close()

        except Exception as e:
            print(f"❌ خطأ في تغيير حالة المستخدم: {e}")

    def delete_user(self, user_id):
        """حذف مستخدم (مع التأكيد)"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على معلومات المستخدم
            cursor.execute('SELECT username, full_name FROM users WHERE id = ?', (user_id,))
            user = cursor.fetchone()

            if not user:
                print(f"❌ لم يتم العثور على مستخدم بالمعرف: {user_id}")
                return

            print(f"\n⚠️ تحذير: سيتم حذف المستخدم '{user[0]}' ({user[1]}) نهائياً!")
            print("هذا الإجراء لا يمكن التراجع عنه وسيحذف جميع مشاريع المستخدم أيضاً.")

            confirm = input("هل أنت متأكد؟ اكتب 'نعم' للتأكيد: ").strip()

            if confirm.lower() in ['نعم', 'yes', 'y']:
                # حذف مشاريع المستخدم أولاً
                cursor.execute('DELETE FROM user_projects WHERE user_id = ?', (user_id,))
                projects_deleted = cursor.rowcount

                # حذف المستخدم
                cursor.execute('DELETE FROM users WHERE id = ?', (user_id,))

                conn.commit()

                if cursor.rowcount > 0:
                    print(f"✅ تم حذف المستخدم '{user[0]}' و {projects_deleted} مشروع بنجاح!")
                else:
                    print("❌ لم يتم حذف المستخدم")
            else:
                print("❌ تم إلغاء عملية الحذف")

            conn.close()

        except Exception as e:
            print(f"❌ خطأ في حذف المستخدم: {e}")

    def backup_database(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"users_backup_{timestamp}.db"

            # نسخ ملف قاعدة البيانات
            import shutil
            shutil.copy2(self.db_path, backup_filename)

            print(f"✅ تم إنشاء نسخة احتياطية: {backup_filename}")

        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")

    def show_database_stats(self):
        """عرض إحصائيات قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إحصائيات المستخدمين
            cursor.execute('SELECT COUNT(*) FROM users')
            total_users = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 1')
            active_users = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM user_projects')
            total_projects = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM user_sessions')
            total_sessions = cursor.fetchone()[0]

            print("\n📊 إحصائيات قاعدة البيانات:")
            print("=" * 40)
            print(f"👥 إجمالي المستخدمين: {total_users}")
            print(f"✅ المستخدمين النشطين: {active_users}")
            print(f"❌ المستخدمين المعطلين: {total_users - active_users}")
            print(f"📁 إجمالي المشاريع: {total_projects}")
            print(f"🔐 إجمالي الجلسات: {total_sessions}")

            # آخر تسجيل دخول
            cursor.execute('SELECT username, last_login FROM users WHERE last_login IS NOT NULL ORDER BY last_login DESC LIMIT 1')
            last_login = cursor.fetchone()

            if last_login:
                print(f"🕐 آخر تسجيل دخول: {last_login[0]} في {last_login[1]}")

            conn.close()

        except Exception as e:
            print(f"❌ خطأ في عرض الإحصائيات: {e}")

def show_menu():
    """عرض القائمة الرئيسية"""
    print("\n" + "=" * 60)
    print("🗄️  مدير قاعدة البيانات - نظام التصميم المعماري")
    print("=" * 60)
    print("1️⃣  عرض جميع المستخدمين")
    print("2️⃣  عرض تفاصيل مستخدم محدد")
    print("3️⃣  إنشاء مستخدم جديد")
    print("4️⃣  تحديث بيانات مستخدم")
    print("5️⃣  تغيير كلمة مرور مستخدم")
    print("6️⃣  تفعيل/إلغاء تفعيل مستخدم")
    print("7️⃣  حذف مستخدم")
    print("8️⃣  إنشاء نسخة احتياطية")
    print("9️⃣  عرض إحصائيات قاعدة البيانات")
    print("🔍  البحث عن مستخدم")
    print("🧹  تنظيف قاعدة البيانات")
    print("📤  تصدير البيانات")
    print("📥  استيراد البيانات")
    print("0️⃣  خروج")
    print("=" * 60)

def search_users(db_manager):
    """البحث عن المستخدمين"""
    print("\n🔍 البحث عن المستخدمين:")
    search_term = input("أدخل اسم المستخدم أو الاسم الكامل أو البريد الإلكتروني: ").strip()

    if not search_term:
        print("❌ يرجى إدخال كلمة البحث")
        return

    try:
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, username, full_name, email, phone, is_active
            FROM users
            WHERE username LIKE ? OR full_name LIKE ? OR email LIKE ?
        ''', (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))

        results = cursor.fetchall()

        if not results:
            print("❌ لم يتم العثور على نتائج")
            return

        print(f"\n🎯 نتائج البحث ({len(results)} نتيجة):")
        print("-" * 70)
        print(f"{'ID':<4} {'Username':<15} {'Full Name':<20} {'Email':<25} {'Active':<8}")
        print("-" * 70)

        for user in results:
            active_status = "✅ نعم" if user[5] else "❌ لا"
            print(f"{user[0]:<4} {user[1]:<15} {user[2] or 'N/A':<20} {user[3] or 'N/A':<25} {active_status:<8}")

        conn.close()

    except Exception as e:
        print(f"❌ خطأ في البحث: {e}")

def cleanup_database(db_manager):
    """تنظيف قاعدة البيانات"""
    print("\n🧹 تنظيف قاعدة البيانات:")
    print("1. حذف الجلسات المنتهية الصلاحية")
    print("2. حذف المستخدمين المعطلين")
    print("3. حذف المشاريع الفارغة")

    choice = input("اختر نوع التنظيف (1-3): ").strip()

    try:
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()

        if choice == "1":
            # حذف الجلسات المنتهية
            cursor.execute('DELETE FROM user_sessions WHERE expires_at < datetime("now")')
            deleted = cursor.rowcount
            print(f"✅ تم حذف {deleted} جلسة منتهية الصلاحية")

        elif choice == "2":
            # حذف المستخدمين المعطلين (مع التأكيد)
            cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 0')
            inactive_count = cursor.fetchone()[0]

            if inactive_count == 0:
                print("✅ لا توجد مستخدمين معطلين")
            else:
                confirm = input(f"⚠️ سيتم حذف {inactive_count} مستخدم معطل. تأكيد (نعم/لا): ")
                if confirm.lower() in ['نعم', 'yes']:
                    cursor.execute('DELETE FROM user_projects WHERE user_id IN (SELECT id FROM users WHERE is_active = 0)')
                    cursor.execute('DELETE FROM users WHERE is_active = 0')
                    print(f"✅ تم حذف {inactive_count} مستخدم معطل")
                else:
                    print("❌ تم إلغاء العملية")

        elif choice == "3":
            # حذف المشاريع الفارغة
            cursor.execute('DELETE FROM user_projects WHERE project_data IS NULL OR project_data = ""')
            deleted = cursor.rowcount
            print(f"✅ تم حذف {deleted} مشروع فارغ")

        else:
            print("❌ خيار غير صحيح")
            return

        conn.commit()
        conn.close()

    except Exception as e:
        print(f"❌ خطأ في تنظيف قاعدة البيانات: {e}")

def export_data(db_manager):
    """تصدير البيانات إلى JSON"""
    try:
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()

        # تصدير المستخدمين
        cursor.execute('SELECT * FROM users')
        users = cursor.fetchall()

        # تصدير المشاريع
        cursor.execute('SELECT * FROM user_projects')
        projects = cursor.fetchall()

        # إنشاء هيكل البيانات
        export_data = {
            'export_date': datetime.now().isoformat(),
            'users': [
                {
                    'id': user[0],
                    'username': user[1],
                    'email': user[3],
                    'full_name': user[4],
                    'phone': user[5],
                    'created_at': user[6],
                    'last_login': user[7],
                    'is_active': user[8]
                }
                for user in users
            ],
            'projects': [
                {
                    'id': project[0],
                    'user_id': project[1],
                    'project_name': project[2],
                    'created_at': project[4],
                    'updated_at': project[5]
                }
                for project in projects
            ]
        }

        # حفظ في ملف JSON
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"database_export_{timestamp}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)

        print(f"✅ تم تصدير البيانات إلى: {filename}")
        print(f"📊 تم تصدير {len(users)} مستخدم و {len(projects)} مشروع")

        conn.close()

    except Exception as e:
        print(f"❌ خطأ في تصدير البيانات: {e}")

def main():
    """الدالة الرئيسية"""
    print("🗄️ مرحباً بك في مدير قاعدة البيانات")

    # تهيئة مدير قاعدة البيانات
    db_manager = DatabaseManager()

    while True:
        show_menu()
        choice = input("\n🎯 اختر العملية المطلوبة: ").strip()

        try:
            if choice == "1":
                db_manager.show_all_users()

            elif choice == "2":
                user_id = input("أدخل معرف المستخدم: ").strip()
                if user_id.isdigit():
                    db_manager.show_user_details(int(user_id))
                else:
                    print("❌ معرف المستخدم يجب أن يكون رقماً")

            elif choice == "3":
                db_manager.create_user()

            elif choice == "4":
                user_id = input("أدخل معرف المستخدم للتحديث: ").strip()
                if user_id.isdigit():
                    db_manager.update_user(int(user_id))
                else:
                    print("❌ معرف المستخدم يجب أن يكون رقماً")

            elif choice == "5":
                user_id = input("أدخل معرف المستخدم لتغيير كلمة المرور: ").strip()
                if user_id.isdigit():
                    db_manager.change_password(int(user_id))
                else:
                    print("❌ معرف المستخدم يجب أن يكون رقماً")

            elif choice == "6":
                user_id = input("أدخل معرف المستخدم لتغيير الحالة: ").strip()
                if user_id.isdigit():
                    db_manager.toggle_user_status(int(user_id))
                else:
                    print("❌ معرف المستخدم يجب أن يكون رقماً")

            elif choice == "7":
                user_id = input("أدخل معرف المستخدم للحذف: ").strip()
                if user_id.isdigit():
                    db_manager.delete_user(int(user_id))
                else:
                    print("❌ معرف المستخدم يجب أن يكون رقماً")

            elif choice == "8":
                db_manager.backup_database()

            elif choice == "9":
                db_manager.show_database_stats()

            elif choice.lower() in ["بحث", "search", "🔍"]:
                search_users(db_manager)

            elif choice.lower() in ["تنظيف", "cleanup", "🧹"]:
                cleanup_database(db_manager)

            elif choice.lower() in ["تصدير", "export", "📤"]:
                export_data(db_manager)

            elif choice == "0":
                print("👋 شكراً لاستخدام مدير قاعدة البيانات!")
                break

            else:
                print("❌ خيار غير صحيح. يرجى المحاولة مرة أخرى.")

        except KeyboardInterrupt:
            print("\n\n⏹️ تم إيقاف العملية بواسطة المستخدم")
            break
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")

        # انتظار المستخدم قبل العودة للقائمة
        input("\n⏸️ اضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
