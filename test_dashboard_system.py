#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 اختبار نظام Dashboard المتقدم
Test Advanced Dashboard System
"""

import sys
import os
from database import UserDatabase

def test_dashboard_import():
    """اختبار استيراد Dashboard"""
    print("📊 اختبار استيراد Dashboard...")
    
    try:
        from dashboard import Dashboard, show_dashboard
        print("✅ تم استيراد Dashboard بنجاح")
        return True
    except ImportError as e:
        print(f"❌ فشل في استيراد Dashboard: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في استيراد Dashboard: {e}")
        return False

def test_projects_viewer_import():
    """اختبار استيراد عارض المشاريع"""
    print("\n📁 اختبار استيراد عارض المشاريع...")
    
    try:
        from projects_viewer import ProjectsViewer, show_projects_viewer
        print("✅ تم استيراد عارض المشاريع بنجاح")
        return True
    except ImportError as e:
        print(f"❌ فشل في استيراد عارض المشاريع: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في استيراد عارض المشاريع: {e}")
        return False

def test_system_settings_import():
    """اختبار استيراد إعدادات النظام"""
    print("\n⚙️ اختبار استيراد إعدادات النظام...")
    
    try:
        from system_settings import SystemSettings, show_system_settings
        print("✅ تم استيراد إعدادات النظام بنجاح")
        return True
    except ImportError as e:
        print(f"❌ فشل في استيراد إعدادات النظام: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في استيراد إعدادات النظام: {e}")
        return False

def test_user_permissions():
    """اختبار صلاحيات المستخدمين"""
    print("\n🔐 اختبار صلاحيات المستخدمين...")
    
    db = UserDatabase()
    
    # اختبار المدير الرئيسي
    super_admin = db.verify_user("MohammedBushiha", "Mfb112002*")
    if super_admin and super_admin['user_role'] == 'super_admin':
        print("✅ المدير الرئيسي: صلاحيات كاملة")
    else:
        print("❌ المدير الرئيسي: صلاحيات غير صحيحة")
        return False
    
    # اختبار مستخدم عادي
    regular_user = db.verify_user("admin", "admin123")
    if regular_user and regular_user['user_role'] != 'super_admin':
        print("✅ المستخدم العادي: صلاحيات محدودة")
    else:
        print("❌ المستخدم العادي: صلاحيات غير صحيحة")
        return False
    
    return True

def test_dashboard_data_loading():
    """اختبار تحميل بيانات Dashboard"""
    print("\n📈 اختبار تحميل بيانات Dashboard...")
    
    try:
        from dashboard import Dashboard
        
        db = UserDatabase()
        user_data = db.verify_user("admin", "admin123")
        
        if not user_data:
            print("❌ فشل في تسجيل الدخول للاختبار")
            return False
        
        # محاكاة تحميل البيانات
        dashboard = Dashboard.__new__(Dashboard)
        dashboard.user_data = user_data
        dashboard.db = db
        dashboard.stats = {}
        dashboard.recent_projects = []
        dashboard.system_info = {}
        
        # اختبار تحميل البيانات
        dashboard.load_dashboard_data()
        
        # التحقق من البيانات
        required_stats = ['total_users', 'active_users', 'total_projects', 'user_projects']
        for stat in required_stats:
            if stat not in dashboard.stats:
                print(f"❌ إحصائية مفقودة: {stat}")
                return False
        
        print("✅ تم تحميل بيانات Dashboard بنجاح")
        print(f"   👥 إجمالي المستخدمين: {dashboard.stats['total_users']}")
        print(f"   📁 مشاريع المستخدم: {dashboard.stats['user_projects']}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحميل بيانات Dashboard: {e}")
        return False

def test_projects_data_loading():
    """اختبار تحميل بيانات المشاريع"""
    print("\n📂 اختبار تحميل بيانات المشاريع...")
    
    try:
        from projects_viewer import ProjectsViewer
        
        db = UserDatabase()
        user_data = db.verify_user("admin", "admin123")
        
        if not user_data:
            print("❌ فشل في تسجيل الدخول للاختبار")
            return False
        
        # محاكاة تحميل المشاريع
        viewer = ProjectsViewer.__new__(ProjectsViewer)
        viewer.user_data = user_data
        viewer.db = db
        viewer.projects = []
        
        # اختبار تحميل المشاريع
        viewer.load_projects()
        
        print(f"✅ تم تحميل {len(viewer.projects)} مشروع للمستخدم")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحميل بيانات المشاريع: {e}")
        return False

def test_system_stats_loading():
    """اختبار تحميل إحصائيات النظام"""
    print("\n📊 اختبار تحميل إحصائيات النظام...")
    
    try:
        from system_settings import SystemSettings
        
        db = UserDatabase()
        admin_user = db.verify_user("MohammedBushiha", "Mfb112002*")
        
        if not admin_user:
            print("❌ فشل في تسجيل الدخول كمدير رئيسي")
            return False
        
        # محاكاة تحميل الإحصائيات
        settings = SystemSettings.__new__(SystemSettings)
        settings.user_data = admin_user
        settings.db = db
        settings.system_stats = {}
        
        # اختبار تحميل الإحصائيات
        settings.load_system_stats()
        
        # التحقق من الإحصائيات
        required_stats = ['total_users', 'active_users', 'total_projects', 'db_size_mb']
        for stat in required_stats:
            if stat not in settings.system_stats:
                print(f"❌ إحصائية مفقودة: {stat}")
                return False
        
        print("✅ تم تحميل إحصائيات النظام بنجاح")
        print(f"   💾 حجم قاعدة البيانات: {settings.system_stats['db_size_mb']} MB")
        print(f"   👥 إجمالي المستخدمين: {settings.system_stats['total_users']}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحميل إحصائيات النظام: {e}")
        return False

def test_pygame_functionality():
    """اختبار وظائف pygame"""
    print("\n🎮 اختبار وظائف pygame...")
    
    try:
        import pygame
        
        # تهيئة pygame
        pygame.init()
        
        # اختبار إنشاء شاشة
        screen = pygame.display.set_mode((100, 100))
        pygame.display.set_caption("Test")
        
        # اختبار الخطوط
        font = pygame.font.Font(None, 24)
        text = font.render("Test", True, (0, 0, 0))
        
        # اختبار الألوان والرسم
        screen.fill((255, 255, 255))
        pygame.draw.rect(screen, (0, 0, 255), (10, 10, 50, 50))
        
        pygame.quit()
        
        print("✅ pygame يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في pygame: {e}")
        return False

def test_file_operations():
    """اختبار عمليات الملفات"""
    print("\n📁 اختبار عمليات الملفات...")
    
    try:
        # اختبار وجود قاعدة البيانات
        if os.path.exists("users.db"):
            print("✅ قاعدة البيانات موجودة")
            
            # اختبار حجم قاعدة البيانات
            db_size = os.path.getsize("users.db")
            print(f"   💾 حجم قاعدة البيانات: {db_size} bytes")
        else:
            print("⚠️ قاعدة البيانات غير موجودة")
        
        # اختبار إنشاء ملف مؤقت
        test_file = "test_temp.txt"
        with open(test_file, 'w') as f:
            f.write("Test content")
        
        if os.path.exists(test_file):
            os.remove(test_file)
            print("✅ عمليات الملفات تعمل بشكل صحيح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عمليات الملفات: {e}")
        return False

def test_main_integration():
    """اختبار التكامل مع main.py"""
    print("\n🔗 اختبار التكامل مع main.py...")
    
    try:
        # محاولة استيراد الوظائف من main.py
        sys.path.append('.')
        
        # اختبار وجود الوظائف المطلوبة
        import main
        
        # التحقق من وجود الوظائف الأساسية
        required_functions = ['main', 'start_customer_info', 'start_drawing_app']
        for func_name in required_functions:
            if not hasattr(main, func_name):
                print(f"❌ وظيفة مفقودة في main.py: {func_name}")
                return False
        
        print("✅ التكامل مع main.py يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التكامل مع main.py: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار نظام Dashboard المتقدم")
    print("=" * 60)
    
    tests = [
        ("اختبار استيراد Dashboard", test_dashboard_import),
        ("اختبار استيراد عارض المشاريع", test_projects_viewer_import),
        ("اختبار استيراد إعدادات النظام", test_system_settings_import),
        ("اختبار صلاحيات المستخدمين", test_user_permissions),
        ("اختبار تحميل بيانات Dashboard", test_dashboard_data_loading),
        ("اختبار تحميل بيانات المشاريع", test_projects_data_loading),
        ("اختبار تحميل إحصائيات النظام", test_system_stats_loading),
        ("اختبار وظائف pygame", test_pygame_functionality),
        ("اختبار عمليات الملفات", test_file_operations),
        ("اختبار التكامل مع main.py", test_main_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! نظام Dashboard جاهز للاستخدام")
        print("\n📊 مميزات النظام الجديد:")
        print("  ✅ Dashboard احترافي مع إحصائيات شاملة")
        print("  ✅ عارض مشاريع متقدم مع إدارة كاملة")
        print("  ✅ إعدادات نظام متكاملة للمدير الرئيسي")
        print("  ✅ واجهة رسومية عصرية وجذابة")
        print("  ✅ نظام صلاحيات محكم ومؤمن")
        
        print("\n🚀 لتشغيل النظام:")
        print("  python main.py")
        
        print("\n🔐 معلومات تسجيل الدخول:")
        print("  المدير الرئيسي: MohammedBushiha / Mfb112002*")
        print("  (يتم توجيهه مباشرة للوحة الإدارة)")
        print("  المستخدمين العاديين: يتم توجيههم للـ Dashboard")
        
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        print("\nللمساعدة:")
        print("  - تأكد من تثبيت pygame: pip install pygame")
        print("  - راجع ملف DASHBOARD_SYSTEM_GUIDE.md")
        print("  - تأكد من وجود جميع الملفات المطلوبة")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
