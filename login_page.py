import pygame
import sys
from database import UserDatabase

# إعدادات الشاشة
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
BACKGROUND_COLOR = (240, 248, 255)  # لون أزرق فاتح
PRIMARY_COLOR = (70, 130, 180)      # أزرق فولاذي
SECONDARY_COLOR = (255, 255, 255)   # أبيض
TEXT_COLOR = (25, 25, 112)          # أزرق داكن
ERROR_COLOR = (220, 20, 60)         # أحمر
SUCCESS_COLOR = (34, 139, 34)       # أخضر
INPUT_COLOR = (248, 248, 255)       # أبيض مائل للأزرق
BUTTON_HOVER_COLOR = (100, 149, 237) # أزرق كورن فلاور

class LoginPage:
    def __init__(self):
        """Initialize login page"""
        pygame.init()
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Login - Architectural Design System")
        self.clock = pygame.time.Clock()

        # تهيئة قاعدة البيانات
        self.db = UserDatabase()

        # تهيئة الخطوط
        self.title_font = pygame.font.Font(None, 48)
        self.label_font = pygame.font.Font(None, 32)
        self.input_font = pygame.font.Font(None, 28)
        self.button_font = pygame.font.Font(None, 32)
        self.message_font = pygame.font.Font(None, 24)

        # متغيرات حقول الإدخال
        self.username = ""
        self.password = ""
        self.active_field = None  # "username" أو "password"

        # متغيرات الرسائل
        self.message = ""
        self.message_color = TEXT_COLOR
        self.show_message_timer = 0

        # متغيرات الأزرار
        self.login_button_hover = False
        self.register_button_hover = False

        # حالة التطبيق
        self.running = True
        self.login_successful = False
        self.current_user = None

        # وضع التسجيل
        self.register_mode = False
        self.confirm_password = ""
        self.email = ""
        self.full_name = ""
        self.phone = ""

    def draw_background(self):
        """رسم خلفية الصفحة"""
        self.screen.fill(BACKGROUND_COLOR)

        # رسم دوائر زخرفية في الخلفية
        for i in range(5):
            alpha = 30 - i * 5
            color = (*PRIMARY_COLOR, alpha)
            radius = 100 + i * 50
            center_x = SCREEN_WIDTH // 4
            center_y = SCREEN_HEIGHT // 3

            # إنشاء سطح شفاف للدائرة
            circle_surface = pygame.Surface((radius * 2, radius * 2), pygame.SRCALPHA)
            pygame.draw.circle(circle_surface, color, (radius, radius), radius)
            self.screen.blit(circle_surface, (center_x - radius, center_y - radius))

    def draw_login_form(self):
        """رسم نموذج تسجيل الدخول"""
        # إعدادات النموذج
        form_width = 400
        form_height = 500 if not self.register_mode else 700
        form_x = (SCREEN_WIDTH - form_width) // 2
        form_y = (SCREEN_HEIGHT - form_height) // 2

        # رسم خلفية النموذج
        form_rect = pygame.Rect(form_x, form_y, form_width, form_height)
        pygame.draw.rect(self.screen, SECONDARY_COLOR, form_rect, border_radius=15)
        pygame.draw.rect(self.screen, PRIMARY_COLOR, form_rect, 3, border_radius=15)

        # Title
        title_text = "Create New Account" if self.register_mode else "Login"
        title_surface = self.title_font.render(title_text, True, PRIMARY_COLOR)
        title_rect = title_surface.get_rect(center=(SCREEN_WIDTH // 2, form_y + 50))
        self.screen.blit(title_surface, title_rect)

        # حقول الإدخال
        current_y = form_y + 100

        if self.register_mode:
            current_y = self.draw_register_fields(form_x, current_y, form_width)
        else:
            current_y = self.draw_login_fields(form_x, current_y, form_width)

        # الأزرار
        self.draw_buttons(form_x, current_y, form_width)

        # رسالة الحالة
        if self.message and self.show_message_timer > 0:
            message_surface = self.message_font.render(self.message, True, self.message_color)
            message_rect = message_surface.get_rect(center=(SCREEN_WIDTH // 2, form_y + form_height - 30))
            self.screen.blit(message_surface, message_rect)

    def draw_login_fields(self, form_x, current_y, form_width):
        """Draw login fields"""
        # Username field
        username_label = self.label_font.render("Username:", True, TEXT_COLOR)
        self.screen.blit(username_label, (form_x + 30, current_y))

        username_rect = pygame.Rect(form_x + 30, current_y + 35, form_width - 60, 40)
        self.username_rect = username_rect

        border_color = PRIMARY_COLOR if self.active_field == "username" else TEXT_COLOR
        pygame.draw.rect(self.screen, INPUT_COLOR, username_rect, border_radius=5)
        pygame.draw.rect(self.screen, border_color, username_rect, 2, border_radius=5)

        username_surface = self.input_font.render(self.username, True, TEXT_COLOR)
        self.screen.blit(username_surface, (username_rect.x + 10, username_rect.y + 10))

        current_y += 90

        # Password field
        password_label = self.label_font.render("Password:", True, TEXT_COLOR)
        self.screen.blit(password_label, (form_x + 30, current_y))

        password_rect = pygame.Rect(form_x + 30, current_y + 35, form_width - 60, 40)
        self.password_rect = password_rect

        border_color = PRIMARY_COLOR if self.active_field == "password" else TEXT_COLOR
        pygame.draw.rect(self.screen, INPUT_COLOR, password_rect, border_radius=5)
        pygame.draw.rect(self.screen, border_color, password_rect, 2, border_radius=5)

        # Hide password
        password_display = "*" * len(self.password)
        password_surface = self.input_font.render(password_display, True, TEXT_COLOR)
        self.screen.blit(password_surface, (password_rect.x + 10, password_rect.y + 10))

        return current_y + 90

    def draw_register_fields(self, form_x, current_y, form_width):
        """Draw registration fields"""
        field_height = 40
        field_spacing = 70

        fields = [
            ("Full Name:", "full_name", self.full_name),
            ("Username:", "username", self.username),
            ("Email:", "email", self.email),
            ("Phone:", "phone", self.phone),
            ("Password:", "password", "*" * len(self.password)),
            ("Confirm Password:", "confirm_password", "*" * len(self.confirm_password))
        ]

        for i, (label, field_name, value) in enumerate(fields):
            # Label
            label_surface = self.label_font.render(label, True, TEXT_COLOR)
            self.screen.blit(label_surface, (form_x + 30, current_y))

            # Input field
            field_rect = pygame.Rect(form_x + 30, current_y + 25, form_width - 60, field_height)
            setattr(self, f"{field_name}_rect", field_rect)

            border_color = PRIMARY_COLOR if self.active_field == field_name else TEXT_COLOR
            pygame.draw.rect(self.screen, INPUT_COLOR, field_rect, border_radius=5)
            pygame.draw.rect(self.screen, border_color, field_rect, 2, border_radius=5)

            value_surface = self.input_font.render(value, True, TEXT_COLOR)
            self.screen.blit(value_surface, (field_rect.x + 10, field_rect.y + 8))

            current_y += field_spacing

        return current_y

    def draw_buttons(self, form_x, current_y, form_width):
        """Draw buttons"""
        button_width = 120
        button_height = 45
        button_spacing = 20

        # Login button only (no registration allowed)
        login_x = form_x + (form_width - button_width) // 2
        login_rect = pygame.Rect(login_x, current_y, button_width, button_height)
        self.login_button_rect = login_rect

        button_color = BUTTON_HOVER_COLOR if self.login_button_hover else PRIMARY_COLOR
        pygame.draw.rect(self.screen, button_color, login_rect, border_radius=8)

        login_text = self.button_font.render("Login", True, SECONDARY_COLOR)
        login_text_rect = login_text.get_rect(center=login_rect.center)
        self.screen.blit(login_text, login_text_rect)

    def handle_click(self, pos):
        """معالجة النقر بالماوس"""
        # وضع تسجيل الدخول فقط
        if hasattr(self, 'username_rect') and self.username_rect.collidepoint(pos):
            self.active_field = "username"
        elif hasattr(self, 'password_rect') and self.password_rect.collidepoint(pos):
            self.active_field = "password"
        elif hasattr(self, 'login_button_rect') and self.login_button_rect.collidepoint(pos):
            self.attempt_login()
        else:
            self.active_field = None

    def handle_key_input(self, event):
        """معالجة إدخال لوحة المفاتيح"""
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_BACKSPACE:
                if self.active_field == "username":
                    self.username = self.username[:-1]
                elif self.active_field == "password":
                    self.password = self.password[:-1]
                elif self.active_field == "confirm_password":
                    self.confirm_password = self.confirm_password[:-1]
                elif self.active_field == "email":
                    self.email = self.email[:-1]
                elif self.active_field == "full_name":
                    self.full_name = self.full_name[:-1]
                elif self.active_field == "phone":
                    self.phone = self.phone[:-1]

            elif event.key == pygame.K_RETURN:
                if self.register_mode:
                    self.attempt_register()
                else:
                    self.attempt_login()

            elif event.key == pygame.K_TAB:
                self.switch_active_field()

            else:
                # إضافة النص المدخل
                if event.unicode.isprintable():
                    if self.active_field == "username" and len(self.username) < 20:
                        self.username += event.unicode
                    elif self.active_field == "password" and len(self.password) < 30:
                        self.password += event.unicode
                    elif self.active_field == "confirm_password" and len(self.confirm_password) < 30:
                        self.confirm_password += event.unicode
                    elif self.active_field == "email" and len(self.email) < 50:
                        self.email += event.unicode
                    elif self.active_field == "full_name" and len(self.full_name) < 50:
                        self.full_name += event.unicode
                    elif self.active_field == "phone" and len(self.phone) < 15:
                        self.phone += event.unicode

    def switch_active_field(self):
        """التنقل بين حقول الإدخال باستخدام Tab"""
        if not self.register_mode:
            fields = ["username", "password"]
        else:
            fields = ["full_name", "username", "email", "phone", "password", "confirm_password"]

        if self.active_field in fields:
            current_index = fields.index(self.active_field)
            next_index = (current_index + 1) % len(fields)
            self.active_field = fields[next_index]
        else:
            self.active_field = fields[0]

    def switch_to_register(self):
        """التبديل إلى وضع التسجيل"""
        self.register_mode = True
        self.active_field = "full_name"
        self.clear_message()

    def switch_to_login(self):
        """التبديل إلى وضع تسجيل الدخول"""
        self.register_mode = False
        self.active_field = "username"
        self.clear_fields()
        self.clear_message()

    def clear_fields(self):
        """مسح جميع الحقول"""
        self.username = ""
        self.password = ""
        self.confirm_password = ""
        self.email = ""
        self.full_name = ""
        self.phone = ""

    def attempt_login(self):
        """Attempt to login"""
        if not self.username.strip():
            self.show_message("Please enter username", ERROR_COLOR)
            return

        if not self.password.strip():
            self.show_message("Please enter password", ERROR_COLOR)
            return

        # Verify user credentials
        user_data = self.db.verify_user(self.username.strip(), self.password)

        if user_data:
            self.current_user = user_data
            self.login_successful = True

            # التحقق من المدير الرئيسي
            if user_data['username'] == 'MohammedBushiha' and user_data['user_role'] == 'super_admin':
                self.show_message(f"Welcome Super Admin: {user_data['full_name']}", SUCCESS_COLOR)
                # توجه مباشر للوحة الإدارة
                pygame.time.set_timer(pygame.USEREVENT + 3, 1500)  # 1.5 ثانية
            else:
                self.show_message(f"Welcome {user_data['full_name'] or user_data['username']}", SUCCESS_COLOR)
                # End loop after 2 seconds
                pygame.time.set_timer(pygame.USEREVENT + 1, 2000)
        else:
            self.show_message("Invalid username or password", ERROR_COLOR)

    def attempt_register(self):
        """Attempt to create new account"""
        # Validate input data
        if not self.full_name.strip():
            self.show_message("Please enter full name", ERROR_COLOR)
            return

        if not self.username.strip():
            self.show_message("Please enter username", ERROR_COLOR)
            return

        if len(self.username.strip()) < 3:
            self.show_message("Username must be at least 3 characters", ERROR_COLOR)
            return

        if not self.password:
            self.show_message("Please enter password", ERROR_COLOR)
            return

        if len(self.password) < 6:
            self.show_message("Password must be at least 6 characters", ERROR_COLOR)
            return

        if self.password != self.confirm_password:
            self.show_message("Password and confirmation do not match", ERROR_COLOR)
            return

        # Attempt to create user
        user_id = self.db.create_user(
            username=self.username.strip(),
            password=self.password,
            email=self.email.strip() if self.email.strip() else None,
            full_name=self.full_name.strip(),
            phone=self.phone.strip() if self.phone.strip() else None
        )

        if user_id:
            self.show_message("Account created successfully! You can now login", SUCCESS_COLOR)
            # Return to login page after 2 seconds
            pygame.time.set_timer(pygame.USEREVENT + 2, 2000)
        else:
            self.show_message("Failed to create account. Username already exists", ERROR_COLOR)

    def show_message(self, message, color):
        """عرض رسالة للمستخدم"""
        self.message = message
        self.message_color = color
        self.show_message_timer = 180  # 3 ثوان بـ 60 FPS

    def clear_message(self):
        """مسح الرسالة"""
        self.message = ""
        self.show_message_timer = 0

    def update_hover_states(self, mouse_pos):
        """تحديث حالة التمرير فوق الأزرار"""
        if hasattr(self, 'login_button_rect'):
            self.login_button_hover = self.login_button_rect.collidepoint(mouse_pos)
        if hasattr(self, 'register_button_rect'):
            self.register_button_hover = self.register_button_rect.collidepoint(mouse_pos)

    def run(self):
        """تشغيل صفحة تسجيل الدخول"""
        while self.running:
            mouse_pos = pygame.mouse.get_pos()
            self.update_hover_states(mouse_pos)

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    return None

                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # النقر بالزر الأيسر
                        self.handle_click(event.pos)

                elif event.type == pygame.KEYDOWN:
                    self.handle_key_input(event)

                elif event.type == pygame.USEREVENT + 1:  # مؤقت تسجيل الدخول الناجح
                    pygame.time.set_timer(pygame.USEREVENT + 1, 0)  # إيقاف المؤقت
                    self.running = False
                    return self.current_user

                elif event.type == pygame.USEREVENT + 3:  # مؤقت المدير الرئيسي
                    pygame.time.set_timer(pygame.USEREVENT + 3, 0)  # إيقاف المؤقت
                    self.running = False
                    # إرجاع إشارة خاصة للمدير الرئيسي
                    return {'admin_panel': True, 'user': self.current_user}

            # تحديث مؤقت الرسالة
            if self.show_message_timer > 0:
                self.show_message_timer -= 1

            # رسم الشاشة
            self.draw_background()
            self.draw_login_form()

            pygame.display.flip()
            self.clock.tick(60)

        return None

def show_login_page():
    """عرض صفحة تسجيل الدخول وإرجاع بيانات المستخدم"""
    login_page = LoginPage()
    user_data = login_page.run()
    pygame.quit()
    return user_data

if __name__ == "__main__":
    user = show_login_page()
    if user:
        print(f"تم تسجيل الدخول بنجاح: {user}")
    else:
        print("تم إلغاء تسجيل الدخول")
