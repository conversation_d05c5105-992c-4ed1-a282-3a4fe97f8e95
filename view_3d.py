from direct.showbase.ShowBase import ShowBase
from panda3d.core import Filename, loadPrcFileData, NodePath
from panda3d.core import CollisionTraverser, CollisionHandlerQueue, CollisionRay, CollisionNode
from panda3d.core import GeomNode, BitMask32
from panda3d.core import VBase4, Point3, Vec3, LineSegs
from direct.task import Task
import math
import json
import os
import tkinter as tk
from tkinter import ttk
import threading
import sys

# تكوين Panda3D
loadPrcFileData('', 'win-size 1024 768')
loadPrcFileData('', 'window-title عرض ثلاثي الأبعاد للتصميم')
loadPrcFileData('', 'notify-level-direct warning')  # تقليل رسائل التصحيح

class Design3DViewer(ShowBase):
    def __init__(self, design_data_file=None):
        ShowBase.__init__(self)

        # إعداد الكاميرا
        self.disableMouse()
        self.camera_initial_pos = Point3(20, -30, 20)  # موضع أعلى وأبعد لإظهار الجدران العالية
        self.camera.setPos(self.camera_initial_pos)
        self.camera.lookAt(0, 0, 4)  # النظر إلى نقطة أعلى لإظهار ارتفاع الجدران

        # إعداد الإضاءة
        self.setup_lights()

        # التأكد من وجود نماذج أساسية
        self.ensure_basic_models()

        # متغيرات التحكم بالكاميرا
        self.last_mouse_x = 0
        self.last_mouse_y = 0
        self.mouse_btn_down = [False, False, False]  # [Left, Middle, Right]

        # إعدادات الكاميرا
        self.default_cam_target = Point3(0, 0, 4)  # هدف أعلى لإظهار الجدران العالية بشكل أفضل
        self.cam_target = Point3(self.default_cam_target)
        self.auto_positioned = False  # للتحقق من التوجه التلقائي

        # تهيئة معلمات الكاميرا الأولية
        initial_cam_vec_to_target = self.camera.getPos(self.render) - self.cam_target
        self.cam_distance = initial_cam_vec_to_target.length()
        if self.cam_distance < 1.0: self.cam_distance = 1.0
        self.cam_pitch = math.degrees(math.asin(initial_cam_vec_to_target.getZ() / self.cam_distance))
        self.cam_heading = math.degrees(math.atan2(initial_cam_vec_to_target.getX(), -initial_cam_vec_to_target.getY()))

        # تغيير لون خلفية المشهد
        self.setBackgroundColor(0.6, 0.6, 0.7, 1)  # لون رمادي مائل للأزرق

        # إنشاء الأرضية
        self.create_floor()

        # متغيرات تحريك الكائنات ثلاثية الأبعاد
        self.selected_object = None
        self.dragging_object = False
        self.chair_nodes = []  # قائمة بجميع عقد الكراسي
        self.movable_objects = []  # قائمة بجميع الكائنات القابلة للتحريك

        # متغيرات تحريك الكائنات المحسنة
        self.movement_mode = None  # None, 'drag', 'keyboard'
        self.movement_speed = 0.1  # سرعة التحريك بالمفاتيح

        # متغيرات الصفحة الجانبية
        self.sidebar_window = None
        self.sidebar_thread = None

        # إعداد نظام اكتشاف النقر المحسن
        self.setup_collision_system()

        # تحميل التصميم إذا تم تحديد ملف
        self.design_elements = []
        if design_data_file and os.path.exists(design_data_file):
            self.load_design_from_file(design_data_file)
        else:
            print("تحذير: لم يتم تحديد ملف تصميم أو الملف غير موجود")

        # تمكين التحكم التفاعلي
        self.enable_interactive_controls()

        # إضافة مفتاح للخروج
        self.accept("escape", self.on_escape_key)

        # إضافة مفاتيح لإضافة الأثاث
        self.accept("c", self.add_chair_mode)  # مفتاح C لإضافة كرسي

        # إضافة مفاتيح تحريك الكائنات
        self.accept("w", self.move_selected_forward)     # W للأمام
        self.accept("s", self.move_selected_backward)    # S للخلف
        self.accept("a", self.move_selected_left)        # A لليسار
        self.accept("d", self.move_selected_right)       # D لليمين
        self.accept("q", self.toggle_movement_mode)      # Q لتغيير وضع التحريك

        # متغيرات وضع الإضافة
        self.adding_furniture = None  # None, 'chair'
        self.furniture_size = {'chair': 0.5}

        # إنشاء الصفحة الجانبية
        try:
            self.create_sidebar()
        except Exception as e:
            print(f"تحذير: لا يمكن إنشاء الصفحة الجانبية: {e}")
            print("سيتم تشغيل العارض بدون الصفحة الجانبية")

        # عرض تعليمات الاستخدام
        self.show_instructions()

    def exit_viewer(self):
        """الخروج من العارض والعودة إلى التطبيق الرئيسي"""
        self.userExit()

    def on_escape_key(self):
        """التعامل مع مفتاح ESC"""
        if self.adding_furniture:
            # إلغاء وضع إضافة الأثاث
            self.cancel_furniture_mode()
        else:
            # الخروج من العارض
            self.exit_viewer()

    def add_chair_mode(self):
        """تفعيل وضع إضافة الكراسي"""
        self.adding_furniture = 'chair'
        print("🪑 وضع إضافة الكراسي مفعل - انقر في أي مكان لإضافة كرسي")
        print(f"📏 حجم الكرسي الحالي: {self.furniture_size['chair']} متر")
        print("⌨️ اضغط ESC للإلغاء")



    def cancel_furniture_mode(self):
        """إلغاء وضع إضافة الأثاث"""
        if self.adding_furniture:
            print(f"❌ تم إلغاء وضع إضافة {self.adding_furniture}")
            self.adding_furniture = None

    def toggle_movement_mode(self):
        """تغيير وضع التحريك"""
        if self.movement_mode == 'keyboard':
            self.movement_mode = 'drag'
            print("🖱️ وضع التحريك: السحب بالماوس")
        else:
            self.movement_mode = 'keyboard'
            print("⌨️ وضع التحريك: المفاتيح (W/A/S/D)")

    def move_selected_forward(self):
        """تحريك الكائن المحدد للأمام"""
        if self.selected_object and self.movement_mode == 'keyboard':
            current_pos = self.selected_object.getPos()
            new_pos = Point3(current_pos.x, current_pos.y + self.movement_speed, current_pos.z)
            self.selected_object.setPos(new_pos)
            print(f"⬆️ تحريك للأمام: ({new_pos.x:.2f}, {new_pos.y:.2f})")

    def move_selected_backward(self):
        """تحريك الكائن المحدد للخلف"""
        if self.selected_object and self.movement_mode == 'keyboard':
            current_pos = self.selected_object.getPos()
            new_pos = Point3(current_pos.x, current_pos.y - self.movement_speed, current_pos.z)
            self.selected_object.setPos(new_pos)
            print(f"⬇️ تحريك للخلف: ({new_pos.x:.2f}, {new_pos.y:.2f})")

    def move_selected_left(self):
        """تحريك الكائن المحدد لليسار"""
        if self.selected_object and self.movement_mode == 'keyboard':
            current_pos = self.selected_object.getPos()
            new_pos = Point3(current_pos.x - self.movement_speed, current_pos.y, current_pos.z)
            self.selected_object.setPos(new_pos)
            print(f"⬅️ تحريك لليسار: ({new_pos.x:.2f}, {new_pos.y:.2f})")

    def move_selected_right(self):
        """تحريك الكائن المحدد لليمين"""
        if self.selected_object and self.movement_mode == 'keyboard':
            current_pos = self.selected_object.getPos()
            new_pos = Point3(current_pos.x + self.movement_speed, current_pos.y, current_pos.z)
            self.selected_object.setPos(new_pos)
            print(f"➡️ تحريك لليمين: ({new_pos.x:.2f}, {new_pos.y:.2f})")

    def get_movement_mode_text(self):
        """الحصول على نص وضع التحريك الحالي"""
        if self.movement_mode == 'keyboard':
            return "⌨️ المفاتيح (W/A/S/D)"
        elif self.movement_mode == 'drag':
            return "🖱️ السحب بالماوس"
        else:
            return "❌ غير محدد"

    def create_sidebar(self):
        """إنشاء الصفحة الجانبية مع أزرار الأثاث"""
        try:
            import tkinter as tk
            from tkinter import ttk
            import threading

            def create_sidebar_window():
                # إنشاء نافذة الصفحة الجانبية
                self.sidebar_window = tk.Tk()
                self.sidebar_window.title("🪑 أدوات الأثاث")
                self.sidebar_window.geometry("250x600+50+50")  # عرض × ارتفاع + موضع X + موضع Y
                self.sidebar_window.configure(bg='#f0f0f0')
                self.sidebar_window.resizable(False, True)

                # إعداد الخط
                arabic_font = ('Arial', 12)

                # عنوان الصفحة
                title_label = tk.Label(
                    self.sidebar_window,
                    text="🏠 أدوات التصميم ثلاثي الأبعاد",
                    font=('Arial', 14, 'bold'),
                    bg='#2c3e50',
                    fg='white',
                    pady=10
                )
                title_label.pack(fill='x', padx=5, pady=5)

                # قسم الأثاث
                furniture_frame = tk.LabelFrame(
                    self.sidebar_window,
                    text="🪑 الأثاث",
                    font=arabic_font,
                    bg='#ecf0f1',
                    fg='#2c3e50',
                    padx=10,
                    pady=10
                )
                furniture_frame.pack(fill='x', padx=10, pady=5)

                # زر إضافة كرسي عادي
                chair_btn = tk.Button(
                    furniture_frame,
                    text="🪑 إضافة كرسي عادي",
                    font=arabic_font,
                    bg='#3498db',
                    fg='white',
                    activebackground='#2980b9',
                    activeforeground='white',
                    relief='raised',
                    bd=2,
                    pady=8,
                    command=self.sidebar_add_chair
                )
                chair_btn.pack(fill='x', pady=3)

                # معلومات الكرسي العادي
                chair_info = tk.Label(
                    furniture_frame,
                    text="📏 الحجم: 0.5 متر\n🎨 اللون: بني\n📦 النوع: OBJ بسيط",
                    font=('Arial', 9),
                    bg='#ecf0f1',
                    fg='#7f8c8d',
                    justify='right'
                )
                chair_info.pack(fill='x', pady=2)

                # زر إضافة كرسي خشبي قديم
                old_chair_btn = tk.Button(
                    furniture_frame,
                    text="🪑 إضافة كرسي خشبي قديم",
                    font=arabic_font,
                    bg='#8b4513',
                    fg='white',
                    activebackground='#654321',
                    activeforeground='white',
                    relief='raised',
                    bd=2,
                    pady=8,
                    command=self.sidebar_add_old_chair
                )
                old_chair_btn.pack(fill='x', pady=3)

                # معلومات الكرسي الخشبي القديم
                old_chair_info = tk.Label(
                    furniture_frame,
                    text="📏 الحجم: 0.5 متر\n🎨 اللون: خشبي قديم\n📦 النوع: FBX + 6 تكسشر متقدمة\n🌟 جودة استثنائية مع تفاصيل واقعية\n✨ Albedo, Normal, Metallic, Roughness, AO",
                    font=('Arial', 7),
                    bg='#ecf0f1',
                    fg='#7f8c8d',
                    justify='right'
                )
                old_chair_info.pack(fill='x', pady=2)

                # قسم التحكم
                control_frame = tk.LabelFrame(
                    self.sidebar_window,
                    text="🎮 التحكم",
                    font=arabic_font,
                    bg='#ecf0f1',
                    fg='#2c3e50',
                    padx=10,
                    pady=10
                )
                control_frame.pack(fill='x', padx=10, pady=5)

                # زر تغيير وضع التحريك
                movement_btn = tk.Button(
                    control_frame,
                    text="🔄 تغيير وضع التحريك",
                    font=arabic_font,
                    bg='#e74c3c',
                    fg='white',
                    activebackground='#c0392b',
                    activeforeground='white',
                    relief='raised',
                    bd=2,
                    pady=8,
                    command=self.sidebar_toggle_movement
                )
                movement_btn.pack(fill='x', pady=5)

                # عرض وضع التحريك الحالي
                self.movement_status_label = tk.Label(
                    control_frame,
                    text="🖱️ الوضع: السحب بالماوس",
                    font=('Arial', 10),
                    bg='#ecf0f1',
                    fg='#27ae60',
                    justify='right'
                )
                self.movement_status_label.pack(fill='x', pady=2)

                # قسم المعلومات
                info_frame = tk.LabelFrame(
                    self.sidebar_window,
                    text="ℹ️ معلومات",
                    font=arabic_font,
                    bg='#ecf0f1',
                    fg='#2c3e50',
                    padx=10,
                    pady=10
                )
                info_frame.pack(fill='x', padx=10, pady=5)

                # معلومات الاستخدام
                info_text = tk.Text(
                    info_frame,
                    height=8,
                    font=('Arial', 9),
                    bg='white',
                    fg='#2c3e50',
                    wrap='word',
                    relief='sunken',
                    bd=1
                )
                info_text.pack(fill='both', expand=True, pady=5)

                # إدراج النص
                info_content = """🎯 كيفية الاستخدام:

🪑 إضافة كرسي:
• انقر على زر "إضافة كرسي"
• انقر في أي مكان في العرض ثلاثي الأبعاد

🎮 تحريك الكائنات:
• انقر على الكائن لتحديده
• اسحبه بالماوس أو استخدم W/A/S/D

🔄 تغيير وضع التحريك:
• انقر على زر "تغيير وضع التحريك"
• أو اضغط Q في العرض ثلاثي الأبعاد"""

                info_text.insert('1.0', info_content)
                info_text.config(state='disabled')

                # زر الخروج
                exit_btn = tk.Button(
                    self.sidebar_window,
                    text="❌ إغلاق العارض",
                    font=arabic_font,
                    bg='#95a5a6',
                    fg='white',
                    activebackground='#7f8c8d',
                    activeforeground='white',
                    relief='raised',
                    bd=2,
                    pady=8,
                    command=self.sidebar_exit
                )
                exit_btn.pack(fill='x', padx=10, pady=10)

                # تشغيل النافذة
                self.sidebar_window.mainloop()

            # تشغيل الصفحة الجانبية في thread منفصل
            self.sidebar_thread = threading.Thread(target=create_sidebar_window, daemon=True)
            self.sidebar_thread.start()

            print("✅ تم إنشاء الصفحة الجانبية بنجاح")

        except Exception as e:
            print(f"خطأ في إنشاء الصفحة الجانبية: {e}")
            import traceback
            traceback.print_exc()

    def sidebar_add_chair(self):
        """إضافة كرسي عادي من الصفحة الجانبية"""
        self.adding_furniture = 'chair'
        print("🪑 وضع إضافة الكراسي العادية مفعل من الصفحة الجانبية")
        print("📍 انقر في أي مكان في العرض ثلاثي الأبعاد لإضافة كرسي عادي")

    def sidebar_add_old_chair(self):
        """إضافة كرسي خشبي قديم من الصفحة الجانبية"""
        self.adding_furniture = 'old_chair'
        print("🪑 وضع إضافة الكراسي الخشبية القديمة مفعل من الصفحة الجانبية")
        print("📍 انقر في أي مكان في العرض ثلاثي الأبعاد لإضافة كرسي خشبي قديم")

    def sidebar_toggle_movement(self):
        """تغيير وضع التحريك من الصفحة الجانبية"""
        self.toggle_movement_mode()
        # تحديث النص في الصفحة الجانبية
        if hasattr(self, 'movement_status_label') and self.movement_status_label:
            try:
                new_text = f"🎮 الوضع: {self.get_movement_mode_text()}"
                self.movement_status_label.config(text=new_text)
            except:
                pass

    def sidebar_exit(self):
        """الخروج من العارض من الصفحة الجانبية"""
        try:
            if self.sidebar_window:
                self.sidebar_window.destroy()
        except:
            pass
        self.exit_viewer()

    def add_furniture_at_mouse(self, mouse_x, mouse_y):
        """إضافة أثاث في موضع الماوس"""
        try:
            # تحويل إحداثيات الماوس إلى موضع على الشبكة
            from panda3d.core import CollisionRay, CollisionNode, CollisionTraverser, CollisionHandlerQueue, Plane, PlaneNode

            # إنشاء مستوى الأرض للتصادم
            ground_plane = Plane(Vec3(0, 0, 1), Point3(0, 0, 0))

            # إنشاء شعاع من الكاميرا عبر موضع الماوس
            picker_ray = CollisionRay()
            picker_ray.setFromLens(self.camNode, mouse_x, mouse_y)

            # حساب نقطة التقاطع مع مستوى الأرض
            intersection_point = Point3()
            if ground_plane.intersectsLine(intersection_point, picker_ray.getOrigin(), picker_ray.getDirection()):
                # إضافة الأثاث في هذا الموضع
                if self.adding_furniture == 'chair':
                    chair_data = {
                        'pos_m': [intersection_point.getX(), intersection_point.getY()],
                        'size_m': self.furniture_size['chair']
                    }
                    self.create_3d_chair(chair_data)
                    print(f"✅ تم إضافة كرسي عادي في الموضع: ({intersection_point.getX():.2f}, {intersection_point.getY():.2f})")
                elif self.adding_furniture == 'old_chair':
                    old_chair_data = {
                        'pos_m': [intersection_point.getX(), intersection_point.getY()],
                        'size_m': 0.5  # نفس حجم الكرسي العادي
                    }
                    self.create_3d_old_chair(old_chair_data)
                    print(f"✅ تم إضافة كرسي خشبي قديم في الموضع: ({intersection_point.getX():.2f}, {intersection_point.getY():.2f})")

                # إلغاء وضع الإضافة بعد إضافة قطعة واحدة
                self.adding_furniture = None
                print("💡 تم إلغاء وضع الإضافة. استخدم الصفحة الجانبية لإضافة المزيد")
            else:
                print("❌ لا يمكن تحديد موضع على الشبكة")

        except Exception as e:
            print(f"خطأ في إضافة الأثاث: {e}")
            import traceback
            traceback.print_exc()

    def setup_lights(self):
        from panda3d.core import AmbientLight, DirectionalLight

        # إضاءة محيطة أقوى
        ambient_light = AmbientLight("ambient_light")
        ambient_light.setColor(VBase4(0.5, 0.5, 0.5, 1))  # زيادة شدة الإضاءة المحيطة
        self.ambient_light_node = self.render.attachNewNode(ambient_light)
        self.render.setLight(self.ambient_light_node)

        # إضاءة اتجاهية أقوى لإبراز الجدران العالية
        directional_light = DirectionalLight("directional_light")
        directional_light.setColor(VBase4(1.0, 1.0, 1.0, 1))  # إضاءة أقوى
        directional_light.setDirection(Vec3(-1, -1, -3))  # تغيير اتجاه الإضاءة لإبراز الارتفاع العالي
        self.directional_light_node = self.render.attachNewNode(directional_light)
        self.render.setLight(self.directional_light_node)

        # إضافة إضاءة اتجاهية ثانية من اتجاه مختلف
        directional_light2 = DirectionalLight("directional_light2")
        directional_light2.setColor(VBase4(0.6, 0.6, 0.6, 1))
        directional_light2.setDirection(Vec3(1, 1, -1))
        self.directional_light_node2 = self.render.attachNewNode(directional_light2)
        self.render.setLight(self.directional_light_node2)

        # إضاءة جانبية لإظهار سمك الجدران
        side_light = DirectionalLight("side_light")
        side_light.setColor(VBase4(0.5, 0.5, 0.5, 1))
        side_light.setDirection(Vec3(1, 0, -0.5))  # من الجانب لإظهار السمك
        self.side_light_node = self.render.attachNewNode(side_light)
        self.render.setLight(self.side_light_node)

        # إضاءة من الأعلى لإبراز ارتفاع الجدران العالية
        top_light = DirectionalLight("top_light")
        top_light.setColor(VBase4(0.7, 0.7, 0.7, 1))
        top_light.setDirection(Vec3(0, 0, -1))  # من الأعلى إلى الأسفل
        self.top_light_node = self.render.attachNewNode(top_light)
        self.render.setLight(self.top_light_node)

    def setup_collision_system(self):
        """إعداد نظام اكتشاف النقر والاصطدام المحسن"""
        try:
            from panda3d.core import CollisionTraverser, CollisionHandlerQueue, CollisionRay, CollisionNode, GeomNode, BitMask32

            # إنشاء نظام اكتشاف النقر
            self.picker_traverser = CollisionTraverser('pickerTraverser')
            self.picker_handler = CollisionHandlerQueue()

            # إنشاء شعاع النقر
            self.picker_ray = CollisionRay()
            picker_node = CollisionNode('mouseRay')
            picker_node.addSolid(self.picker_ray)
            picker_node.setFromCollideMask(GeomNode.getDefaultCollideMask())
            picker_node.setIntoCollideMask(BitMask32.allOff())

            # ربط الشعاع بالكاميرا
            self.picker_ray_node_path = self.camera.attachNewNode(picker_node)
            self.picker_traverser.addCollider(self.picker_ray_node_path, self.picker_handler)

            print("✅ تم إعداد نظام اكتشاف النقر المحسن")

        except Exception as e:
            print(f"خطأ في إعداد نظام الاصطدام: {e}")
            import traceback
            traceback.print_exc()

    def create_floor(self):
        # إنشاء أرضية بسيطة
        floor_size = 100
        floor_color = (0.8, 0.8, 0.8, 1)

        # إنشاء شبكة الأرضية
        lines = LineSegs()
        lines.setThickness(1)
        lines.setColor(*floor_color)

        # رسم خطوط الشبكة
        for i in range(-floor_size, floor_size + 1, 5):
            lines.moveTo(i, -floor_size, 0)
            lines.drawTo(i, floor_size, 0)
            lines.moveTo(-floor_size, i, 0)
            lines.drawTo(floor_size, i, 0)

        # إنشاء عقدة الشبكة وإضافتها للمشهد
        floor_node = lines.create()
        self.floor = self.render.attachNewNode(floor_node)

        # إضافة مستوى أرضي شفاف لتحسين المظهر
        from panda3d.core import CardMaker
        cm = CardMaker("floor_plane")
        cm.setFrame(-floor_size, floor_size, -floor_size, floor_size)
        floor_plane = self.render.attachNewNode(cm.generate())
        floor_plane.setP(-90)  # توجيه المستوى أفقيًا
        floor_plane.setZ(-0.01)  # وضعه تحت الشبكة قليلاً
        floor_plane.setTransparency(1)
        floor_plane.setColor(0.95, 0.95, 0.98, 0.4)  # لون أبيض فاتح شفاف لتباين أفضل

        # محاور الإحداثيات مخفية تماماً
        self.axes = None  # متغير لحفظ المحاور
        self.axes_visible = False  # حالة إظهار المحاور
        # المحاور مخفية نهائياً لتحسين المظهر

    def load_design_from_file(self, file_path):
        try:
            print(f"جاري محاولة تحميل التصميم من: {file_path}")
            with open(file_path, 'r', encoding='utf-8') as f:
                design_data = json.load(f)
                print(f"تم قراءة ملف التصميم بنجاح")
                print(f"محتويات الملف: {design_data.keys()}")

                # حفظ بيانات التصميم في الكائن للاستخدام في دوال أخرى
                self.design_data = design_data
                print(f"💾 تم حفظ بيانات التصميم في الكائن")

            # تحميل الجدران
            if 'walls' in design_data:
                print(f"📐 عدد الجدران في الملف: {len(design_data['walls'])}")
                print("=" * 50)
                walls_data = []
                for i, wall in enumerate(design_data['walls']):
                    print(f"🧱 جدار رقم {i+1}:")
                    print(f"  📍 بداية: ({wall.get('start_m', [0,0])[0]:.2f}, {wall.get('start_m', [0,0])[1]:.2f})")
                    print(f"  📍 نهاية: ({wall.get('end_m', [0,0])[0]:.2f}, {wall.get('end_m', [0,0])[1]:.2f})")
                    print(f"  📏 الطول: {wall.get('length_m', 0):.2f} متر")

                    # إضافة ارتفاع افتراضي إذا لم يكن موجودًا
                    if 'height_m' not in wall:
                        wall['height_m'] = 3.0
                        print(f"  📐 الارتفاع: {wall['height_m']:.2f} متر (افتراضي)")
                    else:
                        print(f"  📐 الارتفاع: {wall.get('height_m'):.2f} متر")

                    self.create_3d_wall(wall)
                    walls_data.append(wall)
                    print("-" * 30)

                # إنشاء قطع الربط في الزوايا
                self.create_wall_corners(walls_data)
                print("=" * 50)
            else:
                print("⚠️ لا توجد جدران في ملف التصميم")

            # تحميل الكراسي
            if 'chairs' in design_data:
                print(f"🪑 عدد الكراسي في الملف: {len(design_data['chairs'])}")
                for i, chair in enumerate(design_data['chairs']):
                    print(f"🪑 كرسي رقم {i+1}:")
                    print(f"  📍 الموضع: ({chair.get('pos_m', [0,0])[0]:.2f}, {chair.get('pos_m', [0,0])[1]:.2f})")
                    print(f"  📏 الحجم: {chair.get('size_m', 0.6):.2f} متر")
                    self.create_3d_chair(chair)
                    print("-" * 30)
            else:
                print("⚠️ لا توجد كراسي في ملف التصميم")

            # إنشاء الأبواب والشبابيك
            doors_and_windows = []

            # البحث عن الأبواب والشبابيك في البيانات
            if isinstance(design_data, dict):
                # إذا كانت البيانات في شكل قاموس مع مفاتيح منفصلة
                doors = design_data.get('doors', [])
                windows = design_data.get('windows', [])
                doors_and_windows = doors + windows
            elif isinstance(design_data, list):
                # إذا كانت البيانات في شكل قائمة
                doors_and_windows = [elem for elem in design_data if elem.get('type') in ['door', 'window']]

            if doors_and_windows:
                print(f"🚪 إنشاء {len(doors_and_windows)} باب/نافذة...")
                for element in doors_and_windows:
                    if element.get('type') == 'door':
                        self.create_3d_door(element)
                    elif element.get('type') == 'window':
                        self.create_3d_window(element)
            else:
                print("⚠️ لا توجد أبواب أو شبابيك في ملف التصميم")

            # توجيه الكاميرا تلقائياً نحو المبنى بعد التحميل
            self.auto_position_camera()

            print(f"تم تحميل التصميم من: {file_path}")
        except Exception as e:
            print(f"خطأ في تحميل التصميم: {e}")
            import traceback
            traceback.print_exc()

    def calculate_building_center(self):
        """حساب مركز المبنى بناءً على الجدران والكائنات"""
        try:
            all_points = []

            # جمع نقاط الجدران
            for element in self.design_elements:
                if element.getName().startswith("wall"):
                    pos = element.getPos()
                    all_points.append((pos.x, pos.y))

            # جمع نقاط الكراسي
            for chair in self.chair_nodes:
                pos = chair.getPos()
                all_points.append((pos.x, pos.y))

            if not all_points:
                return Point3(0, 0, 0)

            # حساب المركز
            center_x = sum(point[0] for point in all_points) / len(all_points)
            center_y = sum(point[1] for point in all_points) / len(all_points)

            # حساب الحدود للحصول على أفضل مسافة للكاميرا
            min_x = min(point[0] for point in all_points)
            max_x = max(point[0] for point in all_points)
            min_y = min(point[1] for point in all_points)
            max_y = max(point[1] for point in all_points)

            # حساب أبعاد المبنى
            building_width = max_x - min_x
            building_height = max_y - min_y
            building_size = max(building_width, building_height)

            return Point3(center_x, center_y, 2), building_size

        except Exception as e:
            print(f"خطأ في حساب مركز المبنى: {e}")
            return Point3(0, 0, 0), 10

    def auto_position_camera(self):
        """توجيه الكاميرا تلقائياً نحو المبنى"""
        try:
            if self.auto_positioned:
                return

            building_center, building_size = self.calculate_building_center()

            if building_center.x == 0 and building_center.y == 0:
                return

            # تحديد هدف الكاميرا
            self.cam_target = building_center

            # تحديد مسافة مناسبة للكاميرا بناءً على حجم المبنى
            optimal_distance = max(15, building_size * 1.5)
            self.cam_distance = optimal_distance

            # تحديد زاوية مثالية لعرض المبنى
            self.cam_heading = 45  # زاوية 45 درجة لعرض أفضل
            self.cam_pitch = -25   # زاوية نظر من الأعلى قليلاً

            # تطبيق موضع الكاميرا الجديد
            self.update_camera_position()

            self.auto_positioned = True

            print(f"🎯 تم توجيه الكاميرا تلقائياً نحو المبنى:")
            print(f"   📍 مركز المبنى: ({building_center.x:.1f}, {building_center.y:.1f})")
            print(f"   📏 حجم المبنى: {building_size:.1f} متر")
            print(f"   📷 مسافة الكاميرا: {optimal_distance:.1f} متر")
            print(f"   🔄 زاوية العرض: {self.cam_heading}° أفقي، {self.cam_pitch}° عمودي")

        except Exception as e:
            print(f"خطأ في توجيه الكاميرا: {e}")
            import traceback
            traceback.print_exc()

    def create_3d_wall(self, wall_data):
        try:
            # استخراج بيانات الجدار
            start_m = wall_data['start_m']
            end_m = wall_data['end_m']

            # استخراج ارتفاع الجدار (ارتفاع عالي جداً للوضوح)
            wall_height = float(wall_data.get('height_m', 8.0))  # ارتفاع 8 أمتار - عالي جداً وواضح
            print(f"إنشاء جدار بارتفاع: {wall_height} متر")

            # تحويل إحداثيات 2D إلى 3D
            start_3d = Point3(start_m[0], start_m[1], 0)
            end_3d = Point3(end_m[0], end_m[1], 0)

            # حساب طول الجدار واتجاهه
            wall_vec = end_3d - start_3d
            wall_length = wall_vec.length()
            wall_thickness = 0.25  # سمك الجدار بالمتر (سمك واقعي - 25 سم)

            # البحث عن الأبواب والشبابيك على هذا الجدار
            wall_openings = self.find_wall_openings(wall_data)
            print(f"🔍 تم العثور على {len(wall_openings)} فتحة في الجدار")

            # إنشاء عقدة الجدار
            wall_node = self.render.attachNewNode("wall")
            wall_node.setPos(start_3d)

            # حساب زاوية الدوران
            heading = math.degrees(math.atan2(wall_vec.getY(), wall_vec.getX()))
            wall_node.setH(heading)

            if wall_openings:
                print(f"🚪 تم العثور على {len(wall_openings)} فتحة في الجدار")
                # إنشاء جدار مع تجاويف حقيقية
                self.create_wall_with_openings(wall_node, wall_length, wall_height, wall_thickness, wall_openings, start_m, end_m)
            else:
                # إنشاء جدار عادي بدون تجاويف
                self.create_realistic_3d_wall(wall_node, wall_length, wall_height, wall_thickness)

            # إضافة حواف سوداء محسنة للجدار لتحسين الرؤية والوضوح
            self.add_wall_edges(start_3d, end_3d, wall_height, wall_thickness)

            # إضافة الجدار إلى قائمة العناصر
            self.design_elements.append(wall_node)

            print(f"✅ تم إنشاء جدار احترافي:")
            print(f"  📏 الطول: {wall_length:.2f} متر")
            print(f"  🏗️ الارتفاع: {wall_height:.2f} متر (عالي وواضح!)")
            print(f"  🧱 السمك: {wall_thickness*100:.0f} سم (سمك واقعي)")
            print(f"  📍 من النقطة ({start_m[0]:.2f}, {start_m[1]:.2f}) إلى ({end_m[0]:.2f}, {end_m[1]:.2f})")
            print(f"  🎨 ألوان فاتحة مع حواف سوداء واضحة")
            print(f"  ✨ جدار احترافي مع حواف محددة للوضوح")

        except Exception as e:
            print(f"خطأ في إنشاء الجدار: {e}")
            import traceback
            traceback.print_exc()

    def create_realistic_3d_wall(self, wall_node, wall_length, wall_height, wall_thickness):
        """إنشاء جدار ثلاثي الأبعاد حقيقي مع تفاصيل واقعية"""
        try:
            from panda3d.core import CardMaker, Material

            # ألوان أكثر وضوحاً ووضوحاً للجدار
            front_color = (0.95, 0.95, 0.95, 1)    # أبيض فاتح جداً للوجه الأمامي
            back_color = (0.90, 0.90, 0.90, 1)     # أبيض مائل للرمادي للوجه الخلفي
            side_color = (0.85, 0.85, 0.85, 1)     # رمادي فاتح للجوانب
            top_color = (0.98, 0.98, 0.98, 1)      # أبيض تقريباً للوجه العلوي
            bottom_color = (0.80, 0.80, 0.80, 1)   # رمادي متوسط للوجه السفلي

            # إنشاء مواد محسنة لكل وجه
            def create_wall_material(base_color, shininess=15.0):
                material = Material()
                material.setAmbient((0.6, 0.6, 0.6, 1))  # إضاءة محيطة أقوى
                material.setDiffuse(base_color)
                material.setSpecular((0.4, 0.4, 0.4, 1))  # انعكاس أقوى
                material.setShininess(shininess)
                material.setEmission((0.1, 0.1, 0.1, 1))  # إضافة إشعاع خفيف
                return material

            # 1. الوجه الأمامي (الرئيسي)
            cm_front = CardMaker("wall_front")
            cm_front.setFrame(0, wall_length, 0, wall_height)
            front_wall = wall_node.attachNewNode(cm_front.generate())
            front_wall.setPos(0, wall_thickness/2, 0)
            front_wall.setColor(*front_color)
            front_wall.setMaterial(create_wall_material(front_color, 20.0))
            front_wall.setTwoSided(True)  # مرئي من الجهتين لحل مشكلة الدوران

            # 2. الوجه الخلفي (مع تصحيح الاتجاه)
            cm_back = CardMaker("wall_back")
            cm_back.setFrame(wall_length, 0, 0, wall_height)  # عكس الاتجاه للوجه الخلفي
            back_wall = wall_node.attachNewNode(cm_back.generate())
            back_wall.setPos(0, -wall_thickness/2, 0)
            back_wall.setColor(*back_color)
            back_wall.setMaterial(create_wall_material(back_color, 12.0))
            back_wall.setTwoSided(True)  # مرئي من الجهتين

            # 3. الجانب الأيسر (مصحح)
            cm_left = CardMaker("wall_left")
            cm_left.setFrame(0, wall_thickness, 0, wall_height)
            left_wall = wall_node.attachNewNode(cm_left.generate())
            left_wall.setPos(0, -wall_thickness/2, 0)
            left_wall.setH(90)
            left_wall.setColor(*side_color)
            left_wall.setMaterial(create_wall_material(side_color, 10.0))
            left_wall.setTwoSided(True)  # مرئي من الجهتين

            # 4. الجانب الأيمن (مصحح)
            cm_right = CardMaker("wall_right")
            cm_right.setFrame(wall_thickness, 0, 0, wall_height)  # عكس الاتجاه للجانب الأيمن
            right_wall = wall_node.attachNewNode(cm_right.generate())
            right_wall.setPos(wall_length, -wall_thickness/2, 0)
            right_wall.setH(90)
            right_wall.setColor(*side_color)
            right_wall.setMaterial(create_wall_material(side_color, 10.0))
            right_wall.setTwoSided(True)  # مرئي من الجهتين

            # 5. الوجه العلوي (مصحح)
            cm_top = CardMaker("wall_top")
            cm_top.setFrame(0, wall_length, 0, wall_thickness)
            top_wall = wall_node.attachNewNode(cm_top.generate())
            top_wall.setPos(0, -wall_thickness/2, wall_height)
            top_wall.setP(-90)
            top_wall.setColor(*top_color)
            top_wall.setMaterial(create_wall_material(top_color, 25.0))
            top_wall.setTwoSided(True)  # مرئي من الجهتين

            # 6. الوجه السفلي (مصحح)
            cm_bottom = CardMaker("wall_bottom")
            cm_bottom.setFrame(wall_length, 0, 0, wall_thickness)  # عكس الاتجاه للوجه السفلي
            bottom_wall = wall_node.attachNewNode(cm_bottom.generate())
            bottom_wall.setPos(0, -wall_thickness/2, 0)
            bottom_wall.setP(90)
            bottom_wall.setColor(*bottom_color)
            bottom_wall.setMaterial(create_wall_material(bottom_color, 5.0))
            bottom_wall.setTwoSided(True)  # مرئي من الجهتين

            # إضافة تأثيرات بصرية محسنة للواقعية
            from panda3d.core import RenderState, ShadeModelAttrib, CullFaceAttrib, AntialiasAttrib

            # تطبيق تظليل ناعم لجميع الأوجه
            smooth_shade = RenderState.make(ShadeModelAttrib.make(ShadeModelAttrib.M_smooth))
            wall_node.setState(smooth_shade)

            # إيقاف إزالة الوجوه الخلفية لضمان رؤية جميع الأوجه من كل الزوايا
            no_cull = RenderState.make(CullFaceAttrib.make(CullFaceAttrib.M_cull_none))
            wall_node.setState(no_cull)

            # تطبيق تنعيم الحواف لمظهر احترافي
            antialiasing = RenderState.make(AntialiasAttrib.make(AntialiasAttrib.M_auto))
            wall_node.setState(antialiasing)

            # تحسين عرض الجدار بإعدادات إضافية
            from panda3d.core import DepthTestAttrib, DepthWriteAttrib

            # ضمان عرض الجدار بشكل صحيح مع العمق
            depth_test = RenderState.make(DepthTestAttrib.make(DepthTestAttrib.M_less_equal))
            wall_node.setState(depth_test)

            depth_write = RenderState.make(DepthWriteAttrib.make(DepthWriteAttrib.M_on))
            wall_node.setState(depth_write)

            print(f"🎯 جدار احترافي: سمك {wall_thickness*100:.0f} سم × ارتفاع {wall_height:.1f} متر - مع حواف سوداء!")

        except Exception as e:
            print(f"خطأ في إنشاء الجدار ثلاثي الأبعاد: {e}")
            import traceback
            traceback.print_exc()

    def find_wall_openings(self, wall_data):
        """البحث عن الأبواب والشبابيك على الجدار المحدد"""
        try:
            openings = []
            wall_start = wall_data['start_m']
            wall_end = wall_data['end_m']

            # البحث في الأبواب والشبابيك المحفوظة في التصميم
            if hasattr(self, 'design_data') and self.design_data:
                doors = self.design_data.get('doors', [])
                windows = self.design_data.get('windows', [])
                print(f"🔍 فحص الجدار من ({wall_start[0]:.2f}, {wall_start[1]:.2f}) إلى ({wall_end[0]:.2f}, {wall_end[1]:.2f})")
                print(f"🔍 عدد الأبواب المتاحة: {len(doors)}")
                print(f"🔍 عدد الشبابيك المتاحة: {len(windows)}")

                # البحث في الأبواب
                for i, door in enumerate(doors):
                    print(f"🚪 فحص الباب {i+1}: موضع ({door['pos_m'][0]:.2f}, {door['pos_m'][1]:.2f})")
                    if self.is_opening_on_wall(door, wall_start, wall_end):
                        door_info = {
                            'type': 'door',
                            'pos_m': door['pos_m'],
                            'width_m': door['width_m'],
                            'height_m': door.get('height_m', 2.2),
                            'bottom_height_m': door.get('bottom_height_m', 0.0)
                        }
                        openings.append(door_info)
                        print(f"✅ تم العثور على باب في الموضع ({door['pos_m'][0]:.2f}, {door['pos_m'][1]:.2f})")
                    else:
                        print(f"❌ الباب {i+1} ليس على هذا الجدار")

                # البحث في الشبابيك
                for i, window in enumerate(windows):
                    print(f"🪟 فحص النافذة {i+1}: موضع ({window['pos_m'][0]:.2f}, {window['pos_m'][1]:.2f})")
                    if self.is_opening_on_wall(window, wall_start, wall_end):
                        window_info = {
                            'type': 'window',
                            'pos_m': window['pos_m'],
                            'width_m': window['width_m'],
                            'height_m': window.get('height_m', 1.5),
                            'bottom_height_m': window.get('bottom_height_m', 1.0)
                        }
                        openings.append(window_info)
                        print(f"✅ تم العثور على نافذة في الموضع ({window['pos_m'][0]:.2f}, {window['pos_m'][1]:.2f})")
                    else:
                        print(f"❌ النافذة {i+1} ليست على هذا الجدار")
            else:
                print("⚠️ لا توجد بيانات تصميم أو لا توجد أبواب/شبابيك")

            return openings

        except Exception as e:
            print(f"خطأ في البحث عن فتحات الجدار: {e}")
            return []

    def is_opening_on_wall(self, opening, wall_start, wall_end):
        """التحقق من وجود الفتحة على الجدار المحدد"""
        try:
            opening_pos = opening['pos_m']

            print(f"🔍 فحص الفتحة في ({opening_pos[0]:.2f}, {opening_pos[1]:.2f})")
            print(f"🔍 الجدار من ({wall_start[0]:.2f}, {wall_start[1]:.2f}) إلى ({wall_end[0]:.2f}, {wall_end[1]:.2f})")

            # حساب المسافة من نقطة الفتحة إلى خط الجدار
            # استخدام معادلة المسافة من نقطة إلى خط
            wall_vec = [wall_end[0] - wall_start[0], wall_end[1] - wall_start[1]]
            wall_length = (wall_vec[0]**2 + wall_vec[1]**2)**0.5

            print(f"🔍 طول الجدار: {wall_length:.2f}م")

            if wall_length == 0:
                print("❌ طول الجدار صفر")
                return False

            # تطبيع متجه الجدار
            wall_unit = [wall_vec[0]/wall_length, wall_vec[1]/wall_length]

            # متجه من بداية الجدار إلى الفتحة
            to_opening = [opening_pos[0] - wall_start[0], opening_pos[1] - wall_start[1]]

            # إسقاط الفتحة على الجدار
            projection = to_opening[0] * wall_unit[0] + to_opening[1] * wall_unit[1]

            print(f"🔍 إسقاط الفتحة على الجدار: {projection:.2f}م")

            # التحقق من أن الإسقاط ضمن طول الجدار
            if 0 <= projection <= wall_length:
                # حساب المسافة العمودية من الفتحة إلى الجدار
                projected_point = [
                    wall_start[0] + projection * wall_unit[0],
                    wall_start[1] + projection * wall_unit[1]
                ]

                distance = ((opening_pos[0] - projected_point[0])**2 +
                           (opening_pos[1] - projected_point[1])**2)**0.5

                print(f"🔍 المسافة العمودية من الفتحة إلى الجدار: {distance:.3f}م")

                # إذا كانت المسافة صغيرة جداً، فالفتحة على الجدار
                tolerance = 0.5  # زيادة التسامح إلى 50 سم للاختبار
                is_on_wall = distance < tolerance

                if is_on_wall:
                    print(f"✅ الفتحة على الجدار (مسافة {distance:.3f}م < {tolerance}م)")
                else:
                    print(f"❌ الفتحة ليست على الجدار (مسافة {distance:.3f}م >= {tolerance}م)")

                return is_on_wall
            else:
                print(f"❌ الإسقاط خارج نطاق الجدار ({projection:.2f} ليس بين 0 و {wall_length:.2f})")
                return False

        except Exception as e:
            print(f"خطأ في التحقق من موضع الفتحة: {e}")
            import traceback
            traceback.print_exc()
            return False

    def create_wall_with_openings(self, wall_node, wall_length, wall_height, wall_thickness, openings, wall_start, wall_end):
        """إنشاء جدار مع تجاويف حقيقية للأبواب والشبابيك"""
        try:
            print(f"🏗️ إنشاء جدار مع {len(openings)} تجويف")

            # ترتيب الفتحات حسب الموضع على الجدار
            # استخدام معلومات بداية ونهاية الجدار الصحيحة للحساب الدقيق
            sorted_openings = self.sort_openings_by_position(openings, wall_start, wall_end)

            # إنشاء أجزاء الجدار بين الفتحات
            wall_segments = self.create_wall_segments(wall_length, wall_height, wall_thickness, sorted_openings)

            # إنشاء كل جزء من الجدار
            for i, segment in enumerate(wall_segments):
                segment_node = self.create_wall_segment_geometry(
                    wall_node, segment, f"wall_segment_{i}"
                )
                self.apply_wall_material_to_segment(segment_node)

            # إنشاء إطارات ملونة للفتحات
            self.create_opening_frames(wall_node, sorted_openings, wall_height, wall_thickness)

            print(f"✅ تم إنشاء {len(wall_segments)} جزء من الجدار مع {len(openings)} تجويف")

        except Exception as e:
            print(f"خطأ في إنشاء الجدار مع التجاويف: {e}")
            import traceback
            traceback.print_exc()

    def sort_openings_by_position(self, openings, wall_start, wall_end):
        """ترتيب الفتحات حسب موضعها على الجدار مع حساب دقيق للإسقاط"""
        try:
            # حساب موضع كل فتحة على الجدار بدقة أكبر
            wall_vec = Vec3(wall_end[0] - wall_start[0], wall_end[1] - wall_start[1], 0)
            wall_length = wall_vec.length()

            print(f"🔍 حساب مواضع الفتحات على الجدار:")
            print(f"   📍 بداية الجدار: ({wall_start[0]:.2f}, {wall_start[1]:.2f})")
            print(f"   📍 نهاية الجدار: ({wall_end[0]:.2f}, {wall_end[1]:.2f})")
            print(f"   📏 طول الجدار: {wall_length:.2f}م")

            for opening in openings:
                opening_pos = opening['pos_m']

                # حساب الإسقاط الدقيق للفتحة على خط الجدار
                # المتجه من بداية الجدار إلى الفتحة
                opening_vec = Vec3(opening_pos[0] - wall_start[0], opening_pos[1] - wall_start[1], 0)

                # حساب الإسقاط النقطي (dot product) للحصول على المسافة على الجدار
                if wall_length > 0:
                    wall_unit_vec = wall_vec / wall_length
                    projection_distance = opening_vec.dot(wall_unit_vec)

                    # التأكد من أن الإسقاط ضمن حدود الجدار
                    projection_distance = max(0, min(wall_length, projection_distance))

                    opening['wall_position'] = projection_distance
                else:
                    opening['wall_position'] = 0

                print(f"   🔍 فتحة {opening['type']}:")
                print(f"     📍 موضع الفتحة: ({opening_pos[0]:.2f}, {opening_pos[1]:.2f})")
                print(f"     📏 إسقاط على الجدار: {opening['wall_position']:.2f}م")

            # ترتيب الفتحات حسب الموضع من اليسار إلى اليمين
            sorted_openings = sorted(openings, key=lambda x: x['wall_position'])

            print(f"📋 ترتيب الفتحات النهائي:")
            for i, opening in enumerate(sorted_openings):
                print(f"  {i+1}. {opening['type']} في الموضع {opening['wall_position']:.2f}م")

            return sorted_openings

        except Exception as e:
            print(f"خطأ في ترتيب الفتحات: {e}")
            import traceback
            traceback.print_exc()
            return openings

    def create_wall_segments(self, wall_length, wall_height, wall_thickness, sorted_openings):
        """إنشاء أجزاء الجدار بين الفتحات مع تشخيص مفصل للفتحات المتعددة"""
        try:
            segments = []
            current_pos = 0

            print(f"🏗️ إنشاء أجزاء الجدار:")
            print(f"   📏 طول الجدار الكامل: {wall_length:.2f}م")
            print(f"   📐 ارتفاع الجدار: {wall_height:.2f}م")
            print(f"   🧱 سمك الجدار: {wall_thickness:.2f}م")
            print(f"   🔢 عدد الفتحات: {len(sorted_openings)}")

            for i, opening in enumerate(sorted_openings):
                opening_start = opening['wall_position'] - opening['width_m'] / 2
                opening_end = opening['wall_position'] + opening['width_m'] / 2
                opening_type = opening['type']
                opening_width = opening['width_m']
                opening_height = opening['height_m']
                opening_bottom = opening['bottom_height_m']

                print(f"   🔍 فتحة {i+1} ({opening_type}):")
                print(f"     📍 مركز الفتحة: {opening['wall_position']:.2f}م")
                print(f"     📏 عرض الفتحة: {opening_width:.2f}م")
                print(f"     📐 ارتفاع الفتحة: {opening_height:.2f}م")
                print(f"     ⬆️ ارتفاع من الأرض: {opening_bottom:.2f}م")
                print(f"     ↔️ من {opening_start:.2f}م إلى {opening_end:.2f}م")

                # إنشاء جزء قبل الفتحة
                if current_pos < opening_start:
                    segment_width = opening_start - current_pos
                    print(f"     ➡️ إنشاء جزء جدار قبل الفتحة: من {current_pos:.2f}م إلى {opening_start:.2f}م (عرض {segment_width:.2f}م)")

                    segment = {
                        'start': current_pos,
                        'end': opening_start,
                        'height': wall_height,
                        'thickness': wall_thickness
                    }
                    segments.append(segment)

                # إنشاء جزء فوق الفتحة (إذا كانت الفتحة لا تصل للسقف)
                opening_top = opening_bottom + opening_height
                if opening_top < wall_height:
                    segment_height = wall_height - opening_top
                    print(f"     ⬆️ إنشاء جزء جدار فوق الفتحة: ارتفاع {segment_height:.2f}م (من {opening_top:.2f}م إلى {wall_height:.2f}م)")

                    segment = {
                        'start': opening_start,
                        'end': opening_end,
                        'height': segment_height,
                        'thickness': wall_thickness,
                        'bottom_offset': opening_top
                    }
                    segments.append(segment)

                # إنشاء جزء تحت الفتحة (إذا كانت الفتحة لا تبدأ من الأرض)
                if opening_bottom > 0:
                    print(f"     ⬇️ إنشاء جزء جدار تحت الفتحة: ارتفاع {opening_bottom:.2f}م (من 0م إلى {opening_bottom:.2f}م)")

                    segment = {
                        'start': opening_start,
                        'end': opening_end,
                        'height': opening_bottom,
                        'thickness': wall_thickness,
                        'bottom_offset': 0
                    }
                    segments.append(segment)

                current_pos = opening_end
                print(f"     ⏭️ الموضع الحالي بعد الفتحة: {current_pos:.2f}م")

            # إنشاء الجزء الأخير من الجدار
            if current_pos < wall_length:
                segment_width = wall_length - current_pos
                print(f"   ➡️ إنشاء الجزء الأخير من الجدار: من {current_pos:.2f}م إلى {wall_length:.2f}م (عرض {segment_width:.2f}م)")

                segment = {
                    'start': current_pos,
                    'end': wall_length,
                    'height': wall_height,
                    'thickness': wall_thickness
                }
                segments.append(segment)

            print(f"🏁 تم إنشاء {len(segments)} جزء من الجدار")
            return segments

        except Exception as e:
            print(f"خطأ في إنشاء أجزاء الجدار: {e}")
            import traceback
            traceback.print_exc()
            return []

    def create_wall_segment_geometry(self, wall_node, segment, segment_name):
        """إنشاء هندسة جزء من الجدار"""
        try:
            from panda3d.core import CardMaker

            segment_length = segment['end'] - segment['start']
            segment_height = segment['height']
            segment_thickness = segment['thickness']
            bottom_offset = segment.get('bottom_offset', 0)

            # إنشاء عقدة الجزء
            segment_node = wall_node.attachNewNode(segment_name)
            segment_node.setPos(segment['start'], 0, bottom_offset)

            # إنشاء الأوجه الستة للجزء
            faces = [
                # الوجه الأمامي
                {
                    'name': 'front',
                    'frame': (0, segment_length, 0, segment_height),
                    'pos': (0, segment_thickness/2, 0),
                    'rotation': (0, 0, 0),
                    'color': (0.95, 0.95, 0.95, 1)
                },
                # الوجه الخلفي
                {
                    'name': 'back',
                    'frame': (segment_length, 0, 0, segment_height),
                    'pos': (0, -segment_thickness/2, 0),
                    'rotation': (0, 0, 0),
                    'color': (0.90, 0.90, 0.90, 1)
                },
                # الجانب الأيسر
                {
                    'name': 'left',
                    'frame': (0, segment_thickness, 0, segment_height),
                    'pos': (0, -segment_thickness/2, 0),
                    'rotation': (90, 0, 0),
                    'color': (0.85, 0.85, 0.85, 1)
                },
                # الجانب الأيمن
                {
                    'name': 'right',
                    'frame': (segment_thickness, 0, 0, segment_height),
                    'pos': (segment_length, -segment_thickness/2, 0),
                    'rotation': (90, 0, 0),
                    'color': (0.85, 0.85, 0.85, 1)
                },
                # الوجه العلوي
                {
                    'name': 'top',
                    'frame': (0, segment_length, 0, segment_thickness),
                    'pos': (0, -segment_thickness/2, segment_height),
                    'rotation': (0, -90, 0),
                    'color': (0.98, 0.98, 0.98, 1)
                },
                # الوجه السفلي
                {
                    'name': 'bottom',
                    'frame': (segment_length, 0, 0, segment_thickness),
                    'pos': (0, -segment_thickness/2, 0),
                    'rotation': (0, 90, 0),
                    'color': (0.80, 0.80, 0.80, 1)
                }
            ]

            # إنشاء كل وجه
            for face in faces:
                cm = CardMaker(f"{segment_name}_{face['name']}")
                cm.setFrame(*face['frame'])
                face_node = segment_node.attachNewNode(cm.generate())
                face_node.setPos(*face['pos'])
                face_node.setH(face['rotation'][0])
                face_node.setP(face['rotation'][1])
                face_node.setR(face['rotation'][2])
                face_node.setColor(*face['color'])
                face_node.setTwoSided(True)

            return segment_node

        except Exception as e:
            print(f"خطأ في إنشاء هندسة جزء الجدار: {e}")
            return None

    def apply_wall_material_to_segment(self, segment_node):
        """تطبيق مواد الجدار على جزء من الجدار"""
        try:
            from panda3d.core import Material

            # إنشاء مادة الجدار
            material = Material()
            material.setAmbient((0.6, 0.6, 0.6, 1))
            material.setDiffuse((0.9, 0.9, 0.9, 1))
            material.setSpecular((0.3, 0.3, 0.3, 1))
            material.setShininess(15.0)

            # تطبيق المادة على الجزء
            if segment_node:
                segment_node.setMaterial(material)

        except Exception as e:
            print(f"خطأ في تطبيق مواد الجدار: {e}")

    def create_opening_frames(self, wall_node, openings, wall_height, wall_thickness):
        """إنشاء إطارات ملونة للفتحات"""
        try:
            for opening in openings:
                if opening['type'] == 'door':
                    self.create_door_frame_in_wall(
                        wall_node, opening, wall_height, wall_thickness
                    )
                elif opening['type'] == 'window':
                    self.create_window_frame_in_wall(
                        wall_node, opening, wall_height, wall_thickness
                    )

        except Exception as e:
            print(f"خطأ في إنشاء إطارات الفتحات: {e}")

    def create_door_frame_in_wall(self, wall_node, door_opening, wall_height, wall_thickness):
        """إنشاء إطار الباب الأحمر في الجدار"""
        try:
            from panda3d.core import CardMaker

            # معلومات الباب
            door_pos = door_opening['wall_position']
            door_width = door_opening['width_m']
            door_height = door_opening['height_m']
            door_bottom = door_opening['bottom_height_m']

            # إنشاء عقدة الإطار
            frame_node = wall_node.attachNewNode("door_frame")
            frame_node.setPos(door_pos - door_width/2, 0, door_bottom)

            # لون الإطار الأحمر
            frame_color = (0.8, 0.1, 0.1, 1)
            frame_thickness = 0.05

            # إنشاء إطارات الباب
            frames = [
                # الإطار الأيسر
                {
                    'name': 'left',
                    'frame': (-frame_thickness, 0, 0, door_height),
                    'pos': (0, 0, 0)
                },
                # الإطار الأيمن
                {
                    'name': 'right',
                    'frame': (0, frame_thickness, 0, door_height),
                    'pos': (door_width, 0, 0)
                },
                # الإطار العلوي
                {
                    'name': 'top',
                    'frame': (0, door_width, 0, frame_thickness),
                    'pos': (0, 0, door_height)
                }
            ]

            for frame in frames:
                cm = CardMaker(f"door_frame_{frame['name']}")
                cm.setFrame(*frame['frame'])
                frame_part = frame_node.attachNewNode(cm.generate())
                frame_part.setPos(*frame['pos'])
                frame_part.setColor(*frame_color)
                frame_part.setTwoSided(True)

            print(f"🚪 تم إنشاء إطار باب أحمر في الموضع {door_pos:.2f}")

        except Exception as e:
            print(f"خطأ في إنشاء إطار الباب: {e}")

    def create_window_frame_in_wall(self, wall_node, window_opening, wall_height, wall_thickness):
        """إنشاء إطار النافذة الأزرق في الجدار"""
        try:
            from panda3d.core import CardMaker

            # معلومات النافذة
            window_pos = window_opening['wall_position']
            window_width = window_opening['width_m']
            window_height = window_opening['height_m']
            window_bottom = window_opening['bottom_height_m']

            # إنشاء عقدة الإطار
            frame_node = wall_node.attachNewNode("window_frame")
            frame_node.setPos(window_pos - window_width/2, 0, window_bottom)

            # لون الإطار الأزرق
            frame_color = (0.1, 0.1, 0.8, 1)
            frame_thickness = 0.05

            # إنشاء إطارات النافذة
            frames = [
                # الإطار الأيسر
                {
                    'name': 'left',
                    'frame': (-frame_thickness, 0, 0, window_height),
                    'pos': (0, 0, 0)
                },
                # الإطار الأيمن
                {
                    'name': 'right',
                    'frame': (0, frame_thickness, 0, window_height),
                    'pos': (window_width, 0, 0)
                },
                # الإطار العلوي
                {
                    'name': 'top',
                    'frame': (0, window_width, 0, frame_thickness),
                    'pos': (0, 0, window_height)
                },
                # الإطار السفلي
                {
                    'name': 'bottom',
                    'frame': (0, window_width, 0, frame_thickness),
                    'pos': (0, 0, 0)
                }
            ]

            for frame in frames:
                cm = CardMaker(f"window_frame_{frame['name']}")
                cm.setFrame(*frame['frame'])
                frame_part = frame_node.attachNewNode(cm.generate())
                frame_part.setPos(*frame['pos'])
                frame_part.setColor(*frame_color)
                frame_part.setTwoSided(True)

            print(f"🪟 تم إنشاء إطار نافذة أزرق في الموضع {window_pos:.2f}")

        except Exception as e:
            print(f"خطأ في إنشاء إطار النافذة: {e}")

    def create_3d_chair(self, chair_data):
        """إنشاء كرسي ثلاثي الأبعاد باستخدام نموذج OBJ"""
        try:
            # استخراج بيانات الكرسي
            pos_m = chair_data['pos_m']
            size_m = float(chair_data.get('size_m', 0.5))

            print(f"إنشاء كرسي بحجم: {size_m} متر")

            # تحويل إحداثيات 2D إلى 3D
            pos_3d = Point3(pos_m[0], pos_m[1], 0)

            # محاولة تحميل نموذج الكرسي من ملف OBJ
            chair_model_path = "chair.obj"
            chair_node = None

            if os.path.exists(chair_model_path):
                try:
                    # تحميل نموذج الكرسي من ملف OBJ
                    print(f"تحميل نموذج الكرسي من: {chair_model_path}")
                    chair_node = self.loader.loadModel(chair_model_path)

                    if chair_node:
                        # إعادة تسمية العقدة
                        chair_node.setName("chair_obj")

                        # ربط النموذج بالمشهد
                        chair_node.reparentTo(self.render)

                        # تحديد الموضع على الشبكة (z=0 ليكون على الأرض)
                        chair_node.setPos(pos_3d.x, pos_3d.y, 0)

                        # تصحيح اتجاه الكرسي ليكون المقعد أمامك
                        chair_node.setH(0)    # الدوران الأفقي
                        chair_node.setP(90)   # دوران 90 درجة ليجلس على الشبكة
                        chair_node.setR(0)    # بدون دوران جانبي

                        # تحديد المقياس بناءً على حجم الكرسي المطلوب
                        # تصغير النموذج أكثر ليكون متناسق مع الجدران
                        scale_factor = size_m / 20.0  # تصغير أكثر من 10 إلى 20
                        chair_node.setScale(scale_factor, scale_factor, scale_factor)

                        # تحديد لون الكرسي (بني)
                        chair_color = (0.55, 0.27, 0.07, 1)
                        chair_node.setColor(*chair_color)

                        # إضافة علامة للكائن القابل للتحريك
                        chair_node.setTag('movable', 'true')
                        chair_node.setTag('object_type', 'chair')

                        # إضافة الكرسي إلى قائمة العناصر والكراسي
                        self.design_elements.append(chair_node)
                        self.chair_nodes.append(chair_node)
                        self.movable_objects.append(chair_node)

                        print(f"✓ تم تحميل نموذج الكرسي بنجاح من ملف OBJ")
                        print(f"✓ تم إنشاء كرسي بحجم {size_m:.2f} متر في الموضع ({pos_m[0]:.2f}, {pos_m[1]:.2f})")
                        return

                    else:
                        print(f"⚠️ فشل في تحميل نموذج الكرسي من {chair_model_path}")

                except Exception as load_error:
                    print(f"⚠️ خطأ في تحميل نموذج الكرسي: {load_error}")

            else:
                print(f"⚠️ ملف نموذج الكرسي غير موجود: {chair_model_path}")

            # في حالة فشل تحميل النموذج، إنشاء كرسي بسيط كبديل
            print("🔄 إنشاء كرسي بسيط كبديل...")
            self.create_simple_chair_fallback(pos_3d, size_m)

        except Exception as e:
            print(f"خطأ في إنشاء الكرسي: {e}")
            import traceback
            traceback.print_exc()

    def create_simple_chair_fallback(self, pos_3d, size_m):
        """إنشاء كرسي بسيط كبديل في حالة فشل تحميل النموذج"""
        try:
            # إنشاء عقدة الكرسي
            chair_node = self.render.attachNewNode("chair_simple")
            chair_node.setPos(pos_3d)

            # لون الكرسي (بني)
            chair_color = (0.55, 0.27, 0.07, 1)  # بني
            seat_height = 0.45  # ارتفاع المقعد
            back_height = 0.9   # ارتفاع ظهر الكرسي

            from panda3d.core import CardMaker

            # 1. إنشاء المقعد
            cm = CardMaker("seat")
            cm.setFrame(-size_m/2, size_m/2, -size_m/2, size_m/2)
            seat = chair_node.attachNewNode(cm.generate())
            seat.setPos(0, 0, seat_height)
            seat.setP(-90)  # توجيه أفقي
            seat.setColor(*chair_color)

            # 2. إنشاء ظهر الكرسي
            cm = CardMaker("back")
            cm.setFrame(-size_m/2, size_m/2, seat_height, back_height)
            back = chair_node.attachNewNode(cm.generate())
            back.setPos(0, -size_m/2, 0)
            back.setColor(*chair_color)

            # 3. إنشاء الأرجل (4 أرجل)
            leg_thickness = 0.05
            leg_positions = [
                (-size_m/2 + leg_thickness, -size_m/2 + leg_thickness),
                (size_m/2 - leg_thickness, -size_m/2 + leg_thickness),
                (-size_m/2 + leg_thickness, size_m/2 - leg_thickness),
                (size_m/2 - leg_thickness, size_m/2 - leg_thickness)
            ]

            for i, (leg_x, leg_y) in enumerate(leg_positions):
                cm = CardMaker(f"leg_{i}")
                cm.setFrame(-leg_thickness/2, leg_thickness/2, 0, seat_height)
                leg = chair_node.attachNewNode(cm.generate())
                leg.setPos(leg_x, leg_y, 0)
                leg.setColor(0.4, 0.2, 0.05, 1)  # لون أغمق للأرجل

            # إضافة علامة للكائن القابل للتحريك
            chair_node.setTag('movable', 'true')
            chair_node.setTag('object_type', 'chair')

            # إضافة الكرسي إلى قائمة العناصر والكراسي
            self.design_elements.append(chair_node)
            self.chair_nodes.append(chair_node)
            self.movable_objects.append(chair_node)

            print(f"✓ تم إنشاء كرسي بسيط بحجم {size_m:.2f} متر في الموضع ({pos_3d.getX():.2f}, {pos_3d.getY():.2f})")

        except Exception as e:
            print(f"خطأ في إنشاء الكرسي البسيط: {e}")
            import traceback
            traceback.print_exc()

    def create_3d_old_chair(self, chair_data):
        """إنشاء كرسي خشبي قديم ثلاثي الأبعاد من ملف OBJ"""
        try:
            pos_m = chair_data['pos_m']
            size_m = chair_data['size_m']

            print(f"🪑 إنشاء كرسي خشبي قديم بحجم {size_m:.2f} متر في الموضع ({pos_m[0]:.2f}, {pos_m[1]:.2f})")

            # تحويل الموضع إلى Point3
            pos_3d = Point3(pos_m[0], pos_m[1], 0)

            # مسار ملف الكرسي الخشبي القديم - استخدام FBX للجودة العالية
            old_chair_fbx_path = "old_wooden_chair/old_chair.fbx"
            old_chair_obj_path = "old_wooden_chair/old_chair.obj"

            # التحقق من وجود ملف FBX أولاً (جودة أعلى)
            chair_node = None
            model_loaded = False

            if os.path.exists(old_chair_fbx_path):
                try:
                    print(f"🎨 تحميل نموذج FBX عالي الجودة: {old_chair_fbx_path}")
                    chair_node = self.load_fbx_model(old_chair_fbx_path)
                    if chair_node:
                        model_loaded = True
                        print(f"✅ تم تحميل نموذج FBX بنجاح!")
                except Exception as fbx_error:
                    print(f"⚠️ خطأ في تحميل ملف FBX: {fbx_error}")
                    print(f"🔄 سيتم المحاولة مع ملف OBJ...")

            # إذا فشل FBX، جرب OBJ
            if not model_loaded and os.path.exists(old_chair_obj_path):
                try:
                    print(f"🎨 تحميل نموذج OBJ مع المواد: {old_chair_obj_path}")
                    chair_node = self.load_obj_with_materials(old_chair_obj_path)
                    if chair_node:
                        model_loaded = True
                        print(f"✅ تم تحميل نموذج OBJ بنجاح!")
                except Exception as obj_error:
                    print(f"⚠️ خطأ في تحميل ملف OBJ: {obj_error}")

            # إذا تم تحميل النموذج بنجاح
            if model_loaded and chair_node:
                # إعادة تسمية العقدة
                chair_node.setName("old_wooden_chair")

                # ربط النموذج بالمشهد
                chair_node.reparentTo(self.render)

                # تحديد الموضع على الشبكة (z=0 ليكون على الأرض)
                chair_node.setPos(pos_3d.x, pos_3d.y, 0)

                # تصحيح اتجاه الكرسي ليكون المقعد أمامك
                chair_node.setH(0)    # الدوران الأفقي
                chair_node.setP(90)   # دوران 90 درجة ليجلس على الشبكة مثل الكرسي العادي
                chair_node.setR(0)    # بدون دوران جانبي

                # تحديد المقياس بناءً على حجم الكرسي المطلوب
                # نفس مقياس الكرسي العادي
                scale_factor = size_m / 20.0  # نفس مقياس الكرسي العادي
                chair_node.setScale(scale_factor, scale_factor, scale_factor)

                # تطبيق الإضاءة والمواد للحصول على أفضل مظهر
                self.apply_lighting_to_model(chair_node)

                # إضافة علامة للكائن القابل للتحريك
                chair_node.setTag('movable', 'true')
                chair_node.setTag('object_type', 'old_chair')

                # إضافة الكرسي إلى قائمة العناصر والكراسي
                self.design_elements.append(chair_node)
                self.chair_nodes.append(chair_node)
                self.movable_objects.append(chair_node)

                print(f"✓ تم إنشاء كرسي خشبي قديم بحجم {size_m:.2f} متر في الموضع ({pos_m[0]:.2f}, {pos_m[1]:.2f})")
                return

            else:
                print(f"⚠️ لم يتم العثور على ملفات النموذج أو فشل في تحميلها")
                print("🔄 سيتم إنشاء كرسي بسيط بدلاً منه...")

                # إنشاء كرسي بسيط كبديل
                self.create_simple_old_chair(pos_3d, size_m)

        except Exception as e:
            print(f"خطأ في إنشاء الكرسي الخشبي القديم: {e}")
            import traceback
            traceback.print_exc()

    def create_simple_old_chair(self, pos_3d, size_m):
        """إنشاء كرسي خشبي قديم بسيط كبديل"""
        try:
            from panda3d.core import CardMaker

            # إنشاء عقدة الكرسي
            chair_node = self.render.attachNewNode("simple_old_chair")
            chair_node.setPos(pos_3d)

            # إنشاء مقعد الكرسي (أكبر قليلاً)
            seat_maker = CardMaker("old_chair_seat")
            seat_maker.setFrame(-size_m/2, size_m/2, -size_m/2, size_m/2)
            seat = chair_node.attachNewNode(seat_maker.generate())
            seat.setPos(0, 0, size_m * 0.4)
            seat.setColor(0.4, 0.2, 0.1, 1)  # بني داكن للخشب القديم

            # إنشاء ظهر الكرسي (أطول)
            back_maker = CardMaker("old_chair_back")
            back_maker.setFrame(-size_m/2, size_m/2, 0, size_m * 1.2)
            back = chair_node.attachNewNode(back_maker.generate())
            back.setPos(0, -size_m/2, size_m * 0.4)
            back.setP(-90)
            back.setColor(0.35, 0.18, 0.08, 1)  # بني أغمق للظهر

            # إنشاء أرجل الكرسي (أربعة أرجل)
            leg_positions = [
                (-size_m/3, -size_m/3), (size_m/3, -size_m/3),
                (-size_m/3, size_m/3), (size_m/3, size_m/3)
            ]

            for i, (x, y) in enumerate(leg_positions):
                leg_maker = CardMaker(f"old_chair_leg_{i}")
                leg_maker.setFrame(-size_m/20, size_m/20, -size_m/20, size_m/20)
                leg = chair_node.attachNewNode(leg_maker.generate())
                leg.setPos(x, y, size_m * 0.2)
                leg.setScale(1, 1, size_m * 0.4)
                leg.setColor(0.3, 0.15, 0.05, 1)  # بني غامق جداً للأرجل

            # إضافة علامة للكائن القابل للتحريك
            chair_node.setTag('movable', 'true')
            chair_node.setTag('object_type', 'old_chair')

            # إضافة الكرسي إلى قائمة العناصر والكراسي
            self.design_elements.append(chair_node)
            self.chair_nodes.append(chair_node)
            self.movable_objects.append(chair_node)

            print(f"✓ تم إنشاء كرسي خشبي قديم بسيط بحجم {size_m:.2f} متر في الموضع ({pos_3d.getX():.2f}, {pos_3d.getY():.2f})")

        except Exception as e:
            print(f"خطأ في إنشاء الكرسي الخشبي القديم البسيط: {e}")
            import traceback
            traceback.print_exc()

    def load_obj_with_materials(self, obj_path):
        """تحميل ملف OBJ مع دعم كامل لملفات MTL والتكسشر"""
        try:
            print(f"🎨 تحميل نموذج مع المواد والتكسشر: {obj_path}")

            # تحميل النموذج الأساسي
            model_node = self.loader.loadModel(obj_path)

            if not model_node:
                print(f"❌ فشل في تحميل النموذج: {obj_path}")
                return None

            # البحث عن ملف MTL المرافق
            obj_dir = os.path.dirname(obj_path)
            obj_name = os.path.splitext(os.path.basename(obj_path))[0]
            mtl_path = os.path.join(obj_dir, f"{obj_name}.mtl")

            print(f"🔍 البحث عن ملف المواد: {mtl_path}")

            if os.path.exists(mtl_path):
                print(f"✅ تم العثور على ملف المواد: {mtl_path}")
                self.load_mtl_materials(model_node, mtl_path, obj_dir)
            else:
                print(f"⚠️ لم يتم العثور على ملف المواد، سيتم استخدام المواد الافتراضية")

            # تطبيق إعدادات الإضاءة والعرض
            self.setup_model_rendering(model_node)

            return model_node

        except Exception as e:
            print(f"❌ خطأ في تحميل النموذج مع المواد: {e}")
            import traceback
            traceback.print_exc()
            return None

    def load_mtl_materials(self, model_node, mtl_path, base_dir):
        """تحميل وتطبيق المواد من ملف MTL"""
        try:
            print(f"📖 قراءة ملف المواد: {mtl_path}")

            with open(mtl_path, 'r') as mtl_file:
                lines = mtl_file.readlines()

            current_material = None
            materials = {}

            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                parts = line.split()
                if not parts:
                    continue

                command = parts[0]

                if command == 'newmtl':
                    # مادة جديدة
                    current_material = parts[1]
                    materials[current_material] = {}
                    print(f"🎨 مادة جديدة: {current_material}")

                elif current_material and command == 'map_Kd':
                    # تكسشر الألوان الأساسية (Albedo/Diffuse)
                    texture_file = parts[1]
                    texture_path = os.path.join(base_dir, "textures", texture_file)
                    materials[current_material]['diffuse'] = texture_path
                    print(f"🖼️ تكسشر الألوان: {texture_path}")

                elif current_material and command == 'map_Bump':
                    # تكسشر النورمال
                    texture_file = parts[1]
                    texture_path = os.path.join(base_dir, "textures", texture_file)
                    materials[current_material]['normal'] = texture_path
                    print(f"🔺 تكسشر النورمال: {texture_path}")

                elif current_material and command == 'map_Ks':
                    # تكسشر اللمعان
                    texture_file = parts[1]
                    texture_path = os.path.join(base_dir, "textures", texture_file)
                    materials[current_material]['specular'] = texture_path
                    print(f"✨ تكسشر اللمعان: {texture_path}")

            # تطبيق المواد على النموذج
            self.apply_materials_to_model(model_node, materials)

        except Exception as e:
            print(f"❌ خطأ في قراءة ملف المواد: {e}")
            import traceback
            traceback.print_exc()

    def apply_materials_to_model(self, model_node, materials):
        """تطبيق المواد والتكسشر على النموذج"""
        try:
            from panda3d.core import Texture, TextureStage, Material

            print(f"🎨 تطبيق {len(materials)} مادة على النموذج")

            for material_name, material_data in materials.items():
                print(f"🔧 تطبيق مادة: {material_name}")

                # إنشاء مادة جديدة
                material = Material()
                material.setName(material_name)

                # تحميل تكسشر الألوان الأساسية
                if 'diffuse' in material_data:
                    diffuse_path = material_data['diffuse']
                    if os.path.exists(diffuse_path):
                        print(f"📸 تحميل تكسشر الألوان: {diffuse_path}")
                        diffuse_texture = self.loader.loadTexture(diffuse_path)
                        if diffuse_texture:
                            model_node.setTexture(diffuse_texture)
                            print(f"✅ تم تطبيق تكسشر الألوان بنجاح")
                        else:
                            print(f"❌ فشل في تحميل تكسشر الألوان")
                    else:
                        print(f"⚠️ ملف التكسشر غير موجود: {diffuse_path}")

                # تحميل تكسشر النورمال
                if 'normal' in material_data:
                    normal_path = material_data['normal']
                    if os.path.exists(normal_path):
                        print(f"📸 تحميل تكسشر النورمال: {normal_path}")
                        normal_texture = self.loader.loadTexture(normal_path)
                        if normal_texture:
                            ts_normal = TextureStage('normal')
                            ts_normal.setMode(TextureStage.MNormal)
                            model_node.setTexture(ts_normal, normal_texture)
                            print(f"✅ تم تطبيق تكسشر النورمال بنجاح")

                # تطبيق المادة
                model_node.setMaterial(material)

            print(f"🎉 تم تطبيق جميع المواد بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في تطبيق المواد: {e}")
            import traceback
            traceback.print_exc()

    def setup_model_rendering(self, model_node):
        """إعداد خصائص العرض للنموذج"""
        try:
            from panda3d.core import RenderState

            # تمكين الإضاءة
            model_node.setRenderModeWireframe(False)
            model_node.setRenderModeFilled()

            # تحسين جودة العرض
            model_node.setAntialias(True)

            print(f"🎨 تم إعداد خصائص العرض للنموذج")

        except Exception as e:
            print(f"❌ خطأ في إعداد خصائص العرض: {e}")

    def apply_lighting_to_model(self, model_node):
        """تطبيق إضاءة محسنة على النموذج"""
        try:
            from panda3d.core import AmbientLight, DirectionalLight, LightAttrib

            # إضاءة محيطة ناعمة
            ambient_light = AmbientLight('ambient')
            ambient_light.setColor((0.4, 0.4, 0.4, 1))
            ambient_light_np = self.render.attachNewNode(ambient_light)
            model_node.setLight(ambient_light_np)

            # إضاءة اتجاهية لإبراز التفاصيل
            directional_light = DirectionalLight('directional')
            directional_light.setColor((0.8, 0.8, 0.7, 1))
            directional_light.setDirection((-1, -1, -1))
            directional_light_np = self.render.attachNewNode(directional_light)
            model_node.setLight(directional_light_np)

            print(f"💡 تم تطبيق إضاءة محسنة على النموذج")

        except Exception as e:
            print(f"❌ خطأ في تطبيق الإضاءة: {e}")

    def load_fbx_model(self, fbx_path):
        """تحميل ملف FBX مع دعم كامل للمواد والتكسشر المتقدمة"""
        try:
            print(f"🎨 تحميل نموذج FBX عالي الجودة: {fbx_path}")

            # تحميل النموذج الأساسي
            model_node = self.loader.loadModel(fbx_path)

            if not model_node:
                print(f"❌ فشل في تحميل النموذج FBX: {fbx_path}")
                return None

            print(f"✅ تم تحميل النموذج FBX بنجاح!")

            # تطبيق التكسشر المتقدمة من مجلد textures
            self.apply_advanced_textures_to_fbx(model_node, os.path.dirname(fbx_path))

            # تطبيق إعدادات الإضاءة والعرض المتقدمة
            self.setup_advanced_rendering(model_node)

            return model_node

        except Exception as e:
            print(f"❌ خطأ في تحميل النموذج FBX: {e}")
            import traceback
            traceback.print_exc()
            return None

    def apply_advanced_textures_to_fbx(self, model_node, base_dir):
        """تطبيق التكسشر المتقدمة على نموذج FBX"""
        try:
            from panda3d.core import TextureStage

            textures_dir = os.path.join(base_dir, "textures")
            if not os.path.exists(textures_dir):
                print(f"⚠️ مجلد التكسشر غير موجود: {textures_dir}")
                return

            print(f"🎨 تطبيق التكسشر المتقدمة من: {textures_dir}")

            # قائمة التكسشر المتاحة
            texture_files = {
                'albedo': 'old_chair_Albedo.png',      # الألوان الأساسية
                'normal': 'old_chair_Normal.png',      # تفاصيل السطح
                'metallic': 'old_chair_Metallic.png',  # الخصائص المعدنية
                'roughness': 'old_chair_Roughness.png', # خشونة السطح
                'ao': 'old_chair_AO.png',              # الظلال المحيطة
                'bcolor': 'old_chair_bcolor.png'       # ألوان إضافية
            }

            textures_applied = 0

            for texture_type, filename in texture_files.items():
                texture_path = os.path.join(textures_dir, filename)

                if os.path.exists(texture_path):
                    try:
                        print(f"📸 تحميل تكسشر {texture_type}: {filename}")
                        texture = self.loader.loadTexture(texture_path)

                        if texture:
                            if texture_type == 'albedo':
                                # التكسشر الأساسية (الألوان)
                                model_node.setTexture(texture)
                                print(f"✅ تم تطبيق تكسشر الألوان الأساسية")

                            elif texture_type == 'normal':
                                # تكسشر النورمال للتفاصيل
                                ts_normal = TextureStage('normal')
                                ts_normal.setMode(TextureStage.MNormal)
                                model_node.setTexture(ts_normal, texture)
                                print(f"✅ تم تطبيق تكسشر النورمال")

                            elif texture_type == 'metallic':
                                # تكسشر الخصائص المعدنية
                                ts_metallic = TextureStage('metallic')
                                ts_metallic.setMode(TextureStage.MModulate)
                                model_node.setTexture(ts_metallic, texture)
                                print(f"✅ تم تطبيق تكسشر الخصائص المعدنية")

                            elif texture_type == 'roughness':
                                # تكسشر الخشونة
                                ts_roughness = TextureStage('roughness')
                                ts_roughness.setMode(TextureStage.MModulate)
                                model_node.setTexture(ts_roughness, texture)
                                print(f"✅ تم تطبيق تكسشر الخشونة")

                            elif texture_type == 'ao':
                                # تكسشر الظلال المحيطة
                                ts_ao = TextureStage('ao')
                                ts_ao.setMode(TextureStage.MModulate)
                                model_node.setTexture(ts_ao, texture)
                                print(f"✅ تم تطبيق تكسشر الظلال المحيطة")

                            textures_applied += 1
                        else:
                            print(f"❌ فشل في تحميل تكسشر {texture_type}")

                    except Exception as tex_error:
                        print(f"❌ خطأ في تطبيق تكسشر {texture_type}: {tex_error}")
                else:
                    print(f"⚠️ تكسشر {texture_type} غير موجودة: {filename}")

            print(f"🎉 تم تطبيق {textures_applied} تكسشر بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في تطبيق التكسشر المتقدمة: {e}")
            import traceback
            traceback.print_exc()

    def setup_advanced_rendering(self, model_node):
        """إعداد خصائص العرض المتقدمة لنموذج FBX"""
        try:
            from panda3d.core import Material, RenderState

            # إنشاء مادة متقدمة
            material = Material()
            material.setName("old_chair_advanced_material")

            # خصائص المادة الخشبية
            material.setAmbient((0.3, 0.2, 0.1, 1.0))    # لون محيط خشبي
            material.setDiffuse((0.8, 0.6, 0.4, 1.0))    # لون أساسي خشبي دافئ
            material.setSpecular((0.2, 0.2, 0.2, 1.0))   # لمعان خفيف
            material.setShininess(10.0)                   # خشونة طبيعية للخشب

            # تطبيق المادة
            model_node.setMaterial(material)

            # تحسين جودة العرض
            model_node.setRenderModeWireframe(False)
            model_node.setRenderModeFilled()
            model_node.setAntialias(True)

            # تمكين الشفافية إذا لزم الأمر
            model_node.setTransparency(True)

            print(f"🎨 تم إعداد خصائص العرض المتقدمة للنموذج FBX")

        except Exception as e:
            print(f"❌ خطأ في إعداد خصائص العرض المتقدمة: {e}")

    def create_3d_door(self, door_data):
        """إنشاء باب ثلاثي الأبعاد كتجويف في الجدار مع إطار أحمر"""
        try:
            # استخراج بيانات الباب
            pos_m = door_data['pos_m']
            width_m = door_data['width_m']
            wall_ref = door_data.get('wall_ref')

            if not wall_ref:
                print("⚠️ لا يمكن إنشاء الباب: لا يوجد جدار مرجعي")
                return

            print(f"🚪 إنشاء باب بعرض {width_m:.2f} متر في الموضع ({pos_m[0]:.2f}, {pos_m[1]:.2f})")

            # حساب اتجاه الجدار
            wall_start = wall_ref['start_m']
            wall_end = wall_ref['end_m']
            wall_vec = Vec3(wall_end[0] - wall_start[0], wall_end[1] - wall_start[1], 0)
            wall_length = wall_vec.length()

            # تحديد إذا كان الجدار أفقي أم رأسي
            is_horizontal = abs(wall_vec.x) > abs(wall_vec.y)

            # ارتفاع الباب (أقل من ارتفاع الجدار)
            wall_height = wall_ref.get('height_m', 6.0)
            door_height = min(2.2, wall_height - 0.2)  # ارتفاع باب عادي مع هامش

            # سمك الجدار
            wall_thickness = 0.25

            # إنشاء التجويف (إزالة جزء من الجدار)
            self.create_door_opening(pos_m, width_m, door_height, wall_thickness, is_horizontal, wall_ref)

            # إنشاء إطار الباب الأحمر
            self.create_door_frame(pos_m, width_m, door_height, wall_thickness, is_horizontal, wall_ref)

            print(f"✅ تم إنشاء باب بعرض {width_m:.2f}م وارتفاع {door_height:.2f}م")

        except Exception as e:
            print(f"خطأ في إنشاء الباب ثلاثي الأبعاد: {e}")
            import traceback
            traceback.print_exc()

    def create_3d_window(self, window_data):
        """إنشاء نافذة ثلاثية الأبعاد كتجويف في الجدار مع إطار أزرق"""
        try:
            # استخراج بيانات النافذة
            pos_m = window_data['pos_m']
            width_m = window_data['width_m']
            wall_ref = window_data.get('wall_ref')

            if not wall_ref:
                print("⚠️ لا يمكن إنشاء النافذة: لا يوجد جدار مرجعي")
                return

            print(f"🪟 إنشاء نافذة بعرض {width_m:.2f} متر في الموضع ({pos_m[0]:.2f}, {pos_m[1]:.2f})")

            # حساب اتجاه الجدار
            wall_start = wall_ref['start_m']
            wall_end = wall_ref['end_m']
            wall_vec = Vec3(wall_end[0] - wall_start[0], wall_end[1] - wall_start[1], 0)

            # تحديد إذا كان الجدار أفقي أم رأسي
            is_horizontal = abs(wall_vec.x) > abs(wall_vec.y)

            # ارتفاع النافذة (في منتصف الجدار)
            wall_height = wall_ref.get('height_m', 6.0)
            window_height = min(1.5, wall_height * 0.4)  # ارتفاع نافذة عادي
            window_bottom = wall_height * 0.3  # ارتفاع قاعدة النافذة من الأرض

            # سمك الجدار
            wall_thickness = 0.25

            # إنشاء التجويف (إزالة جزء من الجدار)
            self.create_window_opening(pos_m, width_m, window_height, window_bottom, wall_thickness, is_horizontal, wall_ref)

            # إنشاء إطار النافذة الأزرق
            self.create_window_frame(pos_m, width_m, window_height, window_bottom, wall_thickness, is_horizontal, wall_ref)

            print(f"✅ تم إنشاء نافذة بعرض {width_m:.2f}م وارتفاع {window_height:.2f}م")

        except Exception as e:
            print(f"خطأ في إنشاء النافذة ثلاثية الأبعاد: {e}")
            import traceback
            traceback.print_exc()

    def create_door_opening(self, pos_m, width_m, height_m, wall_thickness, is_horizontal, wall_ref):
        """إنشاء تجويف الباب في الجدار"""
        try:
            # ملاحظة: في التطبيق الحقيقي، يجب تعديل الجدار نفسه لإنشاء التجويف
            # هنا سنضع مكعب شفاف لمحاكاة التجويف
            from panda3d.core import CardMaker

            # إنشاء عقدة التجويف
            opening_node = self.render.attachNewNode("door_opening")
            opening_node.setPos(pos_m[0], pos_m[1], 0)

            # حساب اتجاه الجدار
            wall_start = wall_ref['start_m']
            wall_end = wall_ref['end_m']
            wall_vec = Vec3(wall_end[0] - wall_start[0], wall_end[1] - wall_start[1], 0)
            heading = math.degrees(math.atan2(wall_vec.y, wall_vec.x))
            opening_node.setH(heading)

            # إنشاء مكعب التجويف (شفاف تماماً)
            cm = CardMaker("door_opening_box")
            if is_horizontal:
                cm.setFrame(-width_m/2, width_m/2, -wall_thickness/2, wall_thickness/2)
            else:
                cm.setFrame(-wall_thickness/2, wall_thickness/2, -width_m/2, width_m/2)

            opening_box = opening_node.attachNewNode(cm.generate())
            opening_box.setPos(0, 0, height_m/2)
            opening_box.setScale(1, 1, height_m)
            opening_box.setTransparency(1)
            opening_box.setColor(1, 1, 1, 0)  # شفاف تماماً

            self.design_elements.append(opening_node)

        except Exception as e:
            print(f"خطأ في إنشاء تجويف الباب: {e}")

    def create_door_frame(self, pos_m, width_m, height_m, wall_thickness, is_horizontal, wall_ref):
        """إنشاء إطار الباب الأحمر"""
        try:
            from panda3d.core import CardMaker

            # إنشاء عقدة الإطار
            frame_node = self.render.attachNewNode("door_frame")
            frame_node.setPos(pos_m[0], pos_m[1], 0)

            # حساب اتجاه الجدار
            wall_start = wall_ref['start_m']
            wall_end = wall_ref['end_m']
            wall_vec = Vec3(wall_end[0] - wall_start[0], wall_end[1] - wall_start[1], 0)
            heading = math.degrees(math.atan2(wall_vec.y, wall_vec.x))
            frame_node.setH(heading)

            # لون الإطار الأحمر
            frame_color = (0.8, 0.1, 0.1, 1)  # أحمر داكن
            frame_thickness = 0.05  # سمك الإطار

            # إنشاء الإطار الجانبي الأيسر
            cm_left = CardMaker("door_frame_left")
            if is_horizontal:
                cm_left.setFrame(-width_m/2 - frame_thickness, -width_m/2, -wall_thickness/2, wall_thickness/2)
            else:
                cm_left.setFrame(-wall_thickness/2, wall_thickness/2, -width_m/2 - frame_thickness, -width_m/2)

            left_frame = frame_node.attachNewNode(cm_left.generate())
            left_frame.setPos(0, 0, height_m/2)
            left_frame.setScale(1, 1, height_m)
            left_frame.setColor(*frame_color)

            # إنشاء الإطار الجانبي الأيمن
            cm_right = CardMaker("door_frame_right")
            if is_horizontal:
                cm_right.setFrame(width_m/2, width_m/2 + frame_thickness, -wall_thickness/2, wall_thickness/2)
            else:
                cm_right.setFrame(-wall_thickness/2, wall_thickness/2, width_m/2, width_m/2 + frame_thickness)

            right_frame = frame_node.attachNewNode(cm_right.generate())
            right_frame.setPos(0, 0, height_m/2)
            right_frame.setScale(1, 1, height_m)
            right_frame.setColor(*frame_color)

            # إنشاء الإطار العلوي
            cm_top = CardMaker("door_frame_top")
            if is_horizontal:
                cm_top.setFrame(-width_m/2, width_m/2, -wall_thickness/2, wall_thickness/2)
            else:
                cm_top.setFrame(-wall_thickness/2, wall_thickness/2, -width_m/2, width_m/2)

            top_frame = frame_node.attachNewNode(cm_top.generate())
            top_frame.setPos(0, 0, height_m + frame_thickness/2)
            top_frame.setScale(1, 1, frame_thickness)
            top_frame.setColor(*frame_color)

            self.design_elements.append(frame_node)

        except Exception as e:
            print(f"خطأ في إنشاء إطار الباب: {e}")

    def create_window_opening(self, pos_m, width_m, height_m, bottom_height, wall_thickness, is_horizontal, wall_ref):
        """إنشاء تجويف النافذة في الجدار"""
        try:
            from panda3d.core import CardMaker

            # إنشاء عقدة التجويف
            opening_node = self.render.attachNewNode("window_opening")
            opening_node.setPos(pos_m[0], pos_m[1], bottom_height + height_m/2)

            # حساب اتجاه الجدار
            wall_start = wall_ref['start_m']
            wall_end = wall_ref['end_m']
            wall_vec = Vec3(wall_end[0] - wall_start[0], wall_end[1] - wall_start[1], 0)
            heading = math.degrees(math.atan2(wall_vec.y, wall_vec.x))
            opening_node.setH(heading)

            # إنشاء مكعب التجويف (شفاف تماماً)
            cm = CardMaker("window_opening_box")
            if is_horizontal:
                cm.setFrame(-width_m/2, width_m/2, -wall_thickness/2, wall_thickness/2)
            else:
                cm.setFrame(-wall_thickness/2, wall_thickness/2, -width_m/2, width_m/2)

            opening_box = opening_node.attachNewNode(cm.generate())
            opening_box.setScale(1, 1, height_m)
            opening_box.setTransparency(1)
            opening_box.setColor(1, 1, 1, 0)  # شفاف تماماً

            self.design_elements.append(opening_node)

        except Exception as e:
            print(f"خطأ في إنشاء تجويف النافذة: {e}")

    def create_window_frame(self, pos_m, width_m, height_m, bottom_height, wall_thickness, is_horizontal, wall_ref):
        """إنشاء إطار النافذة الأزرق"""
        try:
            from panda3d.core import CardMaker

            # إنشاء عقدة الإطار
            frame_node = self.render.attachNewNode("window_frame")
            frame_node.setPos(pos_m[0], pos_m[1], bottom_height)

            # حساب اتجاه الجدار
            wall_start = wall_ref['start_m']
            wall_end = wall_ref['end_m']
            wall_vec = Vec3(wall_end[0] - wall_start[0], wall_end[1] - wall_start[1], 0)
            heading = math.degrees(math.atan2(wall_vec.y, wall_vec.x))
            frame_node.setH(heading)

            # لون الإطار الأزرق
            frame_color = (0.1, 0.1, 0.8, 1)  # أزرق داكن
            frame_thickness = 0.05  # سمك الإطار

            # إنشاء الإطار الجانبي الأيسر
            cm_left = CardMaker("window_frame_left")
            if is_horizontal:
                cm_left.setFrame(-width_m/2 - frame_thickness, -width_m/2, -wall_thickness/2, wall_thickness/2)
            else:
                cm_left.setFrame(-wall_thickness/2, wall_thickness/2, -width_m/2 - frame_thickness, -width_m/2)

            left_frame = frame_node.attachNewNode(cm_left.generate())
            left_frame.setPos(0, 0, height_m/2)
            left_frame.setScale(1, 1, height_m)
            left_frame.setColor(*frame_color)

            # إنشاء الإطار الجانبي الأيمن
            cm_right = CardMaker("window_frame_right")
            if is_horizontal:
                cm_right.setFrame(width_m/2, width_m/2 + frame_thickness, -wall_thickness/2, wall_thickness/2)
            else:
                cm_right.setFrame(-wall_thickness/2, wall_thickness/2, width_m/2, width_m/2 + frame_thickness)

            right_frame = frame_node.attachNewNode(cm_right.generate())
            right_frame.setPos(0, 0, height_m/2)
            right_frame.setScale(1, 1, height_m)
            right_frame.setColor(*frame_color)

            # إنشاء الإطار العلوي
            cm_top = CardMaker("window_frame_top")
            if is_horizontal:
                cm_top.setFrame(-width_m/2, width_m/2, -wall_thickness/2, wall_thickness/2)
            else:
                cm_top.setFrame(-wall_thickness/2, wall_thickness/2, -width_m/2, width_m/2)

            top_frame = frame_node.attachNewNode(cm_top.generate())
            top_frame.setPos(0, 0, height_m + frame_thickness/2)
            top_frame.setScale(1, 1, frame_thickness)
            top_frame.setColor(*frame_color)

            # إنشاء الإطار السفلي
            cm_bottom = CardMaker("window_frame_bottom")
            if is_horizontal:
                cm_bottom.setFrame(-width_m/2, width_m/2, -wall_thickness/2, wall_thickness/2)
            else:
                cm_bottom.setFrame(-wall_thickness/2, wall_thickness/2, -width_m/2, width_m/2)

            bottom_frame = frame_node.attachNewNode(cm_bottom.generate())
            bottom_frame.setPos(0, 0, -frame_thickness/2)
            bottom_frame.setScale(1, 1, frame_thickness)
            bottom_frame.setColor(*frame_color)

            # إضافة شبكة النافذة (خطوط متقاطعة)
            self.create_window_grid(frame_node, width_m, height_m, wall_thickness, is_horizontal, frame_color)

            self.design_elements.append(frame_node)

        except Exception as e:
            print(f"خطأ في إنشاء إطار النافذة: {e}")

    def create_window_grid(self, parent_node, width_m, height_m, wall_thickness, is_horizontal, color):
        """إنشاء شبكة النافذة (خطوط متقاطعة)"""
        try:
            from panda3d.core import CardMaker

            grid_thickness = 0.02  # سمك خطوط الشبكة

            # خط عمودي في المنتصف
            cm_v = CardMaker("window_grid_vertical")
            if is_horizontal:
                cm_v.setFrame(-grid_thickness/2, grid_thickness/2, -wall_thickness/2, wall_thickness/2)
            else:
                cm_v.setFrame(-wall_thickness/2, wall_thickness/2, -grid_thickness/2, grid_thickness/2)

            v_grid = parent_node.attachNewNode(cm_v.generate())
            v_grid.setPos(0, 0, height_m/2)
            v_grid.setScale(1, 1, height_m * 0.8)  # أقصر قليلاً من الإطار
            v_grid.setColor(*color)

            # خط أفقي في المنتصف
            cm_h = CardMaker("window_grid_horizontal")
            if is_horizontal:
                cm_h.setFrame(-width_m/2 * 0.8, width_m/2 * 0.8, -wall_thickness/2, wall_thickness/2)
            else:
                cm_h.setFrame(-wall_thickness/2, wall_thickness/2, -width_m/2 * 0.8, width_m/2 * 0.8)

            h_grid = parent_node.attachNewNode(cm_h.generate())
            h_grid.setPos(0, 0, height_m/2)
            h_grid.setScale(1, 1, grid_thickness)
            h_grid.setColor(*color)

        except Exception as e:
            print(f"خطأ في إنشاء شبكة النافذة: {e}")



    def add_wall_edges(self, start_3d, end_3d, wall_height, wall_thickness):
        """إضافة خطوط سوداء لإبراز حواف الجدار بوضوح"""
        try:
            # حساب متجه الجدار
            wall_vec = end_3d - start_3d
            wall_length = wall_vec.length()

            # إنشاء خطوط الحواف السوداء الواضحة
            lines = LineSegs()
            lines.setThickness(3)  # سمك مناسب للخطوط
            lines.setColor(0.0, 0.0, 0.0, 1.0)  # لون أسود تماماً للحواف

            # حساب النقاط الثمانية للجدار مع السمك الجديد
            p1 = Point3(0, -wall_thickness/2, 0)                    # أسفل خلف يسار
            p2 = Point3(wall_length, -wall_thickness/2, 0)          # أسفل خلف يمين
            p3 = Point3(wall_length, wall_thickness/2, 0)           # أسفل أمام يمين
            p4 = Point3(0, wall_thickness/2, 0)                     # أسفل أمام يسار
            p5 = Point3(0, -wall_thickness/2, wall_height)          # أعلى خلف يسار
            p6 = Point3(wall_length, -wall_thickness/2, wall_height) # أعلى خلف يمين
            p7 = Point3(wall_length, wall_thickness/2, wall_height)  # أعلى أمام يمين
            p8 = Point3(0, wall_thickness/2, wall_height)           # أعلى أمام يسار

            # رسم الحواف الرئيسية فقط للوضوح

            # الحواف العلوية (مستطيل علوي)
            lines.moveTo(p5)
            lines.drawTo(p6)
            lines.drawTo(p7)
            lines.drawTo(p8)
            lines.drawTo(p5)

            # الحواف السفلية (مستطيل سفلي)
            lines.moveTo(p1)
            lines.drawTo(p2)
            lines.drawTo(p3)
            lines.drawTo(p4)
            lines.drawTo(p1)

            # الحواف الرأسية (الأعمدة الأربعة)
            lines.moveTo(p1)
            lines.drawTo(p5)

            lines.moveTo(p2)
            lines.drawTo(p6)

            lines.moveTo(p3)
            lines.drawTo(p7)

            lines.moveTo(p4)
            lines.drawTo(p8)

            # إنشاء عقدة الخطوط
            node = lines.create()
            edges_node = self.render.attachNewNode(node)

            # تحديد موضع واتجاه الخطوط
            edges_node.setPos(start_3d)
            heading = math.degrees(math.atan2(wall_vec.getY(), wall_vec.getX()))
            edges_node.setH(heading)

            # إضافة الخطوط إلى قائمة العناصر
            self.design_elements.append(edges_node)

        except Exception as e:
            print(f"خطأ في إضافة حواف الجدار: {e}")
            import traceback
            traceback.print_exc()

    def create_wall_corners(self, walls_data):
        """إنشاء قطع ربط في زوايا الجدران لملء الفراغات"""
        try:
            print("🔗 جاري إنشاء قطع الربط في الزوايا...")
            corners_created = 0

            # البحث عن نقاط التقاء الجدران
            for i, wall1 in enumerate(walls_data):
                for j, wall2 in enumerate(walls_data):
                    if i >= j:  # تجنب التكرار
                        continue

                    # استخراج نقاط الجدران
                    wall1_start = Point3(wall1['start_m'][0], wall1['start_m'][1], 0)
                    wall1_end = Point3(wall1['end_m'][0], wall1['end_m'][1], 0)
                    wall2_start = Point3(wall2['start_m'][0], wall2['start_m'][1], 0)
                    wall2_end = Point3(wall2['end_m'][0], wall2['end_m'][1], 0)

                    # التحقق من نقاط الالتقاء
                    intersection_point = None
                    wall1_thickness = 0.25
                    wall2_thickness = 0.25
                    tolerance = 0.1  # تسامح في المسافة

                    # فحص جميع التقاطعات المحتملة
                    points_to_check = [
                        (wall1_start, wall2_start), (wall1_start, wall2_end),
                        (wall1_end, wall2_start), (wall1_end, wall2_end)
                    ]

                    for p1, p2 in points_to_check:
                        distance = (p1 - p2).length()
                        if distance < tolerance:
                            intersection_point = Point3((p1.x + p2.x) / 2, (p1.y + p2.y) / 2, 0)
                            break

                    if intersection_point:
                        # إنشاء قطعة ربط في الزاوية
                        wall1_height = wall1.get('height_m', 6.0)
                        wall2_height = wall2.get('height_m', 6.0)
                        corner_height = max(wall1_height, wall2_height)

                        self.create_corner_piece(intersection_point, wall1_thickness, wall2_thickness, corner_height)
                        corners_created += 1

                        print(f"✅ تم إنشاء قطعة ربط في الموضع: ({intersection_point.x:.2f}, {intersection_point.y:.2f})")

            print(f"🔗 تم إنشاء {corners_created} قطعة ربط في الزوايا")

        except Exception as e:
            print(f"خطأ في إنشاء قطع الربط: {e}")
            import traceback
            traceback.print_exc()

    def create_corner_piece(self, center_point, thickness1, thickness2, height):
        """إنشاء قطعة ربط مكعبة في الزاوية"""
        try:
            from panda3d.core import CardMaker, Material

            # حساب حجم قطعة الربط
            corner_size = max(thickness1, thickness2)

            # إنشاء عقدة قطعة الربط
            corner_node = self.render.attachNewNode("wall_corner")
            corner_node.setPos(center_point.x - corner_size/2, center_point.y - corner_size/2, 0)

            # لون قطعة الربط (نفس لون الجدران)
            corner_color = (0.92, 0.92, 0.92, 1)  # أبيض مائل للرمادي

            # إنشاء مواد قطعة الربط
            def create_corner_material():
                material = Material()
                material.setAmbient((0.6, 0.6, 0.6, 1))
                material.setDiffuse(corner_color)
                material.setSpecular((0.3, 0.3, 0.3, 1))
                material.setShininess(15.0)
                return material

            # إنشاء الوجوه الستة لقطعة الربط
            faces = [
                # الوجه الأمامي
                {"name": "front", "frame": (0, corner_size, 0, height), "pos": (0, corner_size, 0), "rotation": (0, 0, 0)},
                # الوجه الخلفي
                {"name": "back", "frame": (corner_size, 0, 0, height), "pos": (0, 0, 0), "rotation": (0, 0, 0)},
                # الجانب الأيسر
                {"name": "left", "frame": (0, corner_size, 0, height), "pos": (0, 0, 0), "rotation": (90, 0, 0)},
                # الجانب الأيمن
                {"name": "right", "frame": (corner_size, 0, 0, height), "pos": (corner_size, 0, 0), "rotation": (90, 0, 0)},
                # الوجه العلوي
                {"name": "top", "frame": (0, corner_size, 0, corner_size), "pos": (0, 0, height), "rotation": (0, -90, 0)},
                # الوجه السفلي
                {"name": "bottom", "frame": (corner_size, 0, 0, corner_size), "pos": (0, 0, 0), "rotation": (0, 90, 0)}
            ]

            for face in faces:
                cm = CardMaker(f"corner_{face['name']}")
                cm.setFrame(*face["frame"])
                face_node = corner_node.attachNewNode(cm.generate())
                face_node.setPos(*face["pos"])
                face_node.setH(face["rotation"][0])
                face_node.setP(face["rotation"][1])
                face_node.setR(face["rotation"][2])
                face_node.setColor(*corner_color)
                face_node.setMaterial(create_corner_material())
                face_node.setTwoSided(True)

            # إضافة حواف سوداء لقطعة الربط
            self.add_corner_edges(center_point, corner_size, height)

            # إضافة قطعة الربط إلى قائمة العناصر
            self.design_elements.append(corner_node)

        except Exception as e:
            print(f"خطأ في إنشاء قطعة الربط: {e}")
            import traceback
            traceback.print_exc()

    def add_corner_edges(self, center_point, size, height):
        """إضافة حواف سوداء لقطعة الربط"""
        try:
            lines = LineSegs()
            lines.setThickness(3)
            lines.setColor(0.0, 0.0, 0.0, 1.0)

            # النقاط الثمانية لقطعة الربط
            half_size = size / 2
            p1 = Point3(-half_size, -half_size, 0)
            p2 = Point3(half_size, -half_size, 0)
            p3 = Point3(half_size, half_size, 0)
            p4 = Point3(-half_size, half_size, 0)
            p5 = Point3(-half_size, -half_size, height)
            p6 = Point3(half_size, -half_size, height)
            p7 = Point3(half_size, half_size, height)
            p8 = Point3(-half_size, half_size, height)

            # رسم الحواف السفلية
            lines.moveTo(p1)
            lines.drawTo(p2)
            lines.drawTo(p3)
            lines.drawTo(p4)
            lines.drawTo(p1)

            # رسم الحواف العلوية
            lines.moveTo(p5)
            lines.drawTo(p6)
            lines.drawTo(p7)
            lines.drawTo(p8)
            lines.drawTo(p5)

            # رسم الحواف الرأسية
            lines.moveTo(p1)
            lines.drawTo(p5)
            lines.moveTo(p2)
            lines.drawTo(p6)
            lines.moveTo(p3)
            lines.drawTo(p7)
            lines.moveTo(p4)
            lines.drawTo(p8)

            # إنشاء عقدة الحواف
            edges_node = self.render.attachNewNode(lines.create())
            edges_node.setPos(center_point.x, center_point.y, 0)

            # إضافة الحواف إلى قائمة العناصر
            self.design_elements.append(edges_node)

        except Exception as e:
            print(f"خطأ في إضافة حواف قطعة الربط: {e}")

    def enable_interactive_controls(self):
        # تمكين التحكم بالماوس
        self.accept("mouse1", self.on_mouse_1_down)
        self.accept("mouse1-up", self.on_mouse_1_up)
        self.accept("mouse2", self.on_orbit_mouse_down)
        self.accept("mouse2-up", self.on_orbit_mouse_up)
        self.accept("mouse3", self.on_mouse_3_down)
        self.accept("mouse3-up", self.on_mouse_3_up)
        self.accept("wheel_up", self.on_wheel_up)
        self.accept("wheel_down", self.on_wheel_down)

        # تمكين التحكم بلوحة المفاتيح
        self.accept("arrow_left", self.on_key_left)
        self.accept("arrow_right", self.on_key_right)
        self.accept("arrow_up", self.on_key_up)
        self.accept("arrow_down", self.on_key_down)
        self.accept("page_up", self.on_key_zoom_in)
        self.accept("page_down", self.on_key_zoom_out)
        # مفتاح A محذوف - المحاور مخفية نهائياً

        # إضافة مهمة التحكم بالماوس
        self.taskMgr.add(self.mouse_control_task, "MouseControlTask")

    def on_mouse_1_down(self):
        self.mouse_btn_down[0] = True
        if self.mouseWatcherNode.hasMouse():
            mpos = self.mouseWatcherNode.getMouse()
            self.last_mouse_x = mpos.getX()
            self.last_mouse_y = mpos.getY()

            # إذا كان في وضع إضافة الأثاث
            if self.adding_furniture:
                self.add_furniture_at_mouse(mpos.getX(), mpos.getY())
            else:
                # التحقق من النقر على كائن قابل للتحريك
                self.check_object_selection(mpos.getX(), mpos.getY())

    def on_mouse_1_up(self):
        self.mouse_btn_down[0] = False

        # إنهاء سحب الكائن إذا كان في وضع السحب
        if self.selected_object and self.movement_mode == 'drag':
            obj_pos = self.selected_object.getPos()
            object_type = self.selected_object.getTag('object_type')
            print(f"✅ تم إنهاء سحب {object_type} في الموضع: ({obj_pos.x:.2f}, {obj_pos.y:.2f})")

    def on_mouse_3_down(self):
        self.mouse_btn_down[2] = True
        if self.mouseWatcherNode.hasMouse():
            mpos = self.mouseWatcherNode.getMouse()
            self.last_mouse_x = mpos.getX()
            self.last_mouse_y = mpos.getY()

    def on_mouse_3_up(self):
        self.mouse_btn_down[2] = False

    def on_orbit_mouse_down(self):
        self.mouse_btn_down[1] = True
        if self.mouseWatcherNode.hasMouse():
            mpos = self.mouseWatcherNode.getMouse()
            self.last_mouse_x = mpos.getX()
            self.last_mouse_y = mpos.getY()

    def on_orbit_mouse_up(self):
        self.mouse_btn_down[1] = False

    def on_wheel_up(self):
        # تكبير محسن مع تنعيم
        zoom_speed = max(0.8, self.cam_distance * 0.15)  # سرعة متناسبة مع المسافة
        self.cam_distance = max(3, self.cam_distance - zoom_speed)
        self.update_camera_position()
        print(f"🔍 تكبير - مسافة الكاميرا: {self.cam_distance:.1f}م")

    def on_wheel_down(self):
        # تصغير محسن مع تنعيم
        zoom_speed = max(0.8, self.cam_distance * 0.15)  # سرعة متناسبة مع المسافة
        self.cam_distance = min(150, self.cam_distance + zoom_speed)
        self.update_camera_position()
        print(f"🔍 تصغير - مسافة الكاميرا: {self.cam_distance:.1f}م")

    # دوال التحكم بلوحة المفاتيح
    def on_key_left(self):
        self.cam_heading += 10
        self.update_camera_pos()

    def on_key_right(self):
        self.cam_heading -= 10
        self.update_camera_pos()

    def on_key_up(self):
        self.cam_pitch = max(-89.9, self.cam_pitch - 10)
        self.update_camera_pos()

    def on_key_down(self):
        self.cam_pitch = min(89.9, self.cam_pitch + 10)
        self.update_camera_pos()

    def on_key_zoom_in(self):
        self.cam_distance = max(0.1, self.cam_distance - 2)
        self.update_camera_pos()

    def on_key_zoom_out(self):
        self.cam_distance += 2
        self.update_camera_pos()

    def pick_object_at_mouse(self, mouse_x, mouse_y):
        """اكتشاف الكائن المنقور عليه باستخدام النظام المحسن"""
        try:
            # إعداد شعاع النقر
            self.picker_ray.setFromLens(self.camNode, mouse_x, mouse_y)

            # تشغيل نظام اكتشاف التصادم
            self.picker_traverser.traverse(self.render)

            # فحص النتائج
            if self.picker_handler.getNumEntries() > 0:
                # ترتيب النتائج حسب المسافة (الأقرب أولاً)
                self.picker_handler.sortEntries()

                # فحص كل نتيجة للعثور على كائن قابل للتحريك
                for i in range(self.picker_handler.getNumEntries()):
                    entry = self.picker_handler.getEntry(i)
                    picked_node = entry.getIntoNodePath()

                    # البحث عن العقدة الأب التي تحتوي على علامة 'movable'
                    current_node = picked_node
                    while current_node and not current_node.isEmpty():
                        if current_node.hasTag('movable') and current_node.getTag('movable') == 'true':
                            return current_node
                        current_node = current_node.getParent()

            return None

        except Exception as e:
            print(f"خطأ في اكتشاف الكائن: {e}")
            return None

    def check_object_selection(self, mouse_x, mouse_y):
        """التحقق من النقر على كائن وتحديده للتحريك"""
        try:
            # اكتشاف الكائن المنقور عليه
            picked_object = self.pick_object_at_mouse(mouse_x, mouse_y)

            if picked_object:
                # إلغاء تحديد الكائن السابق
                if self.selected_object and self.selected_object != picked_object:
                    self.deselect_object()

                # إذا كان نفس الكائن محدد، لا نعيد إنشاء الأسهم
                if self.selected_object == picked_object:
                    return True

                # تحديد الكائن الجديد
                self.selected_object = picked_object

                # تحديد وضع التحريك حسب الحالة الحالية
                if self.movement_mode is None:
                    self.movement_mode = 'drag'  # افتراضي: السحب

                # تغيير لون الكائن المحدد
                object_type = picked_object.getTag('object_type')
                if object_type == 'chair':
                    picked_object.setColor(1, 1, 0, 1)  # أصفر للتحديد
                elif object_type == 'old_chair':
                    picked_object.setColorScale(1.5, 1.5, 0.5, 1.0)  # أصفر مع الحفاظ على اللون الخشبي

                obj_pos = picked_object.getPos()
                print(f"🎯 تم تحديد {object_type} في الموضع: ({obj_pos.getX():.2f}, {obj_pos.getY():.2f})")
                print(f"🎮 وضع التحريك الحالي: {self.get_movement_mode_text()}")
                print("💡 اضغط Q لتغيير وضع التحريك")

                return True

            return False

        except Exception as e:
            print(f"خطأ في تحديد الكائن: {e}")
            return False

    def check_arrow_selection(self, mouse_x, mouse_y):
        """التحقق من النقر على سهم التحكم"""
        try:
            if not self.control_arrow:
                return False

            # اكتشاف السهم المنقور عليه
            picked_arrow = self.pick_object_at_mouse(mouse_x, mouse_y)

            if picked_arrow and picked_arrow.hasTag('arrow'):
                self.selected_arrow = picked_arrow
                self.dragging_arrow = True

                print("🎯 تم تحديد سهم التحريك")

                # تغيير لون السهم المحدد
                picked_arrow.setColor(1, 1, 1, 1)  # أبيض للتحديد

                return True

            return False

        except Exception as e:
            print(f"خطأ في تحديد السهم: {e}")
            return False

    def move_object_with_arrow(self, direction, distance=0.5):
        """تحريك الكائن المحدد باستخدام السهم"""
        try:
            if not self.selected_object:
                return

            current_pos = self.selected_object.getPos()
            new_pos = Point3(current_pos)

            # تحديد الاتجاه والحركة
            if direction == 'up':
                new_pos.setY(current_pos.getY() + distance)
            elif direction == 'down':
                new_pos.setY(current_pos.getY() - distance)
            elif direction == 'right':
                new_pos.setX(current_pos.getX() + distance)
            elif direction == 'left':
                new_pos.setX(current_pos.getX() - distance)

            # تطبيق الموضع الجديد
            self.selected_object.setPos(new_pos)

            # تحديث موضع أسهم التحكم
            if self.control_arrows:
                self.control_arrows.setPos(new_pos)

            print(f"➡️ تم تحريك الكائن {direction} إلى الموضع: ({new_pos.getX():.2f}, {new_pos.getY():.2f})")

        except Exception as e:
            print(f"خطأ في تحريك الكائن بالسهم: {e}")

    def deselect_object(self):
        """إلغاء تحديد الكائن المحدد"""
        if self.selected_object:
            # إعادة اللون الأصلي
            object_type = self.selected_object.getTag('object_type')
            if object_type == 'chair':
                self.selected_object.setColor(0.55, 0.27, 0.07, 1)  # بني
            elif object_type == 'old_chair':
                self.selected_object.setColorScale(1.0, 0.9, 0.7, 1.0)  # لون خشبي دافئ

            obj_pos = self.selected_object.getPos()
            print(f"✅ تم إنهاء تحديد {object_type} في الموضع: ({obj_pos.getX():.2f}, {obj_pos.getY():.2f})")

            self.selected_object = None
            self.dragging_object = False

    def create_control_arrow(self, object_pos):
        """إنشاء سهم تحكم واحد بجانب الكائن المحدد"""
        try:
            # إزالة السهم السابق إن وجد
            self.remove_control_arrow()

            # إعدادات السهم
            arrow_size = 1.0
            arrow_color = (0, 0.8, 1, 1)  # أزرق فاتح واضح

            # موضع السهم بجانب الكائن
            arrow_position = Point3(object_pos.x + 1.5, object_pos.y, object_pos.z + 0.5)

            # إنشاء السهم
            self.control_arrow = self.create_move_arrow(arrow_position, arrow_color, arrow_size)

            if self.control_arrow:
                # إضافة علامات للسهم
                self.control_arrow.setTag('arrow', 'true')
                self.control_arrow.setTag('move_arrow', 'true')

                print("✅ تم إنشاء سهم التحكم")

        except Exception as e:
            print(f"خطأ في إنشاء سهم التحكم: {e}")
            import traceback
            traceback.print_exc()

    def create_single_arrow(self, direction, position, arrow_dir, color, length, thickness, head_size):
        """إنشاء سهم واحد"""
        try:
            from panda3d.core import CardMaker, Material

            # إنشاء عقدة السهم
            arrow_node = self.render.attachNewNode(f"arrow_{direction}")
            arrow_node.setPos(*position)

            # إنشاء جسم السهم (مستطيل)
            shaft_maker = CardMaker(f"arrow_shaft_{direction}")
            shaft_length = length * 0.7
            shaft_maker.setFrame(-thickness/2, thickness/2, 0, shaft_length)

            shaft_node = arrow_node.attachNewNode(shaft_maker.generate())
            shaft_node.setColor(*color)

            # إنشاء رأس السهم (مثلث)
            head_maker = CardMaker(f"arrow_head_{direction}")
            head_maker.setFrame(-head_size/2, head_size/2, shaft_length, shaft_length + head_size)

            head_node = arrow_node.attachNewNode(head_maker.generate())
            head_node.setColor(*color)

            # توجيه السهم حسب الاتجاه
            if direction == 'up':
                arrow_node.setH(90)  # دوران لأعلى
            elif direction == 'down':
                arrow_node.setH(-90)  # دوران لأسفل
            elif direction == 'right':
                arrow_node.setH(0)   # يمين (افتراضي)
            elif direction == 'left':
                arrow_node.setH(180) # يسار

            # إضافة مادة للسهم
            material = Material()
            material.setAmbient(color)
            material.setDiffuse(color)
            material.setSpecular((1, 1, 1, 1))
            material.setShininess(50.0)
            arrow_node.setMaterial(material)

            # جعل السهم قابل للنقر
            arrow_node.setCollideMask(GeomNode.getDefaultCollideMask())

            return arrow_node

        except Exception as e:
            print(f"خطأ في إنشاء السهم {direction}: {e}")
            return None

    def create_move_arrow(self, position, color, size):
        """إنشاء سهم تحريك واضح وكبير"""
        try:
            from panda3d.core import CardMaker, Material

            # إنشاء عقدة السهم
            arrow_node = self.render.attachNewNode("move_arrow")
            arrow_node.setPos(position)

            # إنشاء جسم السهم (مستطيل عريض)
            shaft_maker = CardMaker("arrow_shaft")
            shaft_width = size * 0.3
            shaft_length = size * 0.8
            shaft_maker.setFrame(-shaft_width/2, shaft_width/2, 0, shaft_length)

            shaft_node = arrow_node.attachNewNode(shaft_maker.generate())
            shaft_node.setColor(*color)

            # إنشاء رأس السهم (مثلث كبير)
            head_maker = CardMaker("arrow_head")
            head_size = size * 0.5
            head_maker.setFrame(-head_size/2, head_size/2, shaft_length, shaft_length + head_size)

            head_node = arrow_node.attachNewNode(head_maker.generate())
            head_node.setColor(*color)

            # إضافة مادة للسهم
            material = Material()
            material.setAmbient(color)
            material.setDiffuse(color)
            material.setSpecular((1, 1, 1, 1))
            material.setShininess(50.0)
            arrow_node.setMaterial(material)

            # جعل السهم قابل للنقر
            arrow_node.setCollideMask(GeomNode.getDefaultCollideMask())

            # إضافة نص توضيحي
            self.add_arrow_label(arrow_node, "اسحب لتحريك")

            return arrow_node

        except Exception as e:
            print(f"خطأ في إنشاء سهم التحريك: {e}")
            return None

    def add_arrow_label(self, arrow_node, text):
        """إضافة نص توضيحي للسهم"""
        try:
            from panda3d.core import TextNode

            # إنشاء نص
            text_node = TextNode('arrow_label')
            text_node.setText(text)
            text_node.setAlign(TextNode.ACenter)

            # إضافة النص للسهم
            text_np = arrow_node.attachNewNode(text_node)
            text_np.setPos(0, 0, 1.5)
            text_np.setScale(0.3)
            text_np.setColor(1, 1, 1, 1)  # أبيض

        except Exception as e:
            print(f"خطأ في إضافة النص: {e}")

    def remove_control_arrow(self):
        """إزالة سهم التحكم"""
        try:
            if self.control_arrow:
                self.control_arrow.removeNode()
                self.control_arrow = None

            self.selected_arrow = None
            self.dragging_arrow = False

        except Exception as e:
            print(f"خطأ في إزالة سهم التحكم: {e}")

    def mouse_control_task(self, task):
        if not self.mouseWatcherNode.hasMouse():
            return Task.cont

        mx = self.mouseWatcherNode.getMouseX()
        my = self.mouseWatcherNode.getMouseY()
        delta_x = mx - self.last_mouse_x
        delta_y = my - self.last_mouse_y

        # تحريك الكائن المحدد بالسحب المباشر (أولوية عالية)
        if self.mouse_btn_down[0] and self.selected_object and self.movement_mode == 'drag':
            # تحريك محسن ومرن أثناء السحب
            movement_speed = 5.0  # سرعة تحريك محسنة

            current_pos = self.selected_object.getPos()
            new_pos = Point3(current_pos)

            # تحريك سلس في جميع الاتجاهات حسب حركة الماوس
            # تحسين الاستجابة للحركة
            move_x = delta_x * movement_speed
            move_y = -delta_y * movement_speed  # عكس Y للحركة الطبيعية

            # تطبيق التحريك مع تنعيم
            new_pos.setX(current_pos.getX() + move_x)
            new_pos.setY(current_pos.getY() + move_y)

            # ضمان بقاء الكائن على الأرض
            new_pos.setZ(0)

            # تطبيق الموضع الجديد
            self.selected_object.setPos(new_pos)

            # إظهار الإحداثيات أثناء التحريك للمساعدة
            if abs(move_x) > 0.01 or abs(move_y) > 0.01:  # فقط عند الحركة الفعلية
                print(f"🔄 تحريك إلى: ({new_pos.x:.2f}, {new_pos.y:.2f})", end='\r')

        # تدوير الكاميرا (زر الماوس الأيسر أو الأوسط - بدون سحب سهم)
        elif self.mouse_btn_down[0] or self.mouse_btn_down[1]:
            # تحسين حساسية الدوران لتكون أكثر سلاسة
            rotation_sensitivity = 120  # تقليل الحساسية قليلاً للتحكم الأفضل

            # تطبيق التدوير مع تنعيم
            heading_change = -delta_x * rotation_sensitivity
            pitch_change = delta_y * rotation_sensitivity

            # تحديث زوايا الكاميرا مع حدود آمنة
            self.cam_heading += heading_change
            self.cam_pitch = max(-85, min(85, self.cam_pitch + pitch_change))  # حدود أكثر راحة
            self.update_camera_pos()

        # تحريك الكاميرا أفقيًا (زر الماوس الأيمن)
        elif self.mouse_btn_down[2]:
            right = self.camera.getQuat().getRight()
            forward = self.camera.getQuat().getForward()

            # تحريك هدف الكاميرا
            self.cam_target += right * (-delta_x * 10)
            self.cam_target += forward * (delta_y * 10)
            self.update_camera_pos()

        self.last_mouse_x = mx
        self.last_mouse_y = my
        return Task.cont

    def update_camera_pos(self):
        """تحديث موضع واتجاه الكاميرا بناءً على الهدف والمسافة والزوايا."""
        rad_pitch = math.radians(self.cam_pitch)
        rad_heading = math.radians(self.cam_heading)

        # حساب موضع الكاميرا بالنسبة للهدف
        cam_x_rel = self.cam_distance * math.sin(rad_heading) * math.cos(rad_pitch)
        cam_y_rel = -self.cam_distance * math.cos(rad_heading) * math.cos(rad_pitch)
        cam_z_rel = self.cam_distance * math.sin(rad_pitch)

        # إضافة موضع الهدف للحصول على الموضع العالمي للكاميرا
        self.camera.setPos(
            self.cam_target.getX() + cam_x_rel,
            self.cam_target.getY() + cam_y_rel,
            self.cam_target.getZ() + cam_z_rel
        )
        self.camera.lookAt(self.cam_target)

    def update_camera_position(self):
        """دالة محسنة لتحديث موضع الكاميرا"""
        self.update_camera_pos()

    def show_instructions(self):
        print("🎮 مرحباً بك في عارض التصميم ثلاثي الأبعاد المحسن!")
        print("🏠 الصفحة الجانبية:")
        print("   • نافذة أدوات الأثاث مفتوحة بجانب العرض ثلاثي الأبعاد")
        print("   • أزرار سهلة لإضافة الكراسي والأثاث:")
        print("     🪑 كرسي عادي (بني بسيط - 0.5 متر)")
        print("     🪑 كرسي خشبي قديم (FBX + 6 تكسشر متقدمة - 0.5 متر)")
        print("       ✨ يدعم: Albedo, Normal, Metallic, Roughness, AO, BColor")
        print("       🎨 جودة استثنائية مع تفاصيل واقعية")
        print("   • تحكم في وضع التحريك من الصفحة الجانبية")
        print("   • معلومات مفيدة وتعليمات الاستخدام")
        print("📷 الكاميرا الذكية:")
        print("   • توجه تلقائي نحو المبنى عند بدء العرض ثلاثي الأبعاد")
        print("   • مسافة مثالية تتناسب مع حجم المبنى")
        print("   • زاوية عرض محسنة لأفضل رؤية")
        print("🖱️ التحكم المحسن بالماوس:")
        print("   • النقر على كائن: تحديد الكائن وإظهار سهم التحكم")
        print("   • زر الماوس الأيسر + السحب (بدون سهم): دوران سلس للكاميرا")
        print("   • زر الماوس الأوسط + السحب: دوران الكاميرا حول المشهد")
        print("   • زر الماوس الأيمن + السحب: تحريك الكاميرا أفقيًا")
        print("   • عجلة الماوس: تكبير/تصغير ذكي ومتناسب")
        print("🎯 تحريك الكائنات - طرق متعددة:")
        print("   • انقر على أي كائن (كرسي، طاولة، إلخ) لتحديده (سيصبح أصفر)")
        print("   • اضغط Q لتغيير وضع التحريك بين:")
        print("     🖱️ السحب المباشر: اسحب الكائن بالماوس مباشرة")
        print("     ⌨️ المفاتيح: استخدم W/A/S/D لتحريك الكائن")
        print("   • التحريك السريع والدقيق مع عرض الإحداثيات")
        print("   • يدعم جميع ملفات OBJ والكائنات القابلة للتحريك")
        print("➕ إضافة الأثاث:")
        print("   • اضغط C: تفعيل وضع إضافة الكراسي")
        print("   • انقر في أي مكان لإضافة كرسي")
        print("⌨️ لوحة المفاتيح:")
        print("   • الأسهم: دوران الكاميرا (يسار/يمين/أعلى/أسفل)")
        print("   • Page Up/Down: تكبير/تصغير")
        print("   • C: إضافة كرسي")
        print("   • Q: تغيير وضع التحريك (سحب/مفاتيح)")
        print("   • W/A/S/D: تحريك الكائن المحدد (في وضع المفاتيح)")
        print("   • ESC: إلغاء وضع الإضافة أو العودة للتطبيق الرئيسي")
        print("=" * 50)

    def ensure_basic_models(self):
        """التأكد من وجود النماذج الأساسية"""
        try:
            # التأكد من وجود مجلد النماذج
            models_dir = "models"
            if not os.path.exists(models_dir):
                os.makedirs(models_dir)
                print(f"تم إنشاء مجلد النماذج: {models_dir}")

            # إنشاء نموذج مكعب بسيط إذا لم يكن موجودًا
            box_model_path = os.path.join(models_dir, "box.egg")
            if not os.path.exists(box_model_path):
                self.create_simple_box_model(box_model_path)
                print(f"تم إنشاء نموذج المكعب في: {box_model_path}")
            else:
                print(f"نموذج المكعب موجود بالفعل في: {box_model_path}")

        except Exception as e:
            print(f"خطأ في التأكد من وجود النماذج الأساسية: {e}")

    def create_simple_box_model(self, box_model_path):
        """إنشاء ملف نموذج مكعب بسيط"""
        try:
            from panda3d.core import EggData, EggVertex, EggPolygon, EggVertexPool

            # إنشاء ملف EGG جديد
            egg_data = EggData()

            # إنشاء مجموعة نقاط
            vpool = EggVertexPool("box")
            egg_data.addChild(vpool)

            # إنشاء النقاط الثمانية للمكعب
            v1 = EggVertex()
            v1.setPos(-0.5, -0.5, -0.5)
            vpool.addVertex(v1)

            v2 = EggVertex()
            v2.setPos(0.5, -0.5, -0.5)
            vpool.addVertex(v2)

            v3 = EggVertex()
            v3.setPos(0.5, 0.5, -0.5)
            vpool.addVertex(v3)

            v4 = EggVertex()
            v4.setPos(-0.5, 0.5, -0.5)
            vpool.addVertex(v4)

            v5 = EggVertex()
            v5.setPos(-0.5, -0.5, 0.5)
            vpool.addVertex(v5)

            v6 = EggVertex()
            v6.setPos(0.5, -0.5, 0.5)
            vpool.addVertex(v6)

            v7 = EggVertex()
            v7.setPos(0.5, 0.5, 0.5)
            vpool.addVertex(v7)

            v8 = EggVertex()
            v8.setPos(-0.5, 0.5, 0.5)
            vpool.addVertex(v8)

            # إنشاء الوجوه الستة للمكعب
            # الوجه السفلي
            poly = EggPolygon()
            poly.addVertex(v1)
            poly.addVertex(v2)
            poly.addVertex(v3)
            poly.addVertex(v4)
            egg_data.addChild(poly)

            # الوجه العلوي
            poly = EggPolygon()
            poly.addVertex(v5)
            poly.addVertex(v8)
            poly.addVertex(v7)
            poly.addVertex(v6)
            egg_data.addChild(poly)

            # الوجه الأمامي
            poly = EggPolygon()
            poly.addVertex(v1)
            poly.addVertex(v5)
            poly.addVertex(v6)
            poly.addVertex(v2)
            egg_data.addChild(poly)

            # الوجه الخلفي
            poly = EggPolygon()
            poly.addVertex(v3)
            poly.addVertex(v7)
            poly.addVertex(v8)
            poly.addVertex(v4)
            egg_data.addChild(poly)

            # الوجه الأيسر
            poly = EggPolygon()
            poly.addVertex(v1)
            poly.addVertex(v4)
            poly.addVertex(v8)
            poly.addVertex(v5)
            egg_data.addChild(poly)

            # الوجه الأيمن
            poly = EggPolygon()
            poly.addVertex(v2)
            poly.addVertex(v6)
            poly.addVertex(v7)
            poly.addVertex(v3)
            egg_data.addChild(poly)

            # حفظ الملف
            egg_data.writeEgg(Filename.fromOsSpecific(box_model_path))
            print(f"تم إنشاء نموذج مكعب بسيط في: {box_model_path}")

        except Exception as e:
            print(f"خطأ في إنشاء نموذج المكعب: {e}")
            import traceback
            traceback.print_exc()

            # محاولة إنشاء ملف بطريقة أبسط
            self.create_fallback_box_model(box_model_path)  # مكعب جاهز

    # دالة create_coordinate_axes محذوفة - المحاور مخفية نهائياً

def launch_3d_viewer(design_file_path=None):
    """
    تشغيل عارض التصميم ثلاثي الأبعاد

    :param design_file_path: مسار ملف التصميم (JSON)
    """
    try:
        # التأكد من وجود مجلد النماذج
        models_dir = "models"
        if not os.path.exists(models_dir):
            os.makedirs(models_dir)
            print(f"تم إنشاء مجلد النماذج: {models_dir}")

        # تشغيل العارض
        print("🚀 بدء تشغيل العارض ثلاثي الأبعاد...")
        print("🎮 التحكم التفاعلي:")
        print("🪑 تحريك الكراسي:")
        print("   • انقر على أي كرسي لتحديده (سيصبح أصفر)")
        print("   • اسحب الكرسي إلى أي مكان على الشبكة")
        print("   • اتركه في المكان المطلوب")
        print("➕ إضافة الأثاث:")
        print("   • اضغط C: تفعيل وضع إضافة الكراسي")
        print("   • انقر في أي مكان لإضافة العنصر المحدد")
        print("🖱️ التحكم بالكاميرا:")
        print("   • اسحب بالزر الأيسر (بدون كائن محدد): دوران الكاميرا")
        print("   • اسحب بالزر الأوسط: دوران الكاميرا حول المشهد")
        print("   • اسحب بالزر الأيمن: تحريك الكاميرا أفقيًا")
        print("   • عجلة الماوس: تكبير/تصغير")
        print("⌨️ لوحة المفاتيح:")
        print("   • الأسهم: دوران الكاميرا")
        print("   • Page Up/Down: تكبير/تصغير")
        print("   • C: تفعيل وضع إضافة الكراسي")
        print("   • B: تفعيل وضع إضافة سيارات Bugatti")
        print("   • Q: تغيير وضع التحريك (سحب/مفاتيح)")
        print("   • W/A/S/D: تحريك الكائن المحدد (في وضع المفاتيح)")
        print("   • ESC: إلغاء وضع الإضافة أو الخروج والعودة للتطبيق الرئيسي")
        print("=" * 60)

        app = Design3DViewer(design_file_path)
        app.run()
        print("✅ تم إغلاق العارض ثلاثي الأبعاد")

    except Exception as e:
        print(f"خطأ في تشغيل العارض ثلاثي الأبعاد: {e}")
        import traceback
        traceback.print_exc()

def create_simple_wall_model(wall_model_path):
    """إنشاء ملف نموذج جدار بسيط إذا لم يكن موجودًا"""
    try:
        with open(wall_model_path, 'w') as f:
            f.write("# Simple wall model for 3D viewer\n")
            f.write("# Created automatically\n")
            f.write("v 0.0 0.0 0.0\n")  # Bottom left
            f.write("v 1.0 0.0 0.0\n")  # Bottom right
            f.write("v 1.0 0.0 1.0\n")  # Top right
            f.write("v 0.0 0.0 1.0\n")  # Top left
            f.write("vt 0.0 0.0\n")
            f.write("vt 1.0 0.0\n")
            f.write("vt 1.0 1.0\n")
            f.write("vt 0.0 1.0\n")
            f.write("vn 0.0 1.0 0.0\n")
            f.write("f 1/1/1 2/2/1 3/3/1 4/4/1\n")
        print(f"تم إنشاء نموذج جدار بسيط في: {wall_model_path}")
    except Exception as e:
        print(f"خطأ في إنشاء نموذج الجدار: {e}")

if __name__ == "__main__":
    # للاختبار المستقل
    if len(sys.argv) > 1:
        launch_3d_viewer(sys.argv[1])
    else:
        launch_3d_viewer()

