#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار قاعدة البيانات فقط
"""

from database import UserDatabase

def main():
    print("🔍 اختبار قاعدة البيانات...")
    
    try:
        db = UserDatabase()
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
        
        # اختبار التحقق من المستخدم الافتراضي
        user_data = db.verify_user("admin", "admin123")
        
        if user_data:
            print(f"✅ المستخدم الافتراضي موجود:")
            print(f"   - المعرف: {user_data['id']}")
            print(f"   - اسم المستخدم: {user_data['username']}")
            print(f"   - الاسم الكامل: {user_data['full_name']}")
        else:
            print("❌ المستخدم الافتراضي غير موجود")
        
        # اختبار إنشاء مستخدم جديد
        new_user_id = db.create_user(
            username="test123",
            password="password123",
            full_name="مستخدم تجريبي",
            email="<EMAIL>"
        )
        
        if new_user_id:
            print(f"✅ تم إنشاء مستخدم جديد بمعرف: {new_user_id}")
        else:
            print("⚠️ المستخدم موجود بالفعل أو حدث خطأ")
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
