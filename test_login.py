#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار صفحة تسجيل الدخول
"""

from login_page import show_login_page
from database import UserDatabase

def test_database():
    """اختبار قاعدة البيانات"""
    print("🔍 اختبار قاعدة البيانات...")
    
    db = UserDatabase()
    
    # اختبار إنشاء مستخدم جديد
    print("\n📝 اختبار إنشاء مستخدم جديد...")
    user_id = db.create_user(
        username="test_user",
        password="test123",
        email="<EMAIL>",
        full_name="مستخدم تجريبي",
        phone="123456789"
    )
    
    if user_id:
        print(f"✅ تم إنشاء المستخدم بنجاح. معرف المستخدم: {user_id}")
    else:
        print("❌ فشل في إنشاء المستخدم")
    
    # اختبار التحقق من المستخدم
    print("\n🔐 اختبار تسجيل الدخول...")
    user_data = db.verify_user("admin", "admin123")
    
    if user_data:
        print(f"✅ تم تسجيل الدخول بنجاح:")
        print(f"   - المعرف: {user_data['id']}")
        print(f"   - اسم المستخدم: {user_data['username']}")
        print(f"   - الاسم الكامل: {user_data['full_name']}")
        print(f"   - البريد الإلكتروني: {user_data['email']}")
        print(f"   - الهاتف: {user_data['phone']}")
    else:
        print("❌ فشل في تسجيل الدخول")
    
    # اختبار بيانات خاطئة
    print("\n❌ اختبار بيانات خاطئة...")
    wrong_user = db.verify_user("wrong_user", "wrong_pass")
    
    if wrong_user:
        print("❌ خطأ: تم قبول بيانات خاطئة!")
    else:
        print("✅ تم رفض البيانات الخاطئة بنجاح")

def test_login_page():
    """اختبار صفحة تسجيل الدخول"""
    print("\n🖥️ اختبار صفحة تسجيل الدخول...")
    print("سيتم فتح صفحة تسجيل الدخول...")
    print("استخدم البيانات التالية للاختبار:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("أو يمكنك إنشاء حساب جديد")
    
    user_data = show_login_page()
    
    if user_data:
        print(f"\n✅ تم تسجيل الدخول بنجاح!")
        print(f"بيانات المستخدم: {user_data}")
        return user_data
    else:
        print("\n❌ تم إلغاء تسجيل الدخول")
        return None

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار نظام تسجيل الدخول")
    print("=" * 50)
    
    # اختبار قاعدة البيانات
    test_database()
    
    print("\n" + "=" * 50)
    
    # اختبار صفحة تسجيل الدخول
    user_data = test_login_page()
    
    print("\n" + "=" * 50)
    print("🏁 انتهاء الاختبار")
    
    if user_data:
        print(f"المستخدم المسجل: {user_data['full_name']} ({user_data['username']})")
    else:
        print("لم يتم تسجيل دخول أي مستخدم")

if __name__ == "__main__":
    main()
