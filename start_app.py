#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🏗️ Architectural Design System with English Login
Professional startup script with comprehensive checks
"""

import sys
import os

def print_banner():
    """Display application banner"""
    print("=" * 60)
    print("🏗️  ARCHITECTURAL DESIGN SYSTEM")
    print("🔐  Professional Login System (English Interface)")
    print("🎨  Advanced 3D Visualization & Design Tools")
    print("=" * 60)

def check_python_version():
    """Check Python version compatibility"""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_dependencies():
    """Check required dependencies"""
    print("\n📦 Checking dependencies...")
    
    dependencies = {
        'pygame': 'pip install pygame',
        'sqlite3': 'Built-in module',
        'tkinter': 'Built-in module (usually)',
        'hashlib': 'Built-in module',
        'json': 'Built-in module'
    }
    
    missing = []
    
    for module, install_cmd in dependencies.items():
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - {install_cmd}")
            missing.append(module)
    
    if missing:
        print(f"\n⚠️  Missing dependencies: {', '.join(missing)}")
        print("Please install missing packages and try again.")
        return False
    
    return True

def check_database():
    """Check database setup"""
    print("\n🗄️  Checking database...")
    
    try:
        from database import UserDatabase
        db = UserDatabase()
        
        # Test default user
        admin_user = db.verify_user("admin", "admin123")
        if admin_user:
            print("✅ Database initialized successfully")
            print("✅ Default admin user available")
            return True
        else:
            print("⚠️  Database exists but admin user not found")
            return True  # Still functional
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def show_login_info():
    """Display login information"""
    print("\n🔐 LOGIN INFORMATION")
    print("-" * 30)
    print("Default Account:")
    print("  Username: admin")
    print("  Password: admin123")
    print()
    print("Or create a new account using the 'Sign Up' button")
    print("-" * 30)

def show_features():
    """Display key features"""
    print("\n🌟 KEY FEATURES")
    print("-" * 30)
    print("✅ English Login Interface")
    print("✅ Secure User Authentication")
    print("✅ SQLite Database Integration")
    print("✅ 3D Architectural Visualization")
    print("✅ Advanced Design Tools")
    print("✅ Project Management")
    print("✅ Customer Data Management")
    print("-" * 30)

def run_application():
    """Run the main application"""
    print("\n🚀 Starting application...")
    
    try:
        from main import main
        print("📋 Opening English login interface...")
        print("🎯 Ready to design!")
        print()
        
        # Run the application
        main()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Application stopped by user")
    except Exception as e:
        print(f"\n❌ Application error: {e}")
        print("\nFor support, check the documentation files:")
        print("  - ENGLISH_LOGIN_GUIDE.md")
        print("  - QUICK_START.md")
        print("  - LOGIN_SYSTEM_README.md")

def main():
    """Main startup function"""
    print_banner()
    
    # System checks
    if not check_python_version():
        input("\nPress Enter to exit...")
        return
    
    if not check_dependencies():
        input("\nPress Enter to exit...")
        return
    
    if not check_database():
        print("⚠️  Database issues detected, but continuing...")
    
    # Information display
    show_features()
    show_login_info()
    
    # Confirmation
    print("\n" + "=" * 60)
    response = input("🎯 Ready to start? (Press Enter to continue, 'q' to quit): ").strip().lower()
    
    if response == 'q':
        print("👋 Goodbye!")
        return
    
    # Run application
    run_application()
    
    print("\n🏁 Application session ended")
    print("Thank you for using Architectural Design System!")

if __name__ == "__main__":
    main()
