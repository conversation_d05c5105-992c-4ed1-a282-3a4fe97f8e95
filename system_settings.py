#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
⚙️ System Settings - إعدادات النظام
للمدير الرئيسي فقط
"""

import pygame
import sys
import sqlite3
import os
import shutil
from datetime import datetime
from database import UserDatabase

# إعدادات الشاشة
SCREEN_WIDTH = 1000
SCREEN_HEIGHT = 700
BACKGROUND_COLOR = (245, 250, 255)
PRIMARY_COLOR = (70, 130, 180)
SECONDARY_COLOR = (255, 255, 255)
TEXT_COLOR = (25, 25, 112)
ACCENT_COLOR = (100, 149, 237)
SUCCESS_COLOR = (34, 139, 34)
WARNING_COLOR = (255, 165, 0)
ERROR_COLOR = (220, 20, 60)

class SystemSettings:
    def __init__(self, user_data):
        """تهيئة إعدادات النظام"""
        pygame.init()
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("System Settings - Super Admin Only")
        self.clock = pygame.time.Clock()

        # بيانات المستخدم
        self.user_data = user_data

        # التحقق من صلاحيات المدير الرئيسي
        if user_data['username'] != 'MohammedBushiha' or user_data['user_role'] != 'super_admin':
            print("❌ غير مصرح لك بالوصول لهذه الصفحة")
            pygame.quit()
            sys.exit()

        # تهيئة قاعدة البيانات
        self.db = UserDatabase()

        # تهيئة الخطوط
        self.title_font = pygame.font.Font(None, 36)
        self.header_font = pygame.font.Font(None, 28)
        self.text_font = pygame.font.Font(None, 24)
        self.button_font = pygame.font.Font(None, 22)

        # متغيرات الواجهة
        self.message = ""
        self.message_color = TEXT_COLOR
        self.message_timer = 0
        self.system_stats = {}

        # الأزرار
        self.buttons = {}
        self.create_buttons()

        # تحميل إحصائيات النظام
        self.load_system_stats()

        # حالة التطبيق
        self.running = True
        self.next_action = None

    def create_buttons(self):
        """إنشاء الأزرار"""
        button_width = 200
        button_height = 50
        start_x = 50
        start_y = 150
        spacing_x = 220
        spacing_y = 70

        # الأزرار الرئيسية
        buttons_data = [
            # الصف الأول
            ("backup_db", "Backup Database", start_x, start_y, SUCCESS_COLOR),
            ("cleanup_db", "Cleanup Database", start_x + spacing_x, start_y, WARNING_COLOR),
            ("export_data", "Export All Data", start_x + 2 * spacing_x, start_y, ACCENT_COLOR),

            # الصف الثاني
            ("view_logs", "View System Logs", start_x, start_y + spacing_y, PRIMARY_COLOR),
            ("reset_passwords", "Reset All Passwords", start_x + spacing_x, start_y + spacing_y, ERROR_COLOR),
            ("system_info", "System Information", start_x + 2 * spacing_x, start_y + spacing_y, TEXT_COLOR),

            # الصف الثالث
            ("optimize_db", "Optimize Database", start_x, start_y + 2 * spacing_y, SUCCESS_COLOR),
            ("clear_sessions", "Clear All Sessions", start_x + spacing_x, start_y + 2 * spacing_y, WARNING_COLOR),
            ("maintenance_mode", "Maintenance Mode", start_x + 2 * spacing_x, start_y + 2 * spacing_y, ERROR_COLOR),

            # أزرار التحكم
            ("refresh_stats", "Refresh Stats", start_x, start_y + 4 * spacing_y, ACCENT_COLOR),
            ("back", "Back to Dashboard", SCREEN_WIDTH - 320, SCREEN_HEIGHT - 80, TEXT_COLOR),
            ("logout", "Logout", SCREEN_WIDTH - 120, SCREEN_HEIGHT - 80, ERROR_COLOR)
        ]

        for button_id, text, x, y, color in buttons_data:
            if button_id == "back":
                width, height = 180, 40
            elif button_id == "logout":
                width, height = 100, 40
            else:
                width, height = button_width, button_height

            self.buttons[button_id] = {
                'rect': pygame.Rect(x, y, width, height),
                'text': text,
                'color': color,
                'hover': False
            }

    def load_system_stats(self):
        """تحميل إحصائيات النظام"""
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()

            # إحصائيات قاعدة البيانات
            cursor.execute('SELECT COUNT(*) FROM users')
            total_users = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 1')
            active_users = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM user_projects')
            total_projects = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM user_sessions')
            active_sessions = cursor.fetchone()[0]

            # حجم قاعدة البيانات
            db_size = os.path.getsize(self.db.db_path) if os.path.exists(self.db.db_path) else 0
            db_size_mb = db_size / (1024 * 1024)

            self.system_stats = {
                'total_users': total_users,
                'active_users': active_users,
                'inactive_users': total_users - active_users,
                'total_projects': total_projects,
                'active_sessions': active_sessions,
                'db_size_mb': round(db_size_mb, 2),
                'last_backup': 'Never',  # يمكن تحديثه لاحقاً
                'system_uptime': 'Running'
            }

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل إحصائيات النظام: {e}")

    def draw_background(self):
        """رسم الخلفية"""
        self.screen.fill(BACKGROUND_COLOR)

        # رسم العنوان الرئيسي
        title_text = "System Settings - Super Admin Panel"
        title_surface = self.title_font.render(title_text, True, PRIMARY_COLOR)
        title_rect = title_surface.get_rect(center=(SCREEN_WIDTH // 2, 30))
        self.screen.blit(title_surface, title_rect)

        # رسم خط فاصل
        pygame.draw.line(self.screen, PRIMARY_COLOR, (50, 60), (SCREEN_WIDTH - 50, 60), 2)

        # معلومات المدير
        admin_info = f"Super Admin: {self.user_data['username']} | Access Level: Full Control"
        info_surface = self.text_font.render(admin_info, True, TEXT_COLOR)
        self.screen.blit(info_surface, (50, 80))

    def draw_system_stats(self):
        """رسم إحصائيات النظام"""
        stats_x = 50
        stats_y = 450
        stats_width = SCREEN_WIDTH - 100
        stats_height = 180

        # إطار الإحصائيات
        stats_rect = pygame.Rect(stats_x, stats_y, stats_width, stats_height)
        pygame.draw.rect(self.screen, SECONDARY_COLOR, stats_rect, border_radius=10)
        pygame.draw.rect(self.screen, PRIMARY_COLOR, stats_rect, 2, border_radius=10)

        # عنوان الإحصائيات
        header_surface = self.header_font.render("System Statistics", True, PRIMARY_COLOR)
        self.screen.blit(header_surface, (stats_x + 20, stats_y + 15))

        # الإحصائيات
        stats_items = [
            ("👥 Total Users", str(self.system_stats['total_users'])),
            ("✅ Active Users", str(self.system_stats['active_users'])),
            ("❌ Inactive Users", str(self.system_stats['inactive_users'])),
            ("🏗️ Total Projects", str(self.system_stats['total_projects'])),
            ("🔗 Active Sessions", str(self.system_stats['active_sessions'])),
            ("💾 Database Size", f"{self.system_stats['db_size_mb']} MB"),
            ("🔄 Last Backup", self.system_stats['last_backup']),
            ("⚡ System Status", self.system_stats['system_uptime'])
        ]

        # عرض الإحصائيات في عمودين
        col1_x = stats_x + 30
        col2_x = stats_x + stats_width // 2 + 30
        start_y = stats_y + 50

        for i, (label, value) in enumerate(stats_items):
            if i < 4:
                x = col1_x
                y = start_y + i * 25
            else:
                x = col2_x
                y = start_y + (i - 4) * 25

            # التسمية
            label_surface = self.text_font.render(label, True, TEXT_COLOR)
            self.screen.blit(label_surface, (x, y))

            # القيمة
            value_surface = self.text_font.render(value, True, ACCENT_COLOR)
            value_rect = value_surface.get_rect(right=x + 350, y=y)
            self.screen.blit(value_surface, value_rect)

    def draw_buttons(self):
        """رسم الأزرار"""
        mouse_pos = pygame.mouse.get_pos()

        for button_id, button in self.buttons.items():
            # تحديث حالة التمرير
            button['hover'] = button['rect'].collidepoint(mouse_pos)

            # تحديد لون الزر
            if button['hover']:
                color = tuple(min(255, c + 30) for c in button['color'])
            else:
                color = button['color']

            # رسم الزر
            pygame.draw.rect(self.screen, color, button['rect'], border_radius=8)
            pygame.draw.rect(self.screen, TEXT_COLOR, button['rect'], 2, border_radius=8)

            # رسم النص
            text_surface = self.button_font.render(button['text'], True, SECONDARY_COLOR)
            text_rect = text_surface.get_rect(center=button['rect'].center)
            self.screen.blit(text_surface, text_rect)

    def draw_message(self):
        """رسم الرسائل"""
        if self.message and self.message_timer > 0:
            message_surface = self.text_font.render(self.message, True, self.message_color)
            message_rect = message_surface.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 30))

            # رسم خلفية للرسالة
            bg_rect = message_rect.inflate(20, 10)
            pygame.draw.rect(self.screen, SECONDARY_COLOR, bg_rect, border_radius=5)
            pygame.draw.rect(self.screen, self.message_color, bg_rect, 2, border_radius=5)

            self.screen.blit(message_surface, message_rect)
            self.message_timer -= 1

    def show_message(self, message, color):
        """عرض رسالة"""
        self.message = message
        self.message_color = color
        self.message_timer = 180  # 3 ثوان بـ 60 FPS

    def handle_click(self, pos):
        """معالجة النقر"""
        for button_id, button in self.buttons.items():
            if button['rect'].collidepoint(pos):
                self.handle_button_click(button_id)
                break

    def handle_button_click(self, button_id):
        """معالجة النقر على الأزرار"""
        if button_id == "backup_db":
            self.backup_database()
        elif button_id == "cleanup_db":
            self.cleanup_database()
        elif button_id == "export_data":
            self.export_all_data()
        elif button_id == "view_logs":
            self.show_message("System logs feature coming soon", WARNING_COLOR)
        elif button_id == "reset_passwords":
            self.show_message("Password reset feature - Use with caution!", ERROR_COLOR)
        elif button_id == "system_info":
            self.show_system_info()
        elif button_id == "optimize_db":
            self.optimize_database()
        elif button_id == "clear_sessions":
            self.clear_all_sessions()
        elif button_id == "maintenance_mode":
            self.show_message("Maintenance mode feature coming soon", WARNING_COLOR)
        elif button_id == "refresh_stats":
            self.load_system_stats()
            self.show_message("System statistics refreshed", SUCCESS_COLOR)
        elif button_id == "back":
            self.next_action = "dashboard"
            self.running = False
        elif button_id == "logout":
            self.next_action = "logout"
            self.running = False

    def backup_database(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"backup_users_{timestamp}.db"

            if os.path.exists(self.db.db_path):
                shutil.copy2(self.db.db_path, backup_filename)
                self.show_message(f"Database backed up to {backup_filename}", SUCCESS_COLOR)
            else:
                self.show_message("Database file not found", ERROR_COLOR)

        except Exception as e:
            self.show_message(f"Backup failed: {e}", ERROR_COLOR)

    def cleanup_database(self):
        """تنظيف قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()

            # حذف الجلسات المنتهية الصلاحية
            cursor.execute('DELETE FROM user_sessions WHERE created_at < datetime("now", "-7 days")')
            deleted_sessions = cursor.rowcount

            # حذف المستخدمين المعطلين لأكثر من 30 يوم
            cursor.execute('DELETE FROM users WHERE is_active = 0 AND created_at < datetime("now", "-30 days")')
            deleted_users = cursor.rowcount

            conn.commit()
            conn.close()

            self.show_message(f"Cleanup completed: {deleted_sessions} sessions, {deleted_users} users", SUCCESS_COLOR)
            self.load_system_stats()

        except Exception as e:
            self.show_message(f"Cleanup failed: {e}", ERROR_COLOR)

    def export_all_data(self):
        """تصدير جميع البيانات"""
        try:
            import json

            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()

            # تصدير المستخدمين
            cursor.execute('SELECT * FROM users')
            users = cursor.fetchall()

            # تصدير المشاريع
            cursor.execute('SELECT * FROM user_projects')
            projects = cursor.fetchall()

            export_data = {
                'export_date': datetime.now().isoformat(),
                'users_count': len(users),
                'projects_count': len(projects),
                'users': users,
                'projects': projects
            }

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_filename = f"export_data_{timestamp}.json"

            with open(export_filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            conn.close()

            self.show_message(f"Data exported to {export_filename}", SUCCESS_COLOR)

        except Exception as e:
            self.show_message(f"Export failed: {e}", ERROR_COLOR)

    def show_system_info(self):
        """عرض معلومات النظام"""
        info_text = f"DB Size: {self.system_stats['db_size_mb']}MB | Users: {self.system_stats['total_users']} | Projects: {self.system_stats['total_projects']}"
        self.show_message(info_text, ACCENT_COLOR)

    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()

            cursor.execute('VACUUM')
            cursor.execute('ANALYZE')

            conn.close()

            self.show_message("Database optimized successfully", SUCCESS_COLOR)
            self.load_system_stats()

        except Exception as e:
            self.show_message(f"Optimization failed: {e}", ERROR_COLOR)

    def clear_all_sessions(self):
        """مسح جميع الجلسات"""
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()

            cursor.execute('DELETE FROM user_sessions')
            deleted_count = cursor.rowcount

            conn.commit()
            conn.close()

            self.show_message(f"Cleared {deleted_count} sessions", SUCCESS_COLOR)
            self.load_system_stats()

        except Exception as e:
            self.show_message(f"Clear sessions failed: {e}", ERROR_COLOR)

    def run(self):
        """تشغيل إعدادات النظام"""
        while self.running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False

                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # النقر بالزر الأيسر
                        self.handle_click(event.pos)

                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        self.running = False
                    elif event.key == pygame.K_F5:
                        self.load_system_stats()
                        self.show_message("System statistics refreshed", SUCCESS_COLOR)

            # رسم الشاشة
            self.draw_background()
            self.draw_buttons()
            self.draw_system_stats()
            self.draw_message()

            pygame.display.flip()
            self.clock.tick(60)

        pygame.quit()
        return self.next_action or "dashboard"

def show_system_settings(user_data):
    """عرض إعدادات النظام"""
    settings = SystemSettings(user_data)
    return settings.run()

if __name__ == "__main__":
    # اختبار إعدادات النظام
    from database import UserDatabase

    db = UserDatabase()
    admin_user = db.verify_user("MohammedBushiha", "Mfb112002*")

    if admin_user:
        show_system_settings(admin_user)
    else:
        print("❌ فشل في تسجيل الدخول كمدير رئيسي")
