#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🖥️ Database GUI Manager
واجهة رسومية لإدارة قاعدة البيانات
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sqlite3
from database import UserDatabase
from datetime import datetime

class DatabaseGUI:
    def __init__(self, root):
        """تهيئة الواجهة الرسومية"""
        self.root = root
        self.root.title("🗄️ Database Manager - نظام التصميم المعماري")
        self.root.geometry("1000x700")
        
        # تهيئة قاعدة البيانات
        self.db = UserDatabase()
        
        # إنشاء الواجهة
        self.create_widgets()
        self.refresh_users()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        title_label = ttk.Label(main_frame, text="🗄️ مدير قاعدة البيانات", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # إطار الأزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        # الأزرار
        ttk.Button(button_frame, text="➕ إضافة مستخدم", command=self.add_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="✏️ تعديل", command=self.edit_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="🔒 تغيير كلمة المرور", command=self.change_password).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="🔄 تحديث", command=self.refresh_users).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="❌ حذف", command=self.delete_user).pack(side=tk.LEFT, padx=(0, 5))
        
        # إطار الجدول
        table_frame = ttk.Frame(main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # جدول المستخدمين
        columns = ("ID", "Username", "Full Name", "Email", "Phone", "Created", "Last Login", "Active")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # ترتيب الجدول وشريط التمرير
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إطار الإحصائيات
        stats_frame = ttk.LabelFrame(main_frame, text="📊 الإحصائيات", padding="10")
        stats_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.stats_label = ttk.Label(stats_frame, text="جاري تحميل الإحصائيات...")
        self.stats_label.pack()
        
        # ربط النقر المزدوج
        self.tree.bind("<Double-1>", self.on_double_click)
    
    def refresh_users(self):
        """تحديث قائمة المستخدمين"""
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, username, full_name, email, phone, created_at, last_login, is_active
                FROM users
                ORDER BY id
            ''')
            
            users = cursor.fetchall()
            
            # إضافة البيانات للجدول
            for user in users:
                active_text = "✅ نعم" if user[7] else "❌ لا"
                created_date = user[5][:10] if user[5] else "N/A"
                last_login = user[6][:10] if user[6] else "لم يسجل دخول"
                
                self.tree.insert("", tk.END, values=(
                    user[0],  # ID
                    user[1],  # Username
                    user[2] or "N/A",  # Full Name
                    user[3] or "N/A",  # Email
                    user[4] or "N/A",  # Phone
                    created_date,  # Created
                    last_login,  # Last Login
                    active_text  # Active
                ))
            
            # تحديث الإحصائيات
            self.update_stats(len(users))
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {e}")
    
    def update_stats(self, total_users):
        """تحديث الإحصائيات"""
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 1')
            active_users = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM user_projects')
            total_projects = cursor.fetchone()[0]
            
            stats_text = f"👥 إجمالي المستخدمين: {total_users} | ✅ النشطين: {active_users} | 📁 المشاريع: {total_projects}"
            self.stats_label.config(text=stats_text)
            
            conn.close()
            
        except Exception as e:
            self.stats_label.config(text=f"خطأ في الإحصائيات: {e}")
    
    def get_selected_user(self):
        """الحصول على المستخدم المحدد"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد مستخدم أولاً")
            return None
        
        item = self.tree.item(selection[0])
        user_id = item['values'][0]
        return user_id
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        dialog = UserDialog(self.root, "إضافة مستخدم جديد")
        if dialog.result:
            try:
                user_id = self.db.create_user(
                    username=dialog.result['username'],
                    password=dialog.result['password'],
                    email=dialog.result['email'] or None,
                    full_name=dialog.result['full_name'] or None,
                    phone=dialog.result['phone'] or None
                )
                
                if user_id:
                    messagebox.showinfo("نجح", "تم إضافة المستخدم بنجاح!")
                    self.refresh_users()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة المستخدم. اسم المستخدم موجود بالفعل.")
                    
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إضافة المستخدم: {e}")
    
    def edit_user(self):
        """تعديل مستخدم"""
        user_id = self.get_selected_user()
        if not user_id:
            return
        
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
            user = cursor.fetchone()
            
            if not user:
                messagebox.showerror("خطأ", "لم يتم العثور على المستخدم")
                return
            
            # إنشاء حوار التعديل مع البيانات الحالية
            dialog = UserDialog(self.root, "تعديل المستخدم", {
                'username': user[1],
                'full_name': user[4] or '',
                'email': user[3] or '',
                'phone': user[5] or ''
            })
            
            if dialog.result:
                cursor.execute('''
                    UPDATE users 
                    SET username = ?, full_name = ?, email = ?, phone = ?
                    WHERE id = ?
                ''', (
                    dialog.result['username'],
                    dialog.result['full_name'] or None,
                    dialog.result['email'] or None,
                    dialog.result['phone'] or None,
                    user_id
                ))
                
                conn.commit()
                messagebox.showinfo("نجح", "تم تحديث المستخدم بنجاح!")
                self.refresh_users()
            
            conn.close()
            
        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "اسم المستخدم موجود بالفعل")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تعديل المستخدم: {e}")
    
    def change_password(self):
        """تغيير كلمة المرور"""
        user_id = self.get_selected_user()
        if not user_id:
            return
        
        password = simpledialog.askstring("تغيير كلمة المرور", "أدخل كلمة المرور الجديدة:", show='*')
        if not password:
            return
        
        confirm = simpledialog.askstring("تأكيد كلمة المرور", "أعد إدخال كلمة المرور:", show='*')
        if password != confirm:
            messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
            return
        
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()
            
            password_hash = self.db.hash_password(password)
            cursor.execute('UPDATE users SET password_hash = ? WHERE id = ?', (password_hash, user_id))
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("نجح", "تم تغيير كلمة المرور بنجاح!")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تغيير كلمة المرور: {e}")
    
    def delete_user(self):
        """حذف مستخدم"""
        user_id = self.get_selected_user()
        if not user_id:
            return
        
        # تأكيد الحذف
        if not messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المستخدم؟\nسيتم حذف جميع مشاريعه أيضاً."):
            return
        
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()
            
            # حذف مشاريع المستخدم
            cursor.execute('DELETE FROM user_projects WHERE user_id = ?', (user_id,))
            
            # حذف المستخدم
            cursor.execute('DELETE FROM users WHERE id = ?', (user_id,))
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("نجح", "تم حذف المستخدم بنجاح!")
            self.refresh_users()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حذف المستخدم: {e}")
    
    def on_double_click(self, event):
        """معالجة النقر المزدوج"""
        self.edit_user()

class UserDialog:
    def __init__(self, parent, title, initial_data=None):
        """حوار إضافة/تعديل المستخدم"""
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # متغيرات الحقول
        self.username_var = tk.StringVar(value=initial_data.get('username', '') if initial_data else '')
        self.full_name_var = tk.StringVar(value=initial_data.get('full_name', '') if initial_data else '')
        self.email_var = tk.StringVar(value=initial_data.get('email', '') if initial_data else '')
        self.phone_var = tk.StringVar(value=initial_data.get('phone', '') if initial_data else '')
        self.password_var = tk.StringVar()
        
        self.create_dialog_widgets(initial_data is not None)
        
        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
        # انتظار إغلاق النافذة
        self.dialog.wait_window()
    
    def create_dialog_widgets(self, is_edit):
        """إنشاء عناصر الحوار"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # الحقول
        ttk.Label(main_frame, text="اسم المستخدم:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.username_var, width=30).grid(row=0, column=1, pady=5, padx=(10, 0))
        
        ttk.Label(main_frame, text="الاسم الكامل:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.full_name_var, width=30).grid(row=1, column=1, pady=5, padx=(10, 0))
        
        ttk.Label(main_frame, text="البريد الإلكتروني:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.email_var, width=30).grid(row=2, column=1, pady=5, padx=(10, 0))
        
        ttk.Label(main_frame, text="رقم الهاتف:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.phone_var, width=30).grid(row=3, column=1, pady=5, padx=(10, 0))
        
        # كلمة المرور (فقط للإضافة)
        if not is_edit:
            ttk.Label(main_frame, text="كلمة المرور:").grid(row=4, column=0, sticky=tk.W, pady=5)
            ttk.Entry(main_frame, textvariable=self.password_var, show="*", width=30).grid(row=4, column=1, pady=5, padx=(10, 0))
        
        # الأزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="حفظ", command=self.save).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="إلغاء", command=self.cancel).pack(side=tk.LEFT)
    
    def save(self):
        """حفظ البيانات"""
        username = self.username_var.get().strip()
        if not username:
            messagebox.showerror("خطأ", "اسم المستخدم مطلوب")
            return
        
        password = self.password_var.get().strip()
        if not password and not hasattr(self, 'is_edit'):
            messagebox.showerror("خطأ", "كلمة المرور مطلوبة")
            return
        
        self.result = {
            'username': username,
            'full_name': self.full_name_var.get().strip(),
            'email': self.email_var.get().strip(),
            'phone': self.phone_var.get().strip(),
            'password': password
        }
        
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء"""
        self.dialog.destroy()

def main():
    """تشغيل الواجهة الرسومية"""
    root = tk.Tk()
    app = DatabaseGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
