#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل التطبيق مع نظام تسجيل الدخول
"""

import sys
import os

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔍 التحقق من المتطلبات...")

    try:
        import pygame
        print("✅ pygame متوفر")
    except ImportError:
        print("❌ pygame غير متوفر. يرجى تثبيته: pip install pygame")
        return False

    try:
        import sqlite3
        print("✅ sqlite3 متوفر")
    except ImportError:
        print("❌ sqlite3 غير متوفر")
        return False

    try:
        import tkinter
        print("✅ tkinter متوفر")
    except ImportError:
        print("❌ tkinter غير متوفر")
        return False

    return True

def setup_database():
    """إعداد قاعدة البيانات"""
    print("\n🗄️ إعداد قاعدة البيانات...")

    try:
        from database import UserDatabase
        db = UserDatabase()
        print("✅ تم إعداد قاعدة البيانات بنجاح")

        # التحقق من وجود المستخدم الافتراضي
        admin_user = db.verify_user("admin", "admin123")
        if admin_user:
            print("✅ المستخدم الافتراضي متوفر: admin / admin123")
        else:
            print("⚠️ المستخدم الافتراضي غير متوفر")

        return True

    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def run_application():
    """تشغيل التطبيق"""
    print("\n🚀 تشغيل التطبيق...")

    try:
        # استيراد الدالة الرئيسية
        from main import main

        print("📋 Opening login page...")
        print("Use the following credentials:")
        print("   Username: admin")
        print("   Password: admin123")
        print("Or you can create a new account")

        # تشغيل التطبيق
        main()

    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    print("🏗️ نظام التصميم المعماري مع تسجيل الدخول")
    print("=" * 50)

    # التحقق من المتطلبات
    if not check_requirements():
        print("\n❌ فشل في التحقق من المتطلبات")
        input("اضغط Enter للخروج...")
        return

    # إعداد قاعدة البيانات
    if not setup_database():
        print("\n❌ فشل في إعداد قاعدة البيانات")
        input("اضغط Enter للخروج...")
        return

    print("\n✅ جميع المتطلبات متوفرة")
    print("=" * 50)

    # تشغيل التطبيق
    run_application()

    print("\n🏁 انتهى التطبيق")

if __name__ == "__main__":
    main()
