#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔐 Setup Super Admin
إعداد المدير الرئيسي: MohammedBushih<PERSON>
"""

import sqlite3
from database import UserDatabase

def setup_super_admin():
    """إعداد المدير الرئيسي"""
    print("🔐 إعداد المدير الرئيسي...")
    
    try:
        # إنشاء قاعدة البيانات
        db = UserDatabase()
        
        # التحقق من وجود المدير الرئيسي
        if db.user_exists("MohammedBushiha"):
            print("✅ المدير الرئيسي موجود بالفعل")
            
            # التحقق من الصلاحيات
            conn = sqlite3.connect(db.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT user_role FROM users WHERE username = ?', ("<PERSON><PERSON>ushih<PERSON>",))
            role = cursor.fetchone()
            
            if role and role[0] != 'super_admin':
                # تحديث الصلاحيات
                cursor.execute('UPDATE users SET user_role = ? WHERE username = ?', 
                             ('super_admin', "MohammedBushiha"))
                conn.commit()
                print("✅ تم تحديث صلاحيات المدير الرئيسي")
            
            conn.close()
        else:
            # إنشاء المدير الرئيسي
            user_id = db.create_user(
                username="MohammedBushiha",
                password="Mfb112002*",
                email="<EMAIL>",
                full_name="Mohammed Bushiha - المدير الرئيسي",
                phone="123456789",
                user_role="super_admin"
            )
            
            if user_id:
                print("✅ تم إنشاء المدير الرئيسي بنجاح")
            else:
                print("❌ فشل في إنشاء المدير الرئيسي")
        
        # اختبار تسجيل الدخول
        print("\n🧪 اختبار تسجيل دخول المدير الرئيسي...")
        admin_user = db.verify_user("MohammedBushiha", "Mfb112002*")
        
        if admin_user:
            print("✅ تسجيل الدخول ناجح!")
            print(f"   الاسم: {admin_user['full_name']}")
            print(f"   اسم المستخدم: {admin_user['username']}")
            print(f"   الصلاحية: {admin_user['user_role']}")
            print(f"   البريد: {admin_user['email']}")
            
            if admin_user['user_role'] == 'super_admin':
                print("🔐 صلاحيات المدير الرئيسي مفعلة")
            else:
                print("⚠️ تحذير: صلاحيات المدير الرئيسي غير مفعلة")
        else:
            print("❌ فشل في تسجيل الدخول")
        
        # عرض إحصائيات قاعدة البيانات
        print("\n📊 إحصائيات قاعدة البيانات:")
        conn = sqlite3.connect(db.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM users')
        total_users = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM users WHERE user_role = "super_admin"')
        super_admins = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM users WHERE user_role = "admin"')
        admins = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM users WHERE user_role = "user"')
        regular_users = cursor.fetchone()[0]
        
        print(f"   👥 إجمالي المستخدمين: {total_users}")
        print(f"   🔐 المديرين الرئيسيين: {super_admins}")
        print(f"   👨‍💼 المديرين: {admins}")
        print(f"   👤 المستخدمين العاديين: {regular_users}")
        
        conn.close()
        
        print("\n🎉 تم إعداد النظام بنجاح!")
        print("=" * 50)
        print("معلومات تسجيل الدخول للمدير الرئيسي:")
        print("   اسم المستخدم: MohammedBushiha")
        print("   كلمة المرور: Mfb112002*")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ خطأ في إعداد المدير الرئيسي: {e}")
        import traceback
        traceback.print_exc()

def add_user_role_column():
    """إضافة عمود الصلاحيات للمستخدمين الموجودين"""
    try:
        conn = sqlite3.connect("users.db")
        cursor = conn.cursor()
        
        # التحقق من وجود عمود user_role
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'user_role' not in columns:
            print("🔧 إضافة عمود الصلاحيات...")
            cursor.execute('ALTER TABLE users ADD COLUMN user_role TEXT DEFAULT "user"')
            conn.commit()
            print("✅ تم إضافة عمود الصلاحيات")
        else:
            print("✅ عمود الصلاحيات موجود بالفعل")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في إضافة عمود الصلاحيات: {e}")

def main():
    """الدالة الرئيسية"""
    print("🛠️ إعداد نظام إدارة المستخدمين")
    print("=" * 50)
    
    # إضافة عمود الصلاحيات إذا لم يكن موجوداً
    add_user_role_column()
    
    # إعداد المدير الرئيسي
    setup_super_admin()

if __name__ == "__main__":
    main()
