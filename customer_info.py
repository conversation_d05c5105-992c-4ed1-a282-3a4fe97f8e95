import tkinter as tk
from tkinter import ttk, messagebox
import json
import os

class CustomerInfoApp:
    def __init__(self, root, on_start_main_app, user_data=None):
        self.root = root
        self.on_start_main_app = on_start_main_app
        self.user_data = user_data  # بيانات المستخدم المسجل

        # تحديث عنوان النافذة لتشمل اسم المستخدم
        if user_data:
            self.root.title(f"معلومات الزبون - {user_data['full_name']} ({user_data['username']})")
        else:
            self.root.title("معلومات الزبون")

        self.root.geometry("700x600")

        # تهيئة متغيرات البيانات
        self.customer_name = tk.StringVar()
        self.customer_phone = tk.StringVar()
        self.customer_address = tk.StringVar()
        self.project_name = tk.StringVar()
        self.project_date = tk.StringVar()

        self.create_widgets()
        self.load_last_customer()

    def create_widgets(self):
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات المستخدم المسجل
        if self.user_data:
            user_frame = ttk.LabelFrame(main_frame, text="معلومات المستخدم", padding="10")
            user_frame.pack(fill=tk.X, pady=(0, 20))

            user_info = f"المستخدم: {self.user_data['full_name']} | اسم المستخدم: {self.user_data['username']}"
            if self.user_data.get('email'):
                user_info += f" | البريد: {self.user_data['email']}"

            ttk.Label(user_frame, text=user_info, font=("Arial", 10)).pack()

        # عنوان
        title_label = ttk.Label(main_frame, text="بيانات الزبون", font=("Arial", 18, "bold"))
        title_label.pack(pady=10)

        # إطار البيانات
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # حقول الإدخال
        ttk.Label(form_frame, text="اسم الزبون:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.customer_name, width=40).grid(row=0, column=1, pady=5, padx=5)

        ttk.Label(form_frame, text="رقم الهاتف:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.customer_phone, width=40).grid(row=1, column=1, pady=5, padx=5)

        ttk.Label(form_frame, text="العنوان:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.customer_address, width=40).grid(row=2, column=1, pady=5, padx=5)

        ttk.Label(form_frame, text="اسم المشروع:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.project_name, width=40).grid(row=3, column=1, pady=5, padx=5)

        ttk.Label(form_frame, text="تاريخ المشروع:").grid(row=4, column=0, sticky=tk.W, pady=5)
        ttk.Entry(form_frame, textvariable=self.project_date, width=40).grid(row=4, column=1, pady=5, padx=5)

        # إطار الأزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=20)

        ttk.Button(button_frame, text="حفظ البيانات", command=self.save_customer_info).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="بدء التصميم", command=self.start_main_application).pack(side=tk.LEFT, padx=10)

    def save_customer_info(self):
        customer_data = {
            "name": self.customer_name.get(),
            "phone": self.customer_phone.get(),
            "address": self.customer_address.get(),
            "project_name": self.project_name.get(),
            "project_date": self.project_date.get()
        }

        # إضافة معلومات المستخدم إذا كانت متوفرة
        if self.user_data:
            customer_data["user_id"] = self.user_data["id"]
            customer_data["user_name"] = self.user_data["username"]
            customer_data["designer_name"] = self.user_data["full_name"]

        try:
            # حفظ في ملف JSON محلي
            with open("customer_data.json", "w", encoding="utf-8") as f:
                json.dump(customer_data, f, ensure_ascii=False, indent=4)

            # حفظ في قاعدة البيانات إذا كان المستخدم مسجل
            if self.user_data and hasattr(self, 'user_data'):
                try:
                    from database import UserDatabase
                    db = UserDatabase()
                    project_name = self.project_name.get() or "مشروع جديد"
                    db.save_user_project(
                        user_id=self.user_data["id"],
                        project_name=project_name,
                        project_data=json.dumps(customer_data, ensure_ascii=False)
                    )
                    print(f"✅ تم حفظ المشروع في قاعدة البيانات: {project_name}")
                except Exception as db_error:
                    print(f"⚠️ خطأ في حفظ المشروع في قاعدة البيانات: {db_error}")

            messagebox.showinfo("تم الحفظ", "تم حفظ بيانات الزبون بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {e}")

    def load_last_customer(self):
        try:
            if os.path.exists("customer_data.json"):
                with open("customer_data.json", "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.customer_name.set(data.get("name", ""))
                    self.customer_phone.set(data.get("phone", ""))
                    self.customer_address.set(data.get("address", ""))
                    self.project_name.set(data.get("project_name", ""))
                    self.project_date.set(data.get("project_date", ""))
        except Exception as e:
            print(f"خطأ في تحميل بيانات الزبون: {e}")

    def start_main_application(self):
        # حفظ البيانات قبل الانتقال
        self.save_customer_info()
        # إغلاق النافذة الحالية
        self.root.destroy()
        # استدعاء الدالة للانتقال إلى التطبيق الرئيسي
        self.on_start_main_app()

def start_customer_info():
    root = tk.Tk()
    # تعيين اتجاه النص من اليمين إلى اليسار
    root.tk.call('encoding', 'system', 'utf-8')
    style = ttk.Style()
    style.configure("TLabel", font=("Arial", 12))
    style.configure("TButton", font=("Arial", 12))
    style.configure("TEntry", font=("Arial", 12))

    return root, CustomerInfoApp