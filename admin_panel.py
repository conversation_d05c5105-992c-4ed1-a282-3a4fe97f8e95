#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔐 Admin Panel - لوحة إدارة المستخدمين
للمدير الرئيسي فقط: MohammedBushiha
"""

import pygame
import sys
import sqlite3
from database import UserDatabase

# إعدادات الشاشة
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
BACKGROUND_COLOR = (240, 248, 255)  # لون أزرق فاتح
PRIMARY_COLOR = (70, 130, 180)      # أزرق فولاذي
SECONDARY_COLOR = (255, 255, 255)   # أبيض
TEXT_COLOR = (25, 25, 112)          # أزرق داكن
ERROR_COLOR = (220, 20, 60)         # أحمر
SUCCESS_COLOR = (34, 139, 34)       # أخضر
BUTTON_COLOR = (100, 149, 237)      # أزرق كورن فلاور
DANGER_COLOR = (178, 34, 34)        # أحمر داكن

class AdminPanel:
    def __init__(self, admin_user):
        """تهيئة لوحة الإدارة"""
        pygame.init()
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption(f"Admin Panel - {admin_user['full_name']}")
        self.clock = pygame.time.Clock()

        # بيانات المدير
        self.admin_user = admin_user

        # التحقق من صلاحيات المدير الرئيسي
        if admin_user['username'] != 'MohammedBushiha' or admin_user['user_role'] != 'super_admin':
            print("❌ غير مصرح لك بالوصول لهذه الصفحة")
            pygame.quit()
            sys.exit()

        # تهيئة قاعدة البيانات
        self.db = UserDatabase()

        # تهيئة الخطوط
        self.title_font = pygame.font.Font(None, 36)
        self.header_font = pygame.font.Font(None, 28)
        self.text_font = pygame.font.Font(None, 24)
        self.button_font = pygame.font.Font(None, 22)

        # متغيرات الواجهة
        self.users_list = []
        self.selected_user = None
        self.scroll_offset = 0
        self.message = ""
        self.message_color = TEXT_COLOR
        self.message_timer = 0

        # أزرار
        self.buttons = {}
        self.create_buttons()

        # تحميل قائمة المستخدمين
        self.load_users()

        # حالة التطبيق
        self.running = True
        self.next_action = None

    def create_buttons(self):
        """إنشاء الأزرار"""
        button_width = 150
        button_height = 40
        start_x = 50
        start_y = 150
        spacing = 20

        buttons_data = [
            ("add_user", "Add User", start_x, start_y, PRIMARY_COLOR),
            ("edit_user", "Edit User", start_x, start_y + (button_height + spacing), BUTTON_COLOR),
            ("change_password", "Change Password", start_x, start_y + 2 * (button_height + spacing), BUTTON_COLOR),
            ("toggle_status", "Toggle Status", start_x, start_y + 3 * (button_height + spacing), BUTTON_COLOR),
            ("delete_user", "Delete User", start_x, start_y + 4 * (button_height + spacing), DANGER_COLOR),
            ("refresh", "Refresh", start_x, start_y + 5 * (button_height + spacing), SUCCESS_COLOR),
            ("logout", "Logout", start_x, start_y + 6 * (button_height + spacing), TEXT_COLOR)
        ]

        for button_id, text, x, y, color in buttons_data:
            self.buttons[button_id] = {
                'rect': pygame.Rect(x, y, button_width, button_height),
                'text': text,
                'color': color,
                'hover': False
            }

    def load_users(self):
        """تحميل قائمة المستخدمين"""
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, username, full_name, email, phone, user_role, created_at, last_login, is_active
                FROM users
                ORDER BY id
            ''')

            self.users_list = cursor.fetchall()
            conn.close()

        except Exception as e:
            self.show_message(f"Error loading users: {e}", ERROR_COLOR)

    def draw_background(self):
        """رسم الخلفية"""
        self.screen.fill(BACKGROUND_COLOR)

        # رسم العنوان الرئيسي
        title_text = f"Admin Panel - {self.admin_user['full_name']}"
        title_surface = self.title_font.render(title_text, True, PRIMARY_COLOR)
        title_rect = title_surface.get_rect(center=(SCREEN_WIDTH // 2, 30))
        self.screen.blit(title_surface, title_rect)

        # رسم خط فاصل
        pygame.draw.line(self.screen, PRIMARY_COLOR, (50, 60), (SCREEN_WIDTH - 50, 60), 2)

        # معلومات المدير
        admin_info = f"Super Admin: {self.admin_user['username']} | Total Users: {len(self.users_list)}"
        info_surface = self.text_font.render(admin_info, True, TEXT_COLOR)
        self.screen.blit(info_surface, (50, 80))

    def draw_buttons(self):
        """رسم الأزرار"""
        mouse_pos = pygame.mouse.get_pos()

        for button_id, button in self.buttons.items():
            # تحديث حالة التمرير
            button['hover'] = button['rect'].collidepoint(mouse_pos)

            # تحديد لون الزر
            if button['hover']:
                color = tuple(min(255, c + 30) for c in button['color'])
            else:
                color = button['color']

            # رسم الزر
            pygame.draw.rect(self.screen, color, button['rect'], border_radius=8)
            pygame.draw.rect(self.screen, TEXT_COLOR, button['rect'], 2, border_radius=8)

            # رسم النص
            text_surface = self.button_font.render(button['text'], True, SECONDARY_COLOR)
            text_rect = text_surface.get_rect(center=button['rect'].center)
            self.screen.blit(text_surface, text_rect)

    def draw_users_list(self):
        """رسم قائمة المستخدمين"""
        list_x = 250
        list_y = 150
        list_width = SCREEN_WIDTH - list_x - 50
        list_height = SCREEN_HEIGHT - list_y - 100

        # رسم إطار القائمة
        list_rect = pygame.Rect(list_x, list_y, list_width, list_height)
        pygame.draw.rect(self.screen, SECONDARY_COLOR, list_rect)
        pygame.draw.rect(self.screen, PRIMARY_COLOR, list_rect, 2)

        # عنوان القائمة
        header_text = "Users Management"
        header_surface = self.header_font.render(header_text, True, PRIMARY_COLOR)
        self.screen.blit(header_surface, (list_x + 10, list_y - 30))

        # رؤوس الأعمدة
        headers = ["ID", "Username", "Full Name", "Email", "Role", "Status", "Last Login"]
        header_y = list_y + 10
        col_widths = [40, 120, 150, 180, 80, 80, 120]
        col_x = list_x + 10

        for i, header in enumerate(headers):
            header_surface = self.text_font.render(header, True, TEXT_COLOR)
            self.screen.blit(header_surface, (col_x, header_y))
            col_x += col_widths[i]

        # خط فاصل تحت الرؤوس
        pygame.draw.line(self.screen, PRIMARY_COLOR,
                        (list_x + 10, header_y + 25),
                        (list_x + list_width - 10, header_y + 25), 1)

        # عرض المستخدمين
        start_y = header_y + 35
        row_height = 30
        visible_rows = (list_height - 50) // row_height

        for i, user in enumerate(self.users_list[self.scroll_offset:self.scroll_offset + visible_rows]):
            row_y = start_y + i * row_height

            # تحديد لون الصف
            if user == self.selected_user:
                row_color = (200, 220, 255)
                pygame.draw.rect(self.screen, row_color,
                               (list_x + 5, row_y - 5, list_width - 10, row_height))

            # بيانات المستخدم
            user_data = [
                str(user[0]),  # ID
                user[1],       # Username
                user[2][:15] + "..." if user[2] and len(user[2]) > 15 else user[2] or "N/A",  # Full Name
                user[3][:20] + "..." if user[3] and len(user[3]) > 20 else user[3] or "N/A",  # Email
                user[5] or "user",  # Role
                "Active" if user[8] else "Inactive",  # Status
                user[7][:10] if user[7] else "Never"  # Last Login
            ]

            col_x = list_x + 10
            for j, data in enumerate(user_data):
                # تحديد لون النص حسب الحالة
                if j == 5:  # عمود الحالة
                    text_color = SUCCESS_COLOR if user[8] else ERROR_COLOR
                elif j == 4:  # عمود الصلاحية
                    text_color = DANGER_COLOR if user[5] == 'super_admin' else PRIMARY_COLOR if user[5] == 'admin' else TEXT_COLOR
                else:
                    text_color = TEXT_COLOR

                text_surface = self.text_font.render(str(data), True, text_color)
                self.screen.blit(text_surface, (col_x, row_y))
                col_x += col_widths[j]

    def draw_message(self):
        """رسم الرسائل"""
        if self.message and self.message_timer > 0:
            message_surface = self.text_font.render(self.message, True, self.message_color)
            message_rect = message_surface.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 30))

            # رسم خلفية للرسالة
            bg_rect = message_rect.inflate(20, 10)
            pygame.draw.rect(self.screen, SECONDARY_COLOR, bg_rect, border_radius=5)
            pygame.draw.rect(self.screen, self.message_color, bg_rect, 2, border_radius=5)

            self.screen.blit(message_surface, message_rect)
            self.message_timer -= 1

    def show_message(self, message, color):
        """عرض رسالة"""
        self.message = message
        self.message_color = color
        self.message_timer = 180  # 3 ثوان بـ 60 FPS

    def handle_click(self, pos):
        """معالجة النقر"""
        # فحص النقر على الأزرار
        for button_id, button in self.buttons.items():
            if button['rect'].collidepoint(pos):
                self.handle_button_click(button_id)
                return

        # فحص النقر على قائمة المستخدمين
        list_x = 250
        list_y = 185  # بعد الرؤوس
        row_height = 30

        if pos[0] >= list_x and pos[1] >= list_y:
            row_index = (pos[1] - list_y) // row_height
            if 0 <= row_index < len(self.users_list[self.scroll_offset:]):
                self.selected_user = self.users_list[self.scroll_offset + row_index]

    def handle_button_click(self, button_id):
        """معالجة النقر على الأزرار"""
        if button_id == "add_user":
            self.add_user_dialog()
        elif button_id == "edit_user":
            if self.selected_user:
                self.edit_user_dialog()
            else:
                self.show_message("Please select a user first", ERROR_COLOR)
        elif button_id == "change_password":
            if self.selected_user:
                self.change_password_dialog()
            else:
                self.show_message("Please select a user first", ERROR_COLOR)
        elif button_id == "toggle_status":
            if self.selected_user:
                self.toggle_user_status()
            else:
                self.show_message("Please select a user first", ERROR_COLOR)
        elif button_id == "delete_user":
            if self.selected_user:
                self.delete_user_dialog()
            else:
                self.show_message("Please select a user first", ERROR_COLOR)
        elif button_id == "refresh":
            self.load_users()
            self.show_message("Users list refreshed", SUCCESS_COLOR)
        elif button_id == "logout":
            self.next_action = "logout"
            self.running = False

    def add_user_dialog(self):
        """حوار إضافة مستخدم جديد"""
        dialog = UserInputDialog(self.screen, "Add New User")
        if dialog.result:
            try:
                user_id = self.db.create_user(
                    username=dialog.result['username'],
                    password=dialog.result['password'],
                    email=dialog.result['email'] or None,
                    full_name=dialog.result['full_name'] or None,
                    phone=dialog.result['phone'] or None,
                    user_role=dialog.result['role']
                )

                if user_id:
                    self.show_message(f"User '{dialog.result['username']}' created successfully", SUCCESS_COLOR)
                    self.load_users()
                else:
                    self.show_message("Failed to create user. Username already exists", ERROR_COLOR)

            except Exception as e:
                self.show_message(f"Error creating user: {e}", ERROR_COLOR)

    def edit_user_dialog(self):
        """حوار تعديل المستخدم"""
        if not self.selected_user:
            return

        # منع تعديل المدير الرئيسي
        if self.selected_user[1] == 'MohammedBushiha':
            self.show_message("Cannot edit super admin account", ERROR_COLOR)
            return

        initial_data = {
            'username': self.selected_user[1],
            'full_name': self.selected_user[2] or '',
            'email': self.selected_user[3] or '',
            'phone': self.selected_user[4] or '',
            'role': self.selected_user[5] or 'user'
        }

        dialog = UserInputDialog(self.screen, "Edit User", initial_data, edit_mode=True)
        if dialog.result:
            try:
                conn = sqlite3.connect(self.db.db_path)
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE users
                    SET username = ?, full_name = ?, email = ?, phone = ?, user_role = ?
                    WHERE id = ?
                ''', (
                    dialog.result['username'],
                    dialog.result['full_name'] or None,
                    dialog.result['email'] or None,
                    dialog.result['phone'] or None,
                    dialog.result['role'],
                    self.selected_user[0]
                ))

                conn.commit()
                conn.close()

                self.show_message(f"User '{dialog.result['username']}' updated successfully", SUCCESS_COLOR)
                self.load_users()
                self.selected_user = None

            except sqlite3.IntegrityError:
                self.show_message("Username already exists", ERROR_COLOR)
            except Exception as e:
                self.show_message(f"Error updating user: {e}", ERROR_COLOR)

    def change_password_dialog(self):
        """حوار تغيير كلمة المرور"""
        if not self.selected_user:
            return

        # منع تغيير كلمة مرور المدير الرئيسي
        if self.selected_user[1] == 'MohammedBushiha':
            self.show_message("Cannot change super admin password", ERROR_COLOR)
            return

        dialog = PasswordDialog(self.screen, f"Change Password for: {self.selected_user[1]}")
        if dialog.result:
            try:
                conn = sqlite3.connect(self.db.db_path)
                cursor = conn.cursor()

                password_hash = self.db.hash_password(dialog.result)
                cursor.execute('UPDATE users SET password_hash = ? WHERE id = ?',
                             (password_hash, self.selected_user[0]))

                conn.commit()
                conn.close()

                self.show_message(f"Password changed for '{self.selected_user[1]}'", SUCCESS_COLOR)

            except Exception as e:
                self.show_message(f"Error changing password: {e}", ERROR_COLOR)

    def toggle_user_status(self):
        """تفعيل/إلغاء تفعيل المستخدم"""
        if not self.selected_user:
            return

        # منع تعطيل المدير الرئيسي
        if self.selected_user[1] == 'MohammedBushiha':
            self.show_message("Cannot disable super admin account", ERROR_COLOR)
            return

        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()

            new_status = not self.selected_user[8]  # عكس الحالة الحالية
            cursor.execute('UPDATE users SET is_active = ? WHERE id = ?',
                         (new_status, self.selected_user[0]))

            conn.commit()
            conn.close()

            status_text = "activated" if new_status else "deactivated"
            self.show_message(f"User '{self.selected_user[1]}' {status_text}", SUCCESS_COLOR)
            self.load_users()
            self.selected_user = None

        except Exception as e:
            self.show_message(f"Error changing user status: {e}", ERROR_COLOR)

    def delete_user_dialog(self):
        """حوار حذف المستخدم"""
        if not self.selected_user:
            return

        # منع حذف المدير الرئيسي
        if self.selected_user[1] == 'MohammedBushiha':
            self.show_message("Cannot delete super admin account", ERROR_COLOR)
            return

        dialog = ConfirmDialog(self.screen,
                             f"Delete User: {self.selected_user[1]}",
                             "Are you sure you want to delete this user?\nThis action cannot be undone.")

        if dialog.result:
            try:
                conn = sqlite3.connect(self.db.db_path)
                cursor = conn.cursor()

                # حذف مشاريع المستخدم
                cursor.execute('DELETE FROM user_projects WHERE user_id = ?', (self.selected_user[0],))

                # حذف المستخدم
                cursor.execute('DELETE FROM users WHERE id = ?', (self.selected_user[0],))

                conn.commit()
                conn.close()

                self.show_message(f"User '{self.selected_user[1]}' deleted successfully", SUCCESS_COLOR)
                self.load_users()
                self.selected_user = None

            except Exception as e:
                self.show_message(f"Error deleting user: {e}", ERROR_COLOR)

    def handle_scroll(self, direction):
        """معالجة التمرير"""
        if direction > 0 and self.scroll_offset > 0:
            self.scroll_offset -= 1
        elif direction < 0 and self.scroll_offset < len(self.users_list) - 10:
            self.scroll_offset += 1

    def run(self):
        """تشغيل لوحة الإدارة"""
        while self.running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False

                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # النقر بالزر الأيسر
                        self.handle_click(event.pos)
                    elif event.button == 4:  # عجلة الماوس للأعلى
                        self.handle_scroll(1)
                    elif event.button == 5:  # عجلة الماوس للأسفل
                        self.handle_scroll(-1)

                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        self.running = False
                    elif event.key == pygame.K_F5:
                        self.load_users()
                        self.show_message("Users list refreshed", SUCCESS_COLOR)

            # رسم الشاشة
            self.draw_background()
            self.draw_buttons()
            self.draw_users_list()
            self.draw_message()

            pygame.display.flip()
            self.clock.tick(60)

        pygame.quit()
        return self.next_action or "quit"

class UserInputDialog:
    def __init__(self, parent_screen, title, initial_data=None, edit_mode=False):
        """حوار إدخال بيانات المستخدم"""
        self.parent_screen = parent_screen
        self.title = title
        self.edit_mode = edit_mode
        self.result = None

        # إعداد النافذة
        self.width = 500
        self.height = 400
        self.x = (SCREEN_WIDTH - self.width) // 2
        self.y = (SCREEN_HEIGHT - self.height) // 2

        # الخطوط
        self.title_font = pygame.font.Font(None, 28)
        self.label_font = pygame.font.Font(None, 24)
        self.input_font = pygame.font.Font(None, 22)

        # الحقول
        self.fields = {
            'username': initial_data.get('username', '') if initial_data else '',
            'password': '' if not edit_mode else 'unchanged',
            'full_name': initial_data.get('full_name', '') if initial_data else '',
            'email': initial_data.get('email', '') if initial_data else '',
            'phone': initial_data.get('phone', '') if initial_data else '',
            'role': initial_data.get('role', 'user') if initial_data else 'user'
        }

        self.active_field = 'username'
        self.show_dialog()

    def show_dialog(self):
        """عرض الحوار"""
        clock = pygame.time.Clock()
        running = True

        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False

                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif event.key == pygame.K_RETURN:
                        if self.validate_input():
                            self.result = self.fields.copy()
                            running = False
                    elif event.key == pygame.K_TAB:
                        self.next_field()
                    elif event.key == pygame.K_BACKSPACE:
                        if self.fields[self.active_field]:
                            self.fields[self.active_field] = self.fields[self.active_field][:-1]
                    else:
                        if event.unicode.isprintable():
                            self.fields[self.active_field] += event.unicode

                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:
                        self.handle_click(event.pos)

            self.draw()
            pygame.display.flip()
            clock.tick(60)

    def next_field(self):
        """الانتقال للحقل التالي"""
        fields_order = ['username', 'password', 'full_name', 'email', 'phone', 'role']
        if self.edit_mode and 'password' in fields_order:
            fields_order.remove('password')

        current_index = fields_order.index(self.active_field)
        next_index = (current_index + 1) % len(fields_order)
        self.active_field = fields_order[next_index]

    def handle_click(self, pos):
        """معالجة النقر"""
        # فحص النقر على الحقول
        field_y = self.y + 80
        for field_name in self.fields.keys():
            if self.edit_mode and field_name == 'password':
                continue

            field_rect = pygame.Rect(self.x + 120, field_y, 300, 30)
            if field_rect.collidepoint(pos):
                self.active_field = field_name
                break
            field_y += 45

        # فحص النقر على الأزرار
        save_button = pygame.Rect(self.x + 100, self.y + self.height - 60, 100, 40)
        cancel_button = pygame.Rect(self.x + 220, self.y + self.height - 60, 100, 40)

        if save_button.collidepoint(pos):
            if self.validate_input():
                self.result = self.fields.copy()
                return False
        elif cancel_button.collidepoint(pos):
            return False

        return True

    def validate_input(self):
        """التحقق من صحة البيانات"""
        if not self.fields['username'].strip():
            return False
        if not self.edit_mode and not self.fields['password'].strip():
            return False
        return True

    def draw(self):
        """رسم الحوار"""
        # رسم الخلفية المعتمة
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill((0, 0, 0))
        self.parent_screen.blit(overlay, (0, 0))

        # رسم نافذة الحوار
        dialog_rect = pygame.Rect(self.x, self.y, self.width, self.height)
        pygame.draw.rect(self.parent_screen, SECONDARY_COLOR, dialog_rect, border_radius=10)
        pygame.draw.rect(self.parent_screen, PRIMARY_COLOR, dialog_rect, 3, border_radius=10)

        # العنوان
        title_surface = self.title_font.render(self.title, True, PRIMARY_COLOR)
        title_rect = title_surface.get_rect(center=(self.x + self.width // 2, self.y + 30))
        self.parent_screen.blit(title_surface, title_rect)

        # الحقول
        field_y = self.y + 80
        field_labels = {
            'username': 'Username:',
            'password': 'Password:',
            'full_name': 'Full Name:',
            'email': 'Email:',
            'phone': 'Phone:',
            'role': 'Role:'
        }

        for field_name, label in field_labels.items():
            if self.edit_mode and field_name == 'password':
                continue

            # التسمية
            label_surface = self.label_font.render(label, True, TEXT_COLOR)
            self.parent_screen.blit(label_surface, (self.x + 20, field_y + 5))

            # حقل الإدخال
            field_rect = pygame.Rect(self.x + 120, field_y, 300, 30)
            border_color = PRIMARY_COLOR if self.active_field == field_name else TEXT_COLOR
            pygame.draw.rect(self.parent_screen, SECONDARY_COLOR, field_rect)
            pygame.draw.rect(self.parent_screen, border_color, field_rect, 2)

            # النص
            display_text = self.fields[field_name]
            if field_name == 'password' and display_text:
                display_text = '*' * len(display_text)

            text_surface = self.input_font.render(display_text, True, TEXT_COLOR)
            self.parent_screen.blit(text_surface, (field_rect.x + 5, field_rect.y + 5))

            field_y += 45

        # الأزرار
        save_button = pygame.Rect(self.x + 100, self.y + self.height - 60, 100, 40)
        cancel_button = pygame.Rect(self.x + 220, self.y + self.height - 60, 100, 40)

        pygame.draw.rect(self.parent_screen, SUCCESS_COLOR, save_button, border_radius=5)
        pygame.draw.rect(self.parent_screen, ERROR_COLOR, cancel_button, border_radius=5)

        save_text = self.label_font.render("Save", True, SECONDARY_COLOR)
        cancel_text = self.label_font.render("Cancel", True, SECONDARY_COLOR)

        save_rect = save_text.get_rect(center=save_button.center)
        cancel_rect = cancel_text.get_rect(center=cancel_button.center)

        self.parent_screen.blit(save_text, save_rect)
        self.parent_screen.blit(cancel_text, cancel_rect)

class PasswordDialog:
    def __init__(self, parent_screen, title):
        """حوار تغيير كلمة المرور"""
        self.parent_screen = parent_screen
        self.title = title
        self.result = None

        # إعداد النافذة
        self.width = 400
        self.height = 200
        self.x = (SCREEN_WIDTH - self.width) // 2
        self.y = (SCREEN_HEIGHT - self.height) // 2

        # الخطوط
        self.title_font = pygame.font.Font(None, 28)
        self.label_font = pygame.font.Font(None, 24)
        self.input_font = pygame.font.Font(None, 22)

        # الحقول
        self.password = ''
        self.confirm_password = ''
        self.active_field = 'password'

        self.show_dialog()

    def show_dialog(self):
        """عرض الحوار"""
        clock = pygame.time.Clock()
        running = True

        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False

                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif event.key == pygame.K_RETURN:
                        if self.validate_input():
                            self.result = self.password
                            running = False
                    elif event.key == pygame.K_TAB:
                        self.active_field = 'confirm_password' if self.active_field == 'password' else 'password'
                    elif event.key == pygame.K_BACKSPACE:
                        if self.active_field == 'password':
                            self.password = self.password[:-1]
                        else:
                            self.confirm_password = self.confirm_password[:-1]
                    else:
                        if event.unicode.isprintable():
                            if self.active_field == 'password':
                                self.password += event.unicode
                            else:
                                self.confirm_password += event.unicode

                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:
                        running = self.handle_click(event.pos)

            self.draw()
            pygame.display.flip()
            clock.tick(60)

    def handle_click(self, pos):
        """معالجة النقر"""
        # فحص النقر على الحقول
        password_rect = pygame.Rect(self.x + 120, self.y + 80, 200, 30)
        confirm_rect = pygame.Rect(self.x + 120, self.y + 125, 200, 30)

        if password_rect.collidepoint(pos):
            self.active_field = 'password'
        elif confirm_rect.collidepoint(pos):
            self.active_field = 'confirm_password'

        # فحص النقر على الأزرار
        save_button = pygame.Rect(self.x + 80, self.y + self.height - 50, 80, 30)
        cancel_button = pygame.Rect(self.x + 180, self.y + self.height - 50, 80, 30)

        if save_button.collidepoint(pos):
            if self.validate_input():
                self.result = self.password
                return False
        elif cancel_button.collidepoint(pos):
            return False

        return True

    def validate_input(self):
        """التحقق من صحة البيانات"""
        return len(self.password) >= 6 and self.password == self.confirm_password

    def draw(self):
        """رسم الحوار"""
        # رسم الخلفية المعتمة
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill((0, 0, 0))
        self.parent_screen.blit(overlay, (0, 0))

        # رسم نافذة الحوار
        dialog_rect = pygame.Rect(self.x, self.y, self.width, self.height)
        pygame.draw.rect(self.parent_screen, SECONDARY_COLOR, dialog_rect, border_radius=10)
        pygame.draw.rect(self.parent_screen, PRIMARY_COLOR, dialog_rect, 3, border_radius=10)

        # العنوان
        title_surface = self.title_font.render(self.title, True, PRIMARY_COLOR)
        title_rect = title_surface.get_rect(center=(self.x + self.width // 2, self.y + 30))
        self.parent_screen.blit(title_surface, title_rect)

        # حقل كلمة المرور
        label1 = self.label_font.render("New Password:", True, TEXT_COLOR)
        self.parent_screen.blit(label1, (self.x + 20, self.y + 85))

        password_rect = pygame.Rect(self.x + 120, self.y + 80, 200, 30)
        border_color = PRIMARY_COLOR if self.active_field == 'password' else TEXT_COLOR
        pygame.draw.rect(self.parent_screen, SECONDARY_COLOR, password_rect)
        pygame.draw.rect(self.parent_screen, border_color, password_rect, 2)

        password_display = '*' * len(self.password)
        password_surface = self.input_font.render(password_display, True, TEXT_COLOR)
        self.parent_screen.blit(password_surface, (password_rect.x + 5, password_rect.y + 5))

        # حقل تأكيد كلمة المرور
        label2 = self.label_font.render("Confirm:", True, TEXT_COLOR)
        self.parent_screen.blit(label2, (self.x + 20, self.y + 130))

        confirm_rect = pygame.Rect(self.x + 120, self.y + 125, 200, 30)
        border_color = PRIMARY_COLOR if self.active_field == 'confirm_password' else TEXT_COLOR
        pygame.draw.rect(self.parent_screen, SECONDARY_COLOR, confirm_rect)
        pygame.draw.rect(self.parent_screen, border_color, confirm_rect, 2)

        confirm_display = '*' * len(self.confirm_password)
        confirm_surface = self.input_font.render(confirm_display, True, TEXT_COLOR)
        self.parent_screen.blit(confirm_surface, (confirm_rect.x + 5, confirm_rect.y + 5))

        # الأزرار
        save_button = pygame.Rect(self.x + 80, self.y + self.height - 50, 80, 30)
        cancel_button = pygame.Rect(self.x + 180, self.y + self.height - 50, 80, 30)

        pygame.draw.rect(self.parent_screen, SUCCESS_COLOR, save_button, border_radius=5)
        pygame.draw.rect(self.parent_screen, ERROR_COLOR, cancel_button, border_radius=5)

        save_text = self.label_font.render("Save", True, SECONDARY_COLOR)
        cancel_text = self.label_font.render("Cancel", True, SECONDARY_COLOR)

        save_rect = save_text.get_rect(center=save_button.center)
        cancel_rect = cancel_text.get_rect(center=cancel_button.center)

        self.parent_screen.blit(save_text, save_rect)
        self.parent_screen.blit(cancel_text, cancel_rect)

class ConfirmDialog:
    def __init__(self, parent_screen, title, message):
        """حوار التأكيد"""
        self.parent_screen = parent_screen
        self.title = title
        self.message = message
        self.result = False

        # إعداد النافذة
        self.width = 400
        self.height = 200
        self.x = (SCREEN_WIDTH - self.width) // 2
        self.y = (SCREEN_HEIGHT - self.height) // 2

        # الخطوط
        self.title_font = pygame.font.Font(None, 28)
        self.text_font = pygame.font.Font(None, 22)

        self.show_dialog()

    def show_dialog(self):
        """عرض الحوار"""
        clock = pygame.time.Clock()
        running = True

        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False

                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif event.key == pygame.K_RETURN:
                        self.result = True
                        running = False

                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:
                        running = self.handle_click(event.pos)

            self.draw()
            pygame.display.flip()
            clock.tick(60)

    def handle_click(self, pos):
        """معالجة النقر"""
        yes_button = pygame.Rect(self.x + 80, self.y + self.height - 50, 80, 30)
        no_button = pygame.Rect(self.x + 180, self.y + self.height - 50, 80, 30)

        if yes_button.collidepoint(pos):
            self.result = True
            return False
        elif no_button.collidepoint(pos):
            self.result = False
            return False

        return True

    def draw(self):
        """رسم الحوار"""
        # رسم الخلفية المعتمة
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill((0, 0, 0))
        self.parent_screen.blit(overlay, (0, 0))

        # رسم نافذة الحوار
        dialog_rect = pygame.Rect(self.x, self.y, self.width, self.height)
        pygame.draw.rect(self.parent_screen, SECONDARY_COLOR, dialog_rect, border_radius=10)
        pygame.draw.rect(self.parent_screen, PRIMARY_COLOR, dialog_rect, 3, border_radius=10)

        # العنوان
        title_surface = self.title_font.render(self.title, True, PRIMARY_COLOR)
        title_rect = title_surface.get_rect(center=(self.x + self.width // 2, self.y + 30))
        self.parent_screen.blit(title_surface, title_rect)

        # الرسالة
        lines = self.message.split('\n')
        y_offset = self.y + 70
        for line in lines:
            text_surface = self.text_font.render(line, True, TEXT_COLOR)
            text_rect = text_surface.get_rect(center=(self.x + self.width // 2, y_offset))
            self.parent_screen.blit(text_surface, text_rect)
            y_offset += 25

        # الأزرار
        yes_button = pygame.Rect(self.x + 80, self.y + self.height - 50, 80, 30)
        no_button = pygame.Rect(self.x + 180, self.y + self.height - 50, 80, 30)

        pygame.draw.rect(self.parent_screen, SUCCESS_COLOR, yes_button, border_radius=5)
        pygame.draw.rect(self.parent_screen, ERROR_COLOR, no_button, border_radius=5)

        yes_text = self.text_font.render("Yes", True, SECONDARY_COLOR)
        no_text = self.text_font.render("No", True, SECONDARY_COLOR)

        yes_rect = yes_text.get_rect(center=yes_button.center)
        no_rect = no_text.get_rect(center=no_button.center)

        self.parent_screen.blit(yes_text, yes_rect)
        self.parent_screen.blit(no_text, no_rect)

def show_admin_panel(admin_user):
    """عرض لوحة الإدارة"""
    panel = AdminPanel(admin_user)
    return panel.run()

if __name__ == "__main__":
    # اختبار لوحة الإدارة
    from database import UserDatabase

    db = UserDatabase()
    admin_user = db.verify_user("MohammedBushiha", "Mfb112002*")

    if admin_user:
        show_admin_panel(admin_user)
    else:
        print("❌ فشل في تسجيل الدخول كمدير رئيسي")
