#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط لصفحة تسجيل الدخول
"""

import pygame
import sys

def test_pygame():
    """اختبار pygame"""
    try:
        pygame.init()
        screen = pygame.display.set_mode((400, 300))
        pygame.display.set_caption("اختبار pygame")
        
        clock = pygame.time.Clock()
        running = True
        
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
            
            screen.fill((100, 150, 200))
            pygame.display.flip()
            clock.tick(60)
        
        pygame.quit()
        print("✅ pygame يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في pygame: {e}")
        return False

def main():
    print("🔍 اختبار pygame...")
    
    if test_pygame():
        print("✅ جميع الاختبارات نجحت")
        
        # الآن اختبار صفحة تسجيل الدخول
        print("\n🖥️ اختبار صفحة تسجيل الدخول...")
        try:
            from login_page import show_login_page
            
            print("سيتم فتح صفحة تسجيل الدخول...")
            print("استخدم: admin / admin123")
            
            user_data = show_login_page()
            
            if user_data:
                print(f"✅ تم تسجيل الدخول: {user_data['username']}")
            else:
                print("❌ تم إلغاء تسجيل الدخول")
                
        except Exception as e:
            print(f"❌ خطأ في صفحة تسجيل الدخول: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ فشل في اختبار pygame")

if __name__ == "__main__":
    main()
