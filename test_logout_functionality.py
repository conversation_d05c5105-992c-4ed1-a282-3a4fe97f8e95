#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 اختبار وظيفة تسجيل الخروج (Logout)
Test Logout Functionality
"""

from database import UserDatabase

def test_logout_buttons_exist():
    """اختبار وجود أزرار logout في جميع الواجهات"""
    print("🔍 اختبار وجود أزرار logout...")
    
    try:
        # اختبار Dashboard
        from dashboard import Dashboard
        db = UserDatabase()
        user_data = db.verify_user("admin", "admin123")
        
        if user_data:
            dashboard = Dashboard.__new__(Dashboard)
            dashboard.user_data = user_data
            dashboard.buttons = {}
            dashboard.create_buttons()
            
            if "logout" in dashboard.buttons:
                print("✅ Dashboard: زر logout موجود")
            else:
                print("❌ Dashboard: زر logout مفقود")
                return False
        
        # اختبار Projects Viewer
        from projects_viewer import ProjectsViewer
        
        viewer = ProjectsViewer.__new__(ProjectsViewer)
        viewer.user_data = user_data
        viewer.buttons = {}
        viewer.create_buttons()
        
        if "logout" in viewer.buttons:
            print("✅ Projects Viewer: زر logout موجود")
        else:
            print("❌ Projects Viewer: زر logout مفقود")
            return False
        
        # اختبار Admin Panel
        from admin_panel import AdminPanel
        admin_user = db.verify_user("MohammedBushiha", "Mfb112002*")
        
        if admin_user:
            admin_panel = AdminPanel.__new__(AdminPanel)
            admin_panel.admin_user = admin_user
            admin_panel.buttons = {}
            admin_panel.create_buttons()
            
            if "logout" in admin_panel.buttons:
                print("✅ Admin Panel: زر logout موجود")
            else:
                print("❌ Admin Panel: زر logout مفقود")
                return False
        
        # اختبار System Settings
        from system_settings import SystemSettings
        
        if admin_user:
            settings = SystemSettings.__new__(SystemSettings)
            settings.user_data = admin_user
            settings.buttons = {}
            settings.create_buttons()
            
            if "logout" in settings.buttons:
                print("✅ System Settings: زر logout موجود")
            else:
                print("❌ System Settings: زر logout مفقود")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أزرار logout: {e}")
        return False

def test_logout_actions():
    """اختبار إجراءات logout"""
    print("\n🔄 اختبار إجراءات logout...")
    
    try:
        from dashboard import Dashboard
        from projects_viewer import ProjectsViewer
        from admin_panel import AdminPanel
        from system_settings import SystemSettings
        
        db = UserDatabase()
        user_data = db.verify_user("admin", "admin123")
        admin_user = db.verify_user("MohammedBushiha", "Mfb112002*")
        
        # اختبار Dashboard logout action
        dashboard = Dashboard.__new__(Dashboard)
        dashboard.user_data = user_data
        dashboard.next_action = None
        dashboard.running = True
        dashboard.handle_button_click("logout")
        
        if dashboard.next_action == "logout":
            print("✅ Dashboard: إجراء logout يعمل بشكل صحيح")
        else:
            print("❌ Dashboard: إجراء logout لا يعمل")
            return False
        
        # اختبار Projects Viewer logout action
        viewer = ProjectsViewer.__new__(ProjectsViewer)
        viewer.user_data = user_data
        viewer.next_action = None
        viewer.running = True
        viewer.handle_button_click("logout")
        
        if viewer.next_action == "logout":
            print("✅ Projects Viewer: إجراء logout يعمل بشكل صحيح")
        else:
            print("❌ Projects Viewer: إجراء logout لا يعمل")
            return False
        
        # اختبار Admin Panel logout action
        if admin_user:
            admin_panel = AdminPanel.__new__(AdminPanel)
            admin_panel.admin_user = admin_user
            admin_panel.next_action = None
            admin_panel.running = True
            admin_panel.handle_button_click("logout")
            
            if admin_panel.next_action == "logout":
                print("✅ Admin Panel: إجراء logout يعمل بشكل صحيح")
            else:
                print("❌ Admin Panel: إجراء logout لا يعمل")
                return False
        
        # اختبار System Settings logout action
        if admin_user:
            settings = SystemSettings.__new__(SystemSettings)
            settings.user_data = admin_user
            settings.next_action = None
            settings.running = True
            settings.handle_button_click("logout")
            
            if settings.next_action == "logout":
                print("✅ System Settings: إجراء logout يعمل بشكل صحيح")
            else:
                print("❌ System Settings: إجراء logout لا يعمل")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إجراءات logout: {e}")
        return False

def test_main_logout_handling():
    """اختبار معالجة logout في main.py"""
    print("\n🔗 اختبار معالجة logout في main.py...")
    
    try:
        import main
        
        # التحقق من وجود الوظائف المطلوبة
        if hasattr(main, 'main'):
            print("✅ main.py: دالة main موجودة")
        else:
            print("❌ main.py: دالة main مفقودة")
            return False
        
        # التحقق من وجود معالجة logout في الكود
        import inspect
        main_source = inspect.getsource(main.main)
        
        if 'logout' in main_source and 'العودة لصفحة تسجيل الدخول' in main_source:
            print("✅ main.py: معالجة logout موجودة")
        else:
            print("❌ main.py: معالجة logout مفقودة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار main.py: {e}")
        return False

def test_recursive_main_call():
    """اختبار استدعاء main() المتكرر"""
    print("\n🔄 اختبار استدعاء main() المتكرر...")
    
    try:
        # محاكاة استدعاء main() متكرر
        call_count = 0
        max_calls = 3
        
        def mock_main():
            nonlocal call_count
            call_count += 1
            if call_count < max_calls:
                print(f"   📞 استدعاء main() رقم {call_count}")
                return True
            else:
                print(f"   🛑 توقف عند الاستدعاء رقم {call_count}")
                return False
        
        # محاكاة عدة استدعاءات
        while mock_main():
            pass
        
        if call_count == max_calls:
            print("✅ استدعاء main() المتكرر يعمل بشكل صحيح")
            return True
        else:
            print("❌ مشكلة في استدعاء main() المتكرر")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار استدعاء main(): {e}")
        return False

def test_user_permissions_logout():
    """اختبار صلاحيات logout للمستخدمين"""
    print("\n👥 اختبار صلاحيات logout للمستخدمين...")
    
    try:
        db = UserDatabase()
        
        # اختبار المستخدم العادي
        user_data = db.verify_user("admin", "admin123")
        if user_data:
            user_role = user_data.get('user_role', 'user')
            print(f"✅ المستخدم العادي ({user_role}): يمكنه logout")
        else:
            print("❌ فشل في تسجيل دخول المستخدم العادي")
            return False
        
        # اختبار المدير الرئيسي
        admin_user = db.verify_user("MohammedBushiha", "Mfb112002*")
        if admin_user:
            admin_role = admin_user.get('user_role', 'user')
            print(f"✅ المدير الرئيسي ({admin_role}): يمكنه logout")
        else:
            print("❌ فشل في تسجيل دخول المدير الرئيسي")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار صلاحيات logout: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار وظيفة تسجيل الخروج (Logout)")
    print("=" * 60)
    
    tests = [
        ("اختبار وجود أزرار logout", test_logout_buttons_exist),
        ("اختبار إجراءات logout", test_logout_actions),
        ("اختبار معالجة logout في main.py", test_main_logout_handling),
        ("اختبار استدعاء main() المتكرر", test_recursive_main_call),
        ("اختبار صلاحيات logout للمستخدمين", test_user_permissions_logout)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات logout نجحت!")
        print("\n✅ وظيفة logout تعمل بشكل صحيح:")
        print("  📊 Dashboard → logout → صفحة تسجيل الدخول")
        print("  📁 Projects Viewer → logout → صفحة تسجيل الدخول")
        print("  🔐 Admin Panel → logout → صفحة تسجيل الدخول")
        print("  ⚙️ System Settings → logout → صفحة تسجيل الدخول")
        
        print("\n🔄 آلية العمل:")
        print("  1. المستخدم ينقر على زر Logout")
        print("  2. يتم تعيين next_action = 'logout'")
        print("  3. يتم إنهاء الحلقة الحالية")
        print("  4. يتم استدعاء main() للعودة لصفحة تسجيل الدخول")
        
    else:
        print("⚠️ بعض اختبارات logout فشلت. يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
