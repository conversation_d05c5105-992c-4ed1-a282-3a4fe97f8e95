import sqlite3
import hashlib
import os
from datetime import datetime

class UserDatabase:
    def __init__(self, db_path="users.db"):
        """تهيئة قاعدة البيانات"""
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # جدول المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    email TEXT,
                    full_name TEXT,
                    phone TEXT,
                    user_role TEXT DEFAULT 'user',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')

            # جدول جلسات المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    session_token TEXT UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            # جدول مشاريع المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_projects (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    project_name TEXT NOT NULL,
                    project_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات بنجاح")

            # إنشاء مستخدم افتراضي للاختبار
            self.create_default_user()

        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    def create_default_user(self):
        """إنشاء المستخدم الرئيسي والمستخدم الافتراضي"""
        try:
            # إنشاء المستخدم الرئيسي
            if not self.user_exists("MohammedBushiha"):
                main_admin_id = self.create_user(
                    username="MohammedBushiha",
                    password="Mfb112002*",
                    email="<EMAIL>",
                    full_name="Mohammed Bushiha - المدير الرئيسي",
                    phone="123456789",
                    user_role="super_admin"
                )
                print("✅ تم إنشاء المستخدم الرئيسي: MohammedBushiha")

            # إنشاء مستخدم افتراضي للاختبار
            if not self.user_exists("admin"):
                self.create_user(
                    username="admin",
                    password="admin123",
                    email="<EMAIL>",
                    full_name="مدير النظام",
                    phone="123456789",
                    user_role="admin"
                )
                print("✅ تم إنشاء المستخدم الافتراضي: admin / admin123")
        except Exception as e:
            print(f"⚠️ خطأ في إنشاء المستخدمين الافتراضيين: {e}")

    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()

    def create_user(self, username, password, email=None, full_name=None, phone=None, user_role="user"):
        """إنشاء مستخدم جديد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            password_hash = self.hash_password(password)

            cursor.execute('''
                INSERT INTO users (username, password_hash, email, full_name, phone, user_role)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (username, password_hash, email, full_name, phone, user_role))

            conn.commit()
            user_id = cursor.lastrowid
            conn.close()

            print(f"✅ تم إنشاء المستخدم بنجاح: {username}")
            return user_id

        except sqlite3.IntegrityError:
            print(f"❌ اسم المستخدم '{username}' موجود بالفعل")
            return None
        except Exception as e:
            print(f"❌ خطأ في إنشاء المستخدم: {e}")
            return None

    def verify_user(self, username, password):
        """التحقق من بيانات المستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            password_hash = self.hash_password(password)

            cursor.execute('''
                SELECT id, username, full_name, email, phone, user_role, is_active
                FROM users
                WHERE username = ? AND password_hash = ? AND is_active = 1
            ''', (username, password_hash))

            user = cursor.fetchone()

            if user:
                # تحديث وقت آخر تسجيل دخول
                cursor.execute('''
                    UPDATE users SET last_login = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (user[0],))
                conn.commit()

                # إرجاع بيانات المستخدم
                user_data = {
                    'id': user[0],
                    'username': user[1],
                    'full_name': user[2],
                    'email': user[3],
                    'phone': user[4],
                    'user_role': user[5],
                    'is_active': user[6]
                }

                conn.close()
                print(f"✅ تم تسجيل الدخول بنجاح: {username}")
                return user_data
            else:
                conn.close()
                print(f"❌ بيانات تسجيل الدخول غير صحيحة: {username}")
                return None

        except Exception as e:
            print(f"❌ خطأ في التحقق من المستخدم: {e}")
            return None

    def user_exists(self, username):
        """التحقق من وجود المستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
            user = cursor.fetchone()

            conn.close()
            return user is not None

        except Exception as e:
            print(f"❌ خطأ في التحقق من وجود المستخدم: {e}")
            return False

    def get_user_projects(self, user_id):
        """الحصول على مشاريع المستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, project_name, created_at, updated_at
                FROM user_projects
                WHERE user_id = ?
                ORDER BY updated_at DESC
            ''', (user_id,))

            projects = cursor.fetchall()
            conn.close()

            return [
                {
                    'id': p[0],
                    'name': p[1],
                    'created_at': p[2],
                    'updated_at': p[3]
                }
                for p in projects
            ]

        except Exception as e:
            print(f"❌ خطأ في الحصول على المشاريع: {e}")
            return []

    def save_user_project(self, user_id, project_name, project_data):
        """حفظ مشروع المستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود مشروع بنفس الاسم
            cursor.execute('''
                SELECT id FROM user_projects
                WHERE user_id = ? AND project_name = ?
            ''', (user_id, project_name))

            existing = cursor.fetchone()

            if existing:
                # تحديث المشروع الموجود
                cursor.execute('''
                    UPDATE user_projects
                    SET project_data = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (project_data, existing[0]))
                print(f"✅ تم تحديث المشروع: {project_name}")
            else:
                # إنشاء مشروع جديد
                cursor.execute('''
                    INSERT INTO user_projects (user_id, project_name, project_data)
                    VALUES (?, ?, ?)
                ''', (user_id, project_name, project_data))
                print(f"✅ تم حفظ المشروع الجديد: {project_name}")

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"❌ خطأ في حفظ المشروع: {e}")
            return False
