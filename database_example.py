#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 مثال عملي على كيفية عمل قاعدة البيانات
Database Working Example
"""

from database import UserDatabase
import json
from datetime import datetime

def demonstrate_database_operations():
    """عرض توضيحي لعمليات قاعدة البيانات"""
    print("🗄️ مثال عملي على كيفية عمل قاعدة البيانات")
    print("=" * 60)
    
    # 1. إنشاء قاعدة البيانات
    print("\n1️⃣ إنشاء قاعدة البيانات:")
    print("-" * 30)
    db = UserDatabase()
    
    # 2. إنشاء مستخدم جديد
    print("\n2️⃣ إنشاء مستخدم جديد:")
    print("-" * 30)
    user_id = db.create_user(
        username="test_user",
        password="test123",
        email="<EMAIL>",
        full_name="مستخدم تجريبي",
        phone="123456789",
        user_role="user"
    )
    
    if user_id:
        print(f"✅ تم إنشاء المستخدم بـ ID: {user_id}")
    
    # 3. التحقق من تسجيل الدخول
    print("\n3️⃣ التحقق من تسجيل الدخول:")
    print("-" * 30)
    user_data = db.verify_user("test_user", "test123")
    
    if user_data:
        print(f"✅ تم تسجيل الدخول بنجاح:")
        print(f"   - الاسم: {user_data['full_name']}")
        print(f"   - النوع: {user_data['user_role']}")
        print(f"   - البريد: {user_data['email']}")
    
    # 4. إنشاء مشروع تجريبي
    print("\n4️⃣ إنشاء مشروع تجريبي:")
    print("-" * 30)
    
    # بيانات مشروع تجريبي
    project_data = {
        'walls': [
            {
                'start_m': [0.0, 0.0],
                'end_m': [5.0, 0.0],
                'length_m': 5.0,
                'height_m': 3.0
            },
            {
                'start_m': [5.0, 0.0],
                'end_m': [5.0, 4.0],
                'length_m': 4.0,
                'height_m': 3.0
            }
        ],
        'chairs': [
            {
                'pos_m': [2.5, 2.0],
                'size_m': 0.5
            }
        ],
        'doors': [
            {
                'pos_m': [2.5, 0.0],
                'width_m': 0.9,
                'height_m': 2.2,
                'bottom_height_m': 0.0,
                'left_distance_m': 1.6,
                'right_distance_m': 1.5
            }
        ],
        'windows': [],
        'measurements': [
            {
                'point1': [0.0, 0.0],
                'point2': [5.0, 0.0],
                'distance': 5.0
            }
        ],
        'saved_at': datetime.now().isoformat(),
        'auto_save': False
    }
    
    # حفظ المشروع
    project_name = f"TestProject_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    success = db.save_user_project(
        user_id=user_data['id'],
        project_name=project_name,
        project_data=json.dumps(project_data, ensure_ascii=False)
    )
    
    if success:
        print(f"✅ تم حفظ المشروع: {project_name}")
        print(f"   - الجدران: {len(project_data['walls'])}")
        print(f"   - الكراسي: {len(project_data['chairs'])}")
        print(f"   - الأبواب: {len(project_data['doors'])}")
        print(f"   - القياسات: {len(project_data['measurements'])}")
    
    # 5. استرداد قائمة المشاريع
    print("\n5️⃣ استرداد قائمة المشاريع:")
    print("-" * 30)
    projects = db.get_user_projects(user_data['id'])
    
    if projects:
        print(f"✅ تم العثور على {len(projects)} مشروع:")
        for project in projects:
            print(f"   - {project['name']} (ID: {project['id']})")
            print(f"     تاريخ الإنشاء: {project['created_at']}")
            print(f"     آخر تحديث: {project['updated_at']}")
    
    # 6. تحديث المشروع
    print("\n6️⃣ تحديث المشروع:")
    print("-" * 30)
    
    # إضافة نافذة للمشروع
    project_data['windows'].append({
        'pos_m': [5.0, 2.0],
        'width_m': 1.2,
        'height_m': 1.5,
        'bottom_height_m': 1.0,
        'left_distance_m': 1.4,
        'right_distance_m': 1.4
    })
    
    # تحديث المشروع
    success = db.save_user_project(
        user_id=user_data['id'],
        project_name=project_name,
        project_data=json.dumps(project_data, ensure_ascii=False)
    )
    
    if success:
        print(f"✅ تم تحديث المشروع: {project_name}")
        print(f"   - النوافذ الجديدة: {len(project_data['windows'])}")
    
    # 7. عرض إحصائيات قاعدة البيانات
    print("\n7️⃣ إحصائيات قاعدة البيانات:")
    print("-" * 30)
    
    import sqlite3
    conn = sqlite3.connect(db.db_path)
    cursor = conn.cursor()
    
    # عدد المستخدمين
    cursor.execute("SELECT COUNT(*) FROM users")
    users_count = cursor.fetchone()[0]
    print(f"📊 عدد المستخدمين: {users_count}")
    
    # عدد المشاريع
    cursor.execute("SELECT COUNT(*) FROM user_projects")
    projects_count = cursor.fetchone()[0]
    print(f"📁 عدد المشاريع: {projects_count}")
    
    # آخر تسجيل دخول
    cursor.execute("SELECT username, last_login FROM users WHERE last_login IS NOT NULL ORDER BY last_login DESC LIMIT 1")
    last_login = cursor.fetchone()
    if last_login:
        print(f"🕐 آخر تسجيل دخول: {last_login[0]} في {last_login[1]}")
    
    conn.close()
    
    print("\n" + "=" * 60)
    print("🎉 انتهى المثال التوضيحي لقاعدة البيانات!")

def show_database_structure():
    """عرض هيكل قاعدة البيانات"""
    print("\n🏗️ هيكل قاعدة البيانات:")
    print("=" * 60)
    
    db = UserDatabase()
    
    import sqlite3
    conn = sqlite3.connect(db.db_path)
    cursor = conn.cursor()
    
    # الحصول على قائمة الجداول
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    print(f"📋 الجداول الموجودة: {len(tables)}")
    
    for table in tables:
        table_name = table[0]
        print(f"\n📊 جدول: {table_name}")
        print("-" * 30)
        
        # الحصول على هيكل الجدول
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        for column in columns:
            col_id, col_name, col_type, not_null, default_val, primary_key = column
            pk_indicator = " (PK)" if primary_key else ""
            null_indicator = " NOT NULL" if not_null else ""
            default_indicator = f" DEFAULT {default_val}" if default_val else ""
            
            print(f"   {col_name}: {col_type}{pk_indicator}{null_indicator}{default_indicator}")
    
    conn.close()

def demonstrate_security_features():
    """عرض مميزات الأمان"""
    print("\n🔒 مميزات الأمان في قاعدة البيانات:")
    print("=" * 60)
    
    db = UserDatabase()
    
    # 1. تشفير كلمة المرور
    print("\n1️⃣ تشفير كلمة المرور:")
    print("-" * 30)
    password = "test123"
    hashed = db.hash_password(password)
    print(f"كلمة المرور الأصلية: {password}")
    print(f"كلمة المرور المشفرة: {hashed}")
    print(f"طول الـ Hash: {len(hashed)} حرف")
    
    # 2. التحقق من عدم تكرار اسم المستخدم
    print("\n2️⃣ منع تكرار أسماء المستخدمين:")
    print("-" * 30)
    
    # محاولة إنشاء مستخدم بنفس الاسم
    result1 = db.create_user("duplicate_test", "pass123", user_role="user")
    result2 = db.create_user("duplicate_test", "pass456", user_role="admin")
    
    print(f"المحاولة الأولى: {'نجحت' if result1 else 'فشلت'}")
    print(f"المحاولة الثانية: {'نجحت' if result2 else 'فشلت'}")
    
    # 3. فصل البيانات بين المستخدمين
    print("\n3️⃣ فصل البيانات بين المستخدمين:")
    print("-" * 30)
    
    # إنشاء مستخدمين مختلفين
    user1_id = db.create_user("user1", "pass1", full_name="المستخدم الأول")
    user2_id = db.create_user("user2", "pass2", full_name="المستخدم الثاني")
    
    if user1_id and user2_id:
        # حفظ مشاريع لكل مستخدم
        db.save_user_project(user1_id, "مشروع المستخدم الأول", '{"data": "user1_data"}')
        db.save_user_project(user2_id, "مشروع المستخدم الثاني", '{"data": "user2_data"}')
        
        # التحقق من الفصل
        user1_projects = db.get_user_projects(user1_id)
        user2_projects = db.get_user_projects(user2_id)
        
        print(f"مشاريع المستخدم الأول: {len(user1_projects)}")
        print(f"مشاريع المستخدم الثاني: {len(user2_projects)}")
        print("✅ كل مستخدم يرى مشاريعه فقط")

if __name__ == "__main__":
    # تشغيل المثال التوضيحي
    demonstrate_database_operations()
    
    # عرض هيكل قاعدة البيانات
    show_database_structure()
    
    # عرض مميزات الأمان
    demonstrate_security_features()
