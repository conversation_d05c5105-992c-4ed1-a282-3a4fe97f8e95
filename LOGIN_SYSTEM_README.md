# نظام تسجيل الدخول - نظام التصميم المعماري

## 🎯 نظرة عامة

تم إضافة نظام تسجيل دخول متكامل إلى تطبيق التصميم المعماري مع قاعدة بيانات SQL لإدارة المستخدمين ومشاريعهم.

## 🗂️ الملفات الجديدة

### 1. `database.py`
- **الوظيفة**: إدارة قاعدة البيانات SQLite
- **المميزات**:
  - إنشاء وإدارة جداول المستخدمين
  - تشفير كلمات المرور
  - حفظ مشاريع المستخدمين
  - إنشاء مستخدم افتراضي للاختبار

### 2. `login_page.py`
- **الوظيفة**: واجهة تسجيل الدخول الرسومية
- **المميزات**:
  - تسجيل دخول المستخدمين الموجودين
  - إنشاء حسابات جديدة
  - واجهة عربية جميلة
  - التحقق من صحة البيانات

### 3. `test_db.py`
- **الوظيفة**: اختبار قاعدة البيانات
- **الاستخدام**: `python test_db.py`

## 🚀 كيفية الاستخدام

### 1. تشغيل التطبيق
```bash
python main.py
```

### 2. تسجيل الدخول
- **المستخدم الافتراضي**:
  - اسم المستخدم: `admin`
  - كلمة المرور: `admin123`

### 3. إنشاء حساب جديد
- انقر على "حساب جديد"
- املأ البيانات المطلوبة
- انقر "تسجيل"

## 🗄️ هيكل قاعدة البيانات

### جدول المستخدمين (`users`)
```sql
- id: معرف المستخدم (مفتاح أساسي)
- username: اسم المستخدم (فريد)
- password_hash: كلمة المرور المشفرة
- email: البريد الإلكتروني
- full_name: الاسم الكامل
- phone: رقم الهاتف
- created_at: تاريخ الإنشاء
- last_login: آخر تسجيل دخول
- is_active: حالة النشاط
```

### جدول المشاريع (`user_projects`)
```sql
- id: معرف المشروع
- user_id: معرف المستخدم
- project_name: اسم المشروع
- project_data: بيانات المشروع (JSON)
- created_at: تاريخ الإنشاء
- updated_at: تاريخ التحديث
```

## 🔐 الأمان

### تشفير كلمات المرور
- استخدام SHA-256 لتشفير كلمات المرور
- عدم حفظ كلمات المرور بشكل واضح

### التحقق من البيانات
- التحقق من طول اسم المستخدم (3 أحرف على الأقل)
- التحقق من طول كلمة المرور (6 أحرف على الأقل)
- التحقق من تطابق كلمة المرور وتأكيدها

## 🎨 واجهة المستخدم

### صفحة تسجيل الدخول
- **تصميم عربي**: واجهة باللغة العربية
- **ألوان احترافية**: تدرجات زرقاء أنيقة
- **تفاعل سلس**: تأثيرات بصرية عند التمرير
- **رسائل واضحة**: تنبيهات للأخطاء والنجاح

### صفحة معلومات الزبون
- **معلومات المستخدم**: عرض بيانات المستخدم المسجل
- **ربط بقاعدة البيانات**: حفظ المشاريع مع معرف المستخدم

## 🔧 التطوير والاختبار

### اختبار قاعدة البيانات
```bash
python test_db.py
```

### اختبار صفحة تسجيل الدخول
```bash
python simple_login_test.py
```

## 📋 المتطلبات

### المكتبات المطلوبة
- `pygame`: للواجهة الرسومية
- `sqlite3`: لقاعدة البيانات (مدمجة في Python)
- `hashlib`: لتشفير كلمات المرور (مدمجة في Python)
- `tkinter`: لواجهة معلومات الزبون (مدمجة في Python)

## 🌟 المميزات الجديدة

### 1. إدارة المستخدمين
- تسجيل دخول آمن
- إنشاء حسابات جديدة
- حفظ بيانات المستخدمين

### 2. إدارة المشاريع
- ربط المشاريع بالمستخدمين
- حفظ تلقائي في قاعدة البيانات
- تتبع تاريخ الإنشاء والتحديث

### 3. واجهة محسنة
- تصميم عربي احترافي
- تفاعل سلس مع المستخدم
- رسائل واضحة ومفيدة

## 🔄 تدفق العمل

1. **بدء التطبيق** → صفحة تسجيل الدخول
2. **تسجيل الدخول** → صفحة معلومات الزبون
3. **إدخال بيانات الزبون** → التطبيق الرئيسي
4. **حفظ المشروع** → قاعدة البيانات + ملف محلي

## 🛠️ الصيانة

### نسخ احتياطية
- ملف قاعدة البيانات: `users.db`
- ملفات المشاريع: `customer_data.json`

### إضافة مستخدمين جدد
```python
from database import UserDatabase
db = UserDatabase()
db.create_user("username", "password", "email", "full_name", "phone")
```

## 📞 الدعم

للمساعدة أو الاستفسارات، يرجى مراجعة الكود أو تشغيل ملفات الاختبار للتأكد من عمل النظام بشكل صحيح.
