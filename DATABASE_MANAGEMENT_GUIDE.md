# 🗄️ دليل إدارة قاعدة البيانات - نظام التصميم المعماري

## 🎯 نظرة عامة

هذا الدليل يوضح كيفية التعامل مع قاعدة البيانات وإدارتها بطرق مختلفة في نظام التصميم المعماري.

## 🛠️ أدوات الإدارة المتاحة

### 1️⃣ **أداة سطر الأوامر** (`database_manager.py`)
- **الاستخدام**: `python database_manager.py`
- **المميزات**: إدارة شاملة عبر واجهة نصية تفاعلية
- **مناسبة لـ**: المطورين والمديرين المتقدمين

### 2️⃣ **الواجهة الرسومية** (`database_gui.py`)
- **الاستخدام**: `python database_gui.py`
- **المميزات**: واجهة رسومية سهلة الاستخدام
- **مناسبة لـ**: جميع المستخدمين

### 3️⃣ **البرمجة المباشرة** (Python Code)
- **الاستخدام**: استيراد `database.py` في الكود
- **المميزات**: تحكم كامل برمجياً
- **مناسبة لـ**: المطورين والتطبيقات المخصصة

## 🖥️ استخدام أداة سطر الأوامر

### **تشغيل الأداة**
```bash
python database_manager.py
```

### **القائمة الرئيسية**
```
🗄️  مدير قاعدة البيانات - نظام التصميم المعماري
════════════════════════════════════════════════════════════
1️⃣  عرض جميع المستخدمين
2️⃣  عرض تفاصيل مستخدم محدد
3️⃣  إنشاء مستخدم جديد
4️⃣  تحديث بيانات مستخدم
5️⃣  تغيير كلمة مرور مستخدم
6️⃣  تفعيل/إلغاء تفعيل مستخدم
7️⃣  حذف مستخدم
8️⃣  إنشاء نسخة احتياطية
9️⃣  عرض إحصائيات قاعدة البيانات
🔍  البحث عن مستخدم
🧹  تنظيف قاعدة البيانات
📤  تصدير البيانات
0️⃣  خروج
```

### **العمليات الأساسية**

#### **1. عرض جميع المستخدمين**
- يعرض جدول بجميع المستخدمين
- يتضمن: ID، اسم المستخدم، الاسم الكامل، البريد، الهاتف، الحالة

#### **2. إنشاء مستخدم جديد**
```
👤 اسم المستخدم: [أدخل اسم المستخدم]
🔒 كلمة المرور: [أدخل كلمة المرور]
👨‍💼 الاسم الكامل (اختياري): [أدخل الاسم]
📧 البريد الإلكتروني (اختياري): [أدخل البريد]
📱 رقم الهاتف (اختياري): [أدخل الهاتف]
```

#### **3. تحديث بيانات مستخدم**
- أدخل معرف المستخدم
- اتركه فارغاً للاحتفاظ بالقيمة الحالية
- أدخل القيم الجديدة للتحديث

#### **4. تغيير كلمة المرور**
- أدخل معرف المستخدم
- أدخل كلمة المرور الجديدة
- أكد كلمة المرور

#### **5. البحث عن المستخدمين**
- ابحث بـ: اسم المستخدم، الاسم الكامل، أو البريد الإلكتروني
- يعرض النتائج المطابقة

## 🖼️ استخدام الواجهة الرسومية

### **تشغيل الواجهة**
```bash
python database_gui.py
```

### **المميزات**
- **جدول تفاعلي**: عرض جميع المستخدمين في جدول
- **أزرار سهلة**: إضافة، تعديل، حذف، تحديث
- **نقر مزدوج**: للتعديل السريع
- **إحصائيات مباشرة**: عدد المستخدمين والمشاريع

### **العمليات**
1. **إضافة مستخدم**: انقر "➕ إضافة مستخدم"
2. **تعديل مستخدم**: حدد المستخدم ← انقر "✏️ تعديل"
3. **تغيير كلمة المرور**: حدد المستخدم ← انقر "🔒 تغيير كلمة المرور"
4. **حذف مستخدم**: حدد المستخدم ← انقر "❌ حذف"
5. **تحديث القائمة**: انقر "🔄 تحديث"

## 💻 البرمجة المباشرة

### **استيراد المكتبة**
```python
from database import UserDatabase

# إنشاء اتصال بقاعدة البيانات
db = UserDatabase()
```

### **العمليات الأساسية**

#### **1. إنشاء مستخدم جديد**
```python
user_id = db.create_user(
    username="new_user",
    password="password123",
    email="<EMAIL>",
    full_name="اسم المستخدم",
    phone="123456789"
)

if user_id:
    print(f"تم إنشاء المستخدم بمعرف: {user_id}")
else:
    print("فشل في إنشاء المستخدم")
```

#### **2. التحقق من تسجيل الدخول**
```python
user_data = db.verify_user("username", "password")

if user_data:
    print(f"مرحباً {user_data['full_name']}")
    print(f"معرف المستخدم: {user_data['id']}")
else:
    print("بيانات تسجيل الدخول غير صحيحة")
```

#### **3. حفظ مشروع للمستخدم**
```python
import json

project_data = {
    "customer_name": "اسم الزبون",
    "project_type": "فيلا",
    "rooms": 5
}

success = db.save_user_project(
    user_id=1,
    project_name="مشروع الفيلا",
    project_data=json.dumps(project_data, ensure_ascii=False)
)

if success:
    print("تم حفظ المشروع بنجاح")
```

#### **4. الحصول على مشاريع المستخدم**
```python
projects = db.get_user_projects(user_id=1)

for project in projects:
    print(f"المشروع: {project['name']}")
    print(f"تاريخ الإنشاء: {project['created_at']}")
```

### **استعلامات SQL مباشرة**
```python
import sqlite3

# الاتصال بقاعدة البيانات
conn = sqlite3.connect("users.db")
cursor = conn.cursor()

# استعلام مخصص
cursor.execute("SELECT * FROM users WHERE is_active = 1")
active_users = cursor.fetchall()

# إغلاق الاتصال
conn.close()
```

## 🔧 عمليات الصيانة

### **1. إنشاء نسخة احتياطية**
```bash
# عبر أداة سطر الأوامر
python database_manager.py
# اختر: 8️⃣ إنشاء نسخة احتياطية
```

### **2. تنظيف قاعدة البيانات**
```bash
# عبر أداة سطر الأوامر
python database_manager.py
# اختر: 🧹 تنظيف قاعدة البيانات
```

**خيارات التنظيف:**
- حذف الجلسات المنتهية الصلاحية
- حذف المستخدمين المعطلين
- حذف المشاريع الفارغة

### **3. تصدير البيانات**
```bash
# عبر أداة سطر الأوامر
python database_manager.py
# اختر: 📤 تصدير البيانات
```

**ينتج ملف JSON يحتوي على:**
- جميع بيانات المستخدمين (بدون كلمات المرور)
- جميع المشاريع
- تاريخ التصدير

## 📊 هيكل قاعدة البيانات

### **جدول المستخدمين (`users`)**
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    email TEXT,
    full_name TEXT,
    phone TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT 1
);
```

### **جدول المشاريع (`user_projects`)**
```sql
CREATE TABLE user_projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    project_name TEXT NOT NULL,
    project_data TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

### **جدول الجلسات (`user_sessions`)**
```sql
CREATE TABLE user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    session_token TEXT UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

## 🔐 الأمان والحماية

### **تشفير كلمات المرور**
- استخدام SHA-256 للتشفير
- عدم حفظ كلمات المرور بشكل واضح
- التحقق من قوة كلمة المرور

### **حماية قاعدة البيانات**
- نسخ احتياطية دورية
- التحقق من صحة البيانات المدخلة
- حماية من SQL Injection

### **إدارة الصلاحيات**
- تفعيل/إلغاء تفعيل المستخدمين
- تتبع آخر تسجيل دخول
- إدارة جلسات المستخدمين

## 🚨 استكشاف الأخطاء

### **مشاكل شائعة وحلولها**

#### **1. خطأ في فتح قاعدة البيانات**
```
خطأ: database is locked
الحل: تأكد من إغلاق جميع الاتصالات بقاعدة البيانات
```

#### **2. اسم المستخدم موجود بالفعل**
```
خطأ: UNIQUE constraint failed
الحل: استخدم اسم مستخدم مختلف
```

#### **3. كلمة مرور ضعيفة**
```
خطأ: Password too short
الحل: استخدم كلمة مرور من 6 أحرف على الأقل
```

### **إعادة تعيين قاعدة البيانات**
```python
import os
if os.path.exists("users.db"):
    os.remove("users.db")

# إعادة إنشاء قاعدة البيانات
from database import UserDatabase
db = UserDatabase()
```

## 📞 الدعم والمساعدة

### **ملفات المساعدة**
- `DATABASE_MANAGEMENT_GUIDE.md` - هذا الدليل
- `LOGIN_SYSTEM_README.md` - دليل نظام تسجيل الدخول
- `ENGLISH_LOGIN_GUIDE.md` - دليل الواجهة الإنجليزية

### **أدوات الاختبار**
- `test_db.py` - اختبار قاعدة البيانات
- `python database_manager.py` - أداة الإدارة الشاملة
- `python database_gui.py` - الواجهة الرسومية

---

**ملاحظة**: احرص على إنشاء نسخ احتياطية دورية من قاعدة البيانات قبل إجراء أي تعديلات كبيرة.
