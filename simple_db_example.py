#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔧 مثال بسيط لإدارة قاعدة البيانات
Simple Database Management Example
"""

from database import UserDatabase
import json

def main():
    print("🗄️ مثال بسيط لإدارة قاعدة البيانات")
    print("=" * 50)
    
    # إنشاء اتصال بقاعدة البيانات
    db = UserDatabase()
    
    # 1. عرض جميع المستخدمين
    print("\n1️⃣ عرض جميع المستخدمين:")
    print("-" * 30)
    
    import sqlite3
    conn = sqlite3.connect(db.db_path)
    cursor = conn.cursor()
    
    cursor.execute('SELECT id, username, full_name, email, is_active FROM users')
    users = cursor.fetchall()
    
    if users:
        print(f"{'ID':<4} {'Username':<15} {'Full Name':<20} {'Email':<25} {'Active'}")
        print("-" * 70)
        for user in users:
            active = "✅" if user[4] else "❌"
            print(f"{user[0]:<4} {user[1]:<15} {user[2] or 'N/A':<20} {user[3] or 'N/A':<25} {active}")
    else:
        print("❌ لا توجد مستخدمين")
    
    # 2. إنشاء مستخدم جديد
    print("\n2️⃣ إنشاء مستخدم جديد:")
    print("-" * 30)
    
    new_user_id = db.create_user(
        username="test_user_" + str(len(users) + 1),
        password="password123",
        email="<EMAIL>",
        full_name="مستخدم تجريبي",
        phone="123456789"
    )
    
    if new_user_id:
        print(f"✅ تم إنشاء مستخدم جديد بمعرف: {new_user_id}")
    else:
        print("⚠️ المستخدم موجود بالفعل أو حدث خطأ")
    
    # 3. التحقق من تسجيل الدخول
    print("\n3️⃣ اختبار تسجيل الدخول:")
    print("-" * 30)
    
    # اختبار المستخدم الافتراضي
    admin_user = db.verify_user("admin", "admin123")
    if admin_user:
        print(f"✅ تسجيل دخول ناجح للمدير:")
        print(f"   الاسم: {admin_user['full_name']}")
        print(f"   البريد: {admin_user['email']}")
    else:
        print("❌ فشل في تسجيل دخول المدير")
    
    # 4. حفظ مشروع تجريبي
    print("\n4️⃣ حفظ مشروع تجريبي:")
    print("-" * 30)
    
    if admin_user:
        project_data = {
            "customer_name": "زبون تجريبي",
            "project_type": "فيلا سكنية",
            "rooms": 4,
            "area": "300 متر مربع",
            "created_by": admin_user['username']
        }
        
        success = db.save_user_project(
            user_id=admin_user['id'],
            project_name="مشروع تجريبي " + str(len(users) + 1),
            project_data=json.dumps(project_data, ensure_ascii=False)
        )
        
        if success:
            print("✅ تم حفظ المشروع التجريبي بنجاح")
        else:
            print("❌ فشل في حفظ المشروع")
    
    # 5. عرض مشاريع المستخدم
    print("\n5️⃣ مشاريع المدير:")
    print("-" * 30)
    
    if admin_user:
        projects = db.get_user_projects(admin_user['id'])
        
        if projects:
            for i, project in enumerate(projects, 1):
                print(f"{i}. {project['name']}")
                print(f"   تاريخ الإنشاء: {project['created_at']}")
                print(f"   آخر تحديث: {project['updated_at']}")
        else:
            print("❌ لا توجد مشاريع للمدير")
    
    # 6. إحصائيات قاعدة البيانات
    print("\n6️⃣ إحصائيات قاعدة البيانات:")
    print("-" * 30)
    
    cursor.execute('SELECT COUNT(*) FROM users')
    total_users = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 1')
    active_users = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM user_projects')
    total_projects = cursor.fetchone()[0]
    
    print(f"👥 إجمالي المستخدمين: {total_users}")
    print(f"✅ المستخدمين النشطين: {active_users}")
    print(f"❌ المستخدمين المعطلين: {total_users - active_users}")
    print(f"📁 إجمالي المشاريع: {total_projects}")
    
    # 7. مثال على تحديث بيانات المستخدم
    print("\n7️⃣ تحديث بيانات المستخدم:")
    print("-" * 30)
    
    if new_user_id:
        cursor.execute('''
            UPDATE users 
            SET full_name = ?, email = ? 
            WHERE id = ?
        ''', ("مستخدم محدث", "<EMAIL>", new_user_id))
        
        conn.commit()
        print(f"✅ تم تحديث بيانات المستخدم {new_user_id}")
    
    # 8. مثال على البحث
    print("\n8️⃣ البحث في المستخدمين:")
    print("-" * 30)
    
    search_term = "admin"
    cursor.execute('''
        SELECT id, username, full_name, email 
        FROM users 
        WHERE username LIKE ? OR full_name LIKE ?
    ''', (f'%{search_term}%', f'%{search_term}%'))
    
    search_results = cursor.fetchall()
    
    if search_results:
        print(f"🔍 نتائج البحث عن '{search_term}':")
        for result in search_results:
            print(f"   {result[1]} - {result[2] or 'N/A'}")
    else:
        print(f"❌ لم يتم العثور على نتائج للبحث عن '{search_term}'")
    
    conn.close()
    
    print("\n" + "=" * 50)
    print("🎉 انتهى المثال بنجاح!")
    print("\nلإدارة قاعدة البيانات بشكل تفاعلي:")
    print("  python database_manager.py  (واجهة نصية)")
    print("  python database_gui.py      (واجهة رسومية)")

if __name__ == "__main__":
    main()
