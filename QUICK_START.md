# 🚀 Quick Start Guide - Architectural Design System

## 📋 How to Run the Application

### Method 1: Normal Run
```bash
python main.py
```

### Method 2: Run with Requirements Check
```bash
python run_with_login.py
```

## 🔐 Login

### Default User
- **Username**: `admin`
- **Password**: `admin123`

### Create New Account
1. Click "Sign Up"
2. Fill in the data:
   - Full Name
   - Username (at least 3 characters)
   - Email (optional)
   - Phone (optional)
   - Password (at least 6 characters)
   - Confirm Password
3. Click "Register"

## 🎮 Login Page Controls

### Keyboard
- **Tab**: Navigate between fields
- **Enter**: Login or Register
- **Backspace**: Delete characters

### Mouse
- **Click**: Select fields
- **Click buttons**: Execute actions

## 📁 الملفات المهمة

### قاعدة البيانات
- `users.db`: ملف قاعدة البيانات الرئيسي

### بيانات المشاريع
- `customer_data.json`: بيانات الزبون الحالي
- `design_data.json`: بيانات التصميم

### ملفات الاختبار
- `test_db.py`: اختبار قاعدة البيانات
- `simple_login_test.py`: اختبار صفحة تسجيل الدخول

## 🔧 حل المشاكل الشائعة

### مشكلة: لا تظهر صفحة تسجيل الدخول
**الحل**: تأكد من تثبيت pygame
```bash
pip install pygame
```

### مشكلة: خطأ في قاعدة البيانات
**الحل**: احذف ملف `users.db` وأعد تشغيل التطبيق

### مشكلة: نسيان كلمة المرور
**الحل**: استخدم المستخدم الافتراضي `admin / admin123`

## 🌟 المميزات الجديدة

### ✅ نظام تسجيل دخول آمن
- تشفير كلمات المرور
- التحقق من صحة البيانات
- حفظ جلسات المستخدمين

### ✅ قاعدة بيانات متقدمة
- حفظ بيانات المستخدمين
- ربط المشاريع بالمستخدمين
- تتبع تاريخ الإنشاء والتحديث

### ✅ واجهة عربية جميلة
- تصميم احترافي
- ألوان متناسقة
- تفاعل سلس

## 📞 الدعم

### اختبار النظام
```bash
python test_db.py
```

### عرض معلومات قاعدة البيانات
```python
from database import UserDatabase
db = UserDatabase()
# استخدم دوال قاعدة البيانات
```

## 🎯 الخطوات التالية

1. **تسجيل الدخول** باستخدام `admin / admin123`
2. **إدخال بيانات الزبون** في الصفحة التالية
3. **بدء التصميم** في التطبيق الرئيسي
4. **حفظ المشروع** سيتم تلقائياً في قاعدة البيانات

---

**ملاحظة**: جميع البيانات محفوظة بشكل آمن في قاعدة البيانات المحلية.
