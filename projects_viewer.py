#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
📁 Projects Viewer - عارض المشاريع
View and manage user projects
"""

import pygame
import sys
import sqlite3
import json
from datetime import datetime
from database import UserDatabase

# إعدادات الشاشة
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
BACKGROUND_COLOR = (245, 250, 255)
PRIMARY_COLOR = (70, 130, 180)
SECONDARY_COLOR = (255, 255, 255)
TEXT_COLOR = (25, 25, 112)
ACCENT_COLOR = (100, 149, 237)
SUCCESS_COLOR = (34, 139, 34)
WARNING_COLOR = (255, 165, 0)
ERROR_COLOR = (220, 20, 60)

class ProjectsViewer:
    def __init__(self, user_data):
        """تهيئة عارض المشاريع"""
        pygame.init()
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption(f"My Projects - {user_data['full_name']}")
        self.clock = pygame.time.Clock()

        # بيانات المستخدم
        self.user_data = user_data

        # تهيئة قاعدة البيانات
        self.db = UserDatabase()

        # تهيئة الخطوط
        self.title_font = pygame.font.Font(None, 36)
        self.header_font = pygame.font.Font(None, 28)
        self.text_font = pygame.font.Font(None, 24)
        self.small_font = pygame.font.Font(None, 20)
        self.button_font = pygame.font.Font(None, 22)

        # متغيرات الواجهة
        self.projects = []
        self.selected_project = None
        self.scroll_offset = 0
        self.message = ""
        self.message_color = TEXT_COLOR
        self.message_timer = 0

        # الأزرار
        self.buttons = {}
        self.create_buttons()

        # تحميل المشاريع
        self.load_projects()

        # حالة التطبيق
        self.running = True
        self.next_action = None

    def create_buttons(self):
        """إنشاء الأزرار"""
        button_width = 120
        button_height = 40
        start_x = 50
        start_y = 100
        spacing = 20

        buttons_data = [
            ("new_project", "New Project", start_x, start_y, SUCCESS_COLOR),
            ("open_project", "Open Project", start_x + (button_width + spacing), start_y, PRIMARY_COLOR),
            ("delete_project", "Delete Project", start_x + 2 * (button_width + spacing), start_y, ERROR_COLOR),
            ("refresh", "Refresh", start_x + 3 * (button_width + spacing), start_y, ACCENT_COLOR),
            ("back", "Back to Dashboard", SCREEN_WIDTH - 300, start_y, TEXT_COLOR),
            ("logout", "Logout", SCREEN_WIDTH - 120, start_y, ERROR_COLOR)
        ]

        for button_id, text, x, y, color in buttons_data:
            if button_id == "back":
                width = 160
            elif button_id == "logout":
                width = 100
            else:
                width = button_width

            self.buttons[button_id] = {
                'rect': pygame.Rect(x, y, width, button_height),
                'text': text,
                'color': color,
                'hover': False
            }

    def load_projects(self):
        """تحميل مشاريع المستخدم"""
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, project_name, project_data, created_at, updated_at
                FROM user_projects
                WHERE user_id = ?
                ORDER BY updated_at DESC
            ''', (self.user_data['id'],))

            self.projects = cursor.fetchall()
            conn.close()

        except Exception as e:
            self.show_message(f"Error loading projects: {e}", ERROR_COLOR)

    def draw_background(self):
        """رسم الخلفية"""
        self.screen.fill(BACKGROUND_COLOR)

        # رسم العنوان الرئيسي
        title_text = f"My Projects - {self.user_data['full_name']}"
        title_surface = self.title_font.render(title_text, True, PRIMARY_COLOR)
        self.screen.blit(title_surface, (50, 30))

        # رسم خط فاصل
        pygame.draw.line(self.screen, PRIMARY_COLOR, (50, 70), (SCREEN_WIDTH - 50, 70), 2)

        # معلومات المشاريع
        projects_info = f"Total Projects: {len(self.projects)}"
        info_surface = self.text_font.render(projects_info, True, TEXT_COLOR)
        self.screen.blit(info_surface, (SCREEN_WIDTH - 200, 30))

    def draw_buttons(self):
        """رسم الأزرار"""
        mouse_pos = pygame.mouse.get_pos()

        for button_id, button in self.buttons.items():
            # تحديث حالة التمرير
            button['hover'] = button['rect'].collidepoint(mouse_pos)

            # تحديد لون الزر
            if button['hover']:
                color = tuple(min(255, c + 30) for c in button['color'])
            else:
                color = button['color']

            # رسم الزر
            pygame.draw.rect(self.screen, color, button['rect'], border_radius=8)
            pygame.draw.rect(self.screen, TEXT_COLOR, button['rect'], 2, border_radius=8)

            # رسم النص
            text_surface = self.button_font.render(button['text'], True, SECONDARY_COLOR)
            text_rect = text_surface.get_rect(center=button['rect'].center)
            self.screen.blit(text_surface, text_rect)

    def draw_projects_list(self):
        """رسم قائمة المشاريع"""
        list_x = 50
        list_y = 160
        list_width = SCREEN_WIDTH - 100
        list_height = SCREEN_HEIGHT - list_y - 100

        # رسم إطار القائمة
        list_rect = pygame.Rect(list_x, list_y, list_width, list_height)
        pygame.draw.rect(self.screen, SECONDARY_COLOR, list_rect)
        pygame.draw.rect(self.screen, PRIMARY_COLOR, list_rect, 2)

        if not self.projects:
            # لا توجد مشاريع
            no_projects_text = "No projects found. Create your first project!"
            no_projects_surface = self.header_font.render(no_projects_text, True, TEXT_COLOR)
            no_projects_rect = no_projects_surface.get_rect(center=(list_x + list_width // 2, list_y + list_height // 2))
            self.screen.blit(no_projects_surface, no_projects_rect)
            return

        # رؤوس الأعمدة
        headers = ["Project Name", "Created", "Last Updated", "Size"]
        header_y = list_y + 10
        col_widths = [300, 150, 150, 100]
        col_x = list_x + 20

        for i, header in enumerate(headers):
            header_surface = self.text_font.render(header, True, PRIMARY_COLOR)
            self.screen.blit(header_surface, (col_x, header_y))
            col_x += col_widths[i]

        # خط فاصل تحت الرؤوس
        pygame.draw.line(self.screen, PRIMARY_COLOR,
                        (list_x + 10, header_y + 25),
                        (list_x + list_width - 10, header_y + 25), 1)

        # عرض المشاريع
        start_y = header_y + 35
        row_height = 40
        visible_rows = (list_height - 60) // row_height

        for i, project in enumerate(self.projects[self.scroll_offset:self.scroll_offset + visible_rows]):
            row_y = start_y + i * row_height

            # تحديد لون الصف
            if project == self.selected_project:
                row_color = (200, 220, 255)
                pygame.draw.rect(self.screen, row_color,
                               (list_x + 5, row_y - 5, list_width - 10, row_height))
            elif i % 2 == 0:
                row_color = (248, 250, 252)
                pygame.draw.rect(self.screen, row_color,
                               (list_x + 5, row_y - 5, list_width - 10, row_height))

            # بيانات المشروع
            project_name = project[1][:35] + "..." if len(project[1]) > 35 else project[1]
            created_date = project[3][:10] if project[3] else "Unknown"
            updated_date = project[4][:10] if project[4] else "Unknown"

            # حساب حجم البيانات
            try:
                data_size = len(project[2]) if project[2] else 0
                if data_size > 1024:
                    size_text = f"{data_size // 1024}KB"
                else:
                    size_text = f"{data_size}B"
            except:
                size_text = "Unknown"

            project_data = [project_name, created_date, updated_date, size_text]

            col_x = list_x + 20
            for j, data in enumerate(project_data):
                text_surface = self.text_font.render(str(data), True, TEXT_COLOR)
                self.screen.blit(text_surface, (col_x, row_y))
                col_x += col_widths[j]

    def draw_project_details(self):
        """رسم تفاصيل المشروع المحدد"""
        if not self.selected_project:
            return

        # إطار التفاصيل
        details_x = 50
        details_y = SCREEN_HEIGHT - 80
        details_width = SCREEN_WIDTH - 100
        details_height = 60

        details_rect = pygame.Rect(details_x, details_y, details_width, details_height)
        pygame.draw.rect(self.screen, (240, 248, 255), details_rect)
        pygame.draw.rect(self.screen, ACCENT_COLOR, details_rect, 2)

        # معلومات المشروع
        project_info = f"Selected: {self.selected_project[1]} | Created: {self.selected_project[3]} | Updated: {self.selected_project[4]}"
        info_surface = self.text_font.render(project_info, True, TEXT_COLOR)
        self.screen.blit(info_surface, (details_x + 10, details_y + 10))

        # تفاصيل إضافية
        try:
            if self.selected_project[2]:
                project_data = json.loads(self.selected_project[2])
                customer_name = project_data.get('customer', 'Unknown')
                additional_info = f"Customer: {customer_name}"
                additional_surface = self.small_font.render(additional_info, True, ACCENT_COLOR)
                self.screen.blit(additional_surface, (details_x + 10, details_y + 35))
        except:
            pass

    def draw_message(self):
        """رسم الرسائل"""
        if self.message and self.message_timer > 0:
            message_surface = self.text_font.render(self.message, True, self.message_color)
            message_rect = message_surface.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 30))

            # رسم خلفية للرسالة
            bg_rect = message_rect.inflate(20, 10)
            pygame.draw.rect(self.screen, SECONDARY_COLOR, bg_rect, border_radius=5)
            pygame.draw.rect(self.screen, self.message_color, bg_rect, 2, border_radius=5)

            self.screen.blit(message_surface, message_rect)
            self.message_timer -= 1

    def show_message(self, message, color):
        """عرض رسالة"""
        self.message = message
        self.message_color = color
        self.message_timer = 180  # 3 ثوان بـ 60 FPS

    def handle_click(self, pos):
        """معالجة النقر"""
        # فحص النقر على الأزرار
        for button_id, button in self.buttons.items():
            if button['rect'].collidepoint(pos):
                self.handle_button_click(button_id)
                return

        # فحص النقر على قائمة المشاريع
        list_x = 50
        list_y = 195  # بعد الرؤوس
        row_height = 40

        if pos[0] >= list_x and pos[1] >= list_y and self.projects:
            row_index = (pos[1] - list_y) // row_height
            if 0 <= row_index < len(self.projects[self.scroll_offset:]):
                self.selected_project = self.projects[self.scroll_offset + row_index]

    def handle_button_click(self, button_id):
        """معالجة النقر على الأزرار"""
        if button_id == "new_project":
            self.next_action = "new_project"
            self.running = False
        elif button_id == "open_project":
            if self.selected_project:
                self.next_action = "open_project"
                self.running = False
            else:
                self.show_message("Please select a project first", ERROR_COLOR)
        elif button_id == "delete_project":
            if self.selected_project:
                self.delete_project()
            else:
                self.show_message("Please select a project first", ERROR_COLOR)
        elif button_id == "refresh":
            self.load_projects()
            self.show_message("Projects list refreshed", SUCCESS_COLOR)
        elif button_id == "back":
            self.next_action = "dashboard"
            self.running = False
        elif button_id == "logout":
            self.next_action = "logout"
            self.running = False

    def delete_project(self):
        """حذف المشروع المحدد"""
        if not self.selected_project:
            return

        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()

            cursor.execute('DELETE FROM user_projects WHERE id = ?', (self.selected_project[0],))
            conn.commit()
            conn.close()

            self.show_message(f"Project '{self.selected_project[1]}' deleted successfully", SUCCESS_COLOR)
            self.load_projects()
            self.selected_project = None

        except Exception as e:
            self.show_message(f"Error deleting project: {e}", ERROR_COLOR)

    def handle_scroll(self, direction):
        """معالجة التمرير"""
        if direction > 0 and self.scroll_offset > 0:
            self.scroll_offset -= 1
        elif direction < 0 and self.scroll_offset < len(self.projects) - 10:
            self.scroll_offset += 1

    def run(self):
        """تشغيل عارض المشاريع"""
        while self.running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.next_action = "quit"
                    self.running = False

                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # النقر بالزر الأيسر
                        self.handle_click(event.pos)
                    elif event.button == 4:  # عجلة الماوس للأعلى
                        self.handle_scroll(1)
                    elif event.button == 5:  # عجلة الماوس للأسفل
                        self.handle_scroll(-1)

                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        self.next_action = "dashboard"
                        self.running = False
                    elif event.key == pygame.K_F5:
                        self.load_projects()
                        self.show_message("Projects list refreshed", SUCCESS_COLOR)
                    elif event.key == pygame.K_DELETE:
                        if self.selected_project:
                            self.delete_project()

            # رسم الشاشة
            self.draw_background()
            self.draw_buttons()
            self.draw_projects_list()
            self.draw_project_details()
            self.draw_message()

            pygame.display.flip()
            self.clock.tick(60)

        pygame.quit()
        return self.next_action, self.selected_project

def show_projects_viewer(user_data):
    """عرض عارض المشاريع"""
    viewer = ProjectsViewer(user_data)
    return viewer.run()

if __name__ == "__main__":
    # اختبار عارض المشاريع
    from database import UserDatabase

    db = UserDatabase()
    user_data = db.verify_user("admin", "admin123")

    if user_data:
        action, selected_project = show_projects_viewer(user_data)
        print(f"الإجراء المحدد: {action}")
        if selected_project:
            print(f"المشروع المحدد: {selected_project[1]}")
    else:
        print("فشل في تسجيل الدخول")
