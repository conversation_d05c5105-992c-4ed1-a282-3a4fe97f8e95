# 🚀 دليل سريع لإدارة قاعدة البيانات

## 🎯 الطرق المتاحة لإدارة قاعدة البيانات

### 1️⃣ **الطريقة السهلة - الواجهة الرسومية**
```bash
python database_gui.py
```
**المميزات:**
- ✅ واجهة رسومية سهلة
- ✅ جدول تفاعلي للمستخدمين
- ✅ أزرار بسيطة للعمليات
- ✅ مناسبة للمبتدئين

### 2️⃣ **الطريقة المتقدمة - سطر الأوامر**
```bash
python database_manager.py
```
**المميزات:**
- ✅ إدارة شاملة ومتقدمة
- ✅ عمليات تنظيف وصيانة
- ✅ تصدير واستيراد البيانات
- ✅ مناسبة للمطورين

### 3️⃣ **الطريقة التعليمية - المثال البسيط**
```bash
python simple_db_example.py
```
**المميزات:**
- ✅ عرض توضيحي للعمليات
- ✅ أمثلة عملية
- ✅ مناسبة للتعلم

## 🔧 العمليات الأساسية

### **إضافة مستخدم جديد**
```python
from database import UserDatabase

db = UserDatabase()
user_id = db.create_user(
    username="اسم_المستخدم",
    password="كلمة_المرور",
    full_name="الاسم الكامل",
    email="البريد@example.com",
    phone="رقم الهاتف"
)
```

### **تسجيل الدخول**
```python
user_data = db.verify_user("اسم_المستخدم", "كلمة_المرور")
if user_data:
    print(f"مرحباً {user_data['full_name']}")
```

### **حفظ مشروع**
```python
import json

project_data = {"اسم_الزبون": "أحمد", "نوع_المشروع": "فيلا"}
db.save_user_project(
    user_id=1,
    project_name="مشروع أحمد",
    project_data=json.dumps(project_data, ensure_ascii=False)
)
```

### **عرض المستخدمين**
```python
import sqlite3

conn = sqlite3.connect("users.db")
cursor = conn.cursor()
cursor.execute("SELECT * FROM users")
users = cursor.fetchall()
conn.close()
```

## 📊 معلومات قاعدة البيانات

### **ملف قاعدة البيانات**
- **الاسم**: `users.db`
- **النوع**: SQLite
- **الموقع**: نفس مجلد التطبيق

### **الجداول الرئيسية**
1. **users** - بيانات المستخدمين
2. **user_projects** - مشاريع المستخدمين
3. **user_sessions** - جلسات تسجيل الدخول

### **المستخدم الافتراضي**
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## 🛠️ عمليات الصيانة

### **إنشاء نسخة احتياطية**
```bash
# عبر أداة سطر الأوامر
python database_manager.py
# اختر: 8️⃣ إنشاء نسخة احتياطية
```

### **تنظيف قاعدة البيانات**
```bash
# عبر أداة سطر الأوامر
python database_manager.py
# اختر: 🧹 تنظيف قاعدة البيانات
```

### **تصدير البيانات**
```bash
# عبر أداة سطر الأوامر
python database_manager.py
# اختر: 📤 تصدير البيانات
```

## 🔍 البحث والاستعلام

### **البحث عن مستخدم**
```python
import sqlite3

conn = sqlite3.connect("users.db")
cursor = conn.cursor()

# البحث بالاسم
cursor.execute("SELECT * FROM users WHERE username LIKE ?", ("%admin%",))
results = cursor.fetchall()

conn.close()
```

### **عرض مشاريع مستخدم**
```python
projects = db.get_user_projects(user_id=1)
for project in projects:
    print(f"المشروع: {project['name']}")
```

## ⚠️ نصائح مهمة

### **الأمان**
- ✅ احرص على إنشاء نسخ احتياطية دورية
- ✅ لا تشارك ملف قاعدة البيانات مع الآخرين
- ✅ استخدم كلمات مرور قوية

### **الأداء**
- ✅ أغلق اتصالات قاعدة البيانات بعد الانتهاء
- ✅ نظف قاعدة البيانات دورياً
- ✅ احذف البيانات غير المستخدمة

### **استكشاف الأخطاء**
- ❌ **خطأ**: `database is locked`
  - **الحل**: أغلق جميع الاتصالات المفتوحة
- ❌ **خطأ**: `UNIQUE constraint failed`
  - **الحل**: استخدم اسم مستخدم مختلف
- ❌ **خطأ**: `no such table`
  - **الحل**: أعد إنشاء قاعدة البيانات

## 📞 المساعدة والدعم

### **الملفات المرجعية**
- `DATABASE_MANAGEMENT_GUIDE.md` - الدليل الشامل
- `simple_db_example.py` - أمثلة عملية
- `test_db.py` - اختبار قاعدة البيانات

### **أدوات الإدارة**
- `database_manager.py` - أداة سطر الأوامر
- `database_gui.py` - الواجهة الرسومية
- `database.py` - مكتبة قاعدة البيانات

## 🎯 خطوات سريعة للبدء

### **1. اختبار النظام**
```bash
python simple_db_example.py
```

### **2. إدارة المستخدمين**
```bash
python database_gui.py
```

### **3. عمليات متقدمة**
```bash
python database_manager.py
```

---

**ملاحظة**: جميع الأدوات تعمل مع نفس ملف قاعدة البيانات `users.db` ويمكن استخدامها بشكل متبادل.
