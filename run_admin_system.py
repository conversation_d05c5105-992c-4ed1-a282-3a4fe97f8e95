#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 تشغيل نظام إدارة المستخدمين المتقدم
Advanced User Management System Launcher
"""

import sys
import os

def print_banner():
    """عرض شعار النظام"""
    print("=" * 80)
    print("📊 نظام Dashboard المتقدم مع إدارة المستخدمين")
    print("   Advanced Dashboard System with User Management")
    print("=" * 80)
    print("👑 المدير الرئيسي: MohammedBushiha")
    print("📊 Dashboard احترافي مع إحصائيات شاملة")
    print("📁 عارض مشاريع متقدم مع إدارة كاملة")
    print("⚙️ إعدادات نظام متكاملة للمدير الرئيسي")
    print("🎨 واجهة رسومية عصرية وجذابة")
    print("=" * 80)

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")

    try:
        import pygame
        print("✅ pygame متوفر")
    except ImportError:
        print("❌ pygame غير متوفر. يرجى تثبيته: pip install pygame")
        return False

    try:
        import sqlite3
        print("✅ sqlite3 متوفر")
    except ImportError:
        print("❌ sqlite3 غير متوفر")
        return False

    try:
        import tkinter
        print("✅ tkinter متوفر")
    except ImportError:
        print("❌ tkinter غير متوفر")
        return False

    return True

def setup_system():
    """إعداد النظام"""
    print("\n🛠️ إعداد النظام...")

    try:
        from setup_super_admin import setup_super_admin, add_user_role_column

        # إضافة عمود الصلاحيات إذا لم يكن موجوداً
        add_user_role_column()

        # إعداد المدير الرئيسي
        setup_super_admin()

        return True

    except Exception as e:
        print(f"❌ خطأ في إعداد النظام: {e}")
        return False

def test_system():
    """اختبار النظام"""
    print("\n🧪 اختبار النظام...")

    try:
        from database import UserDatabase

        db = UserDatabase()

        # اختبار المدير الرئيسي
        admin_user = db.verify_user("MohammedBushiha", "Mfb112002*")

        if admin_user and admin_user['user_role'] == 'super_admin':
            print("✅ المدير الرئيسي جاهز")
            return True
        else:
            print("❌ المدير الرئيسي غير جاهز")
            return False

    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return False

def show_login_info():
    """عرض معلومات تسجيل الدخول"""
    print("\n" + "=" * 50)
    print("🔐 معلومات تسجيل الدخول")
    print("=" * 50)
    print("👑 المدير الرئيسي:")
    print("   اسم المستخدم: MohammedBushiha")
    print("   كلمة المرور: Mfb112002*")
    print("   الصلاحيات: إدارة كاملة للنظام")
    print()
    print("👤 المستخدمين العاديين:")
    print("   يمكن للمدير الرئيسي إنشاؤهم من لوحة الإدارة")
    print("   لا يمكن التسجيل الذاتي")
    print("=" * 50)

def show_features():
    """عرض مميزات النظام"""
    print("\n🌟 مميزات النظام الجديد:")
    print("-" * 40)
    print("📊 Dashboard احترافي مع إحصائيات شاملة")
    print("📁 عارض مشاريع متقدم مع إدارة كاملة")
    print("⚙️ إعدادات نظام متكاملة للمدير الرئيسي")
    print("🎨 واجهة رسومية عصرية وجذابة")
    print("🔐 نظام صلاحيات محكم ومؤمن")
    print("👥 إدارة شاملة للمستخدمين")
    print("🛡️ أمان متقدم مع تشفير كلمات المرور")
    print("🚫 منع التسجيل العشوائي")
    print("🔒 حماية حساب المدير الرئيسي")
    print("💾 نسخ احتياطية وتنظيف تلقائي")
    print("📈 مراقبة النظام المستمرة")
    print("🎮 تحكم سهل بلوحة المفاتيح والماوس")

def run_system():
    """تشغيل النظام"""
    print("\n🚀 تشغيل النظام...")

    try:
        from main import main

        print("📋 فتح صفحة تسجيل الدخول...")
        print("🎯 النظام جاهز!")

        # تشغيل النظام الرئيسي
        main()

    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("\nللمساعدة:")
        print("  - تأكد من وجود جميع الملفات المطلوبة")
        print("  - راجع ملف ADMIN_SYSTEM_GUIDE.md")
        print("  - شغل python test_admin_system.py للاختبار")

def main():
    """الدالة الرئيسية"""
    print_banner()

    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ فشل في فحص المتطلبات")
        input("اضغط Enter للخروج...")
        return

    # إعداد النظام
    if not setup_system():
        print("\n❌ فشل في إعداد النظام")
        input("اضغط Enter للخروج...")
        return

    # اختبار النظام
    if not test_system():
        print("\n❌ فشل في اختبار النظام")
        input("اضغط Enter للخروج...")
        return

    # عرض المعلومات
    show_features()
    show_login_info()

    # تأكيد التشغيل
    print("\n" + "=" * 70)
    response = input("🎯 هل تريد تشغيل النظام؟ (Enter للمتابعة، 'q' للخروج): ").strip().lower()

    if response == 'q':
        print("👋 شكراً لاستخدام نظام إدارة المستخدمين!")
        return

    # تشغيل النظام
    run_system()

    print("\n🏁 انتهى تشغيل النظام")
    print("شكراً لاستخدام نظام Dashboard المتقدم مع إدارة المستخدمين!")

if __name__ == "__main__":
    main()
